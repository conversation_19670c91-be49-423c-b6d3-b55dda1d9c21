import { ReactNode, useState, useRef, useEffect } from 'react';
import Taskbar from '../ui/taskbar';
import StartMenu from '../ui/start-menu';
import { useAuth } from '@/context/AuthContext';
import { useOnboarding } from '@/context/OnboardingContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScrollDirection } from '@/hooks/useScrollDirection';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import OnboardingModal from '../onboarding/OnboardingModal';

interface MainLayoutProps {
  children: ReactNode;
  fullWidth?: boolean;
  noPadding?: boolean; // Added to support PageContainer
  hideOnboardingModal?: boolean; // New prop to optionally hide the modal
  showTaskbar?: boolean; // New prop to control taskbar visibility
  isStartMenuOpen?: boolean;
  setIsStartMenuOpen?: (open: boolean) => void;
}

/**
 * MainLayout - Main application layout with taskbar
 * Provides the Windows-like experience with taskbar and start menu
 * Updated to work with popup-based onboarding instead of the banner
 */
const MainLayout = ({ 
  children, 
  fullWidth = false,
  noPadding = false,
  hideOnboardingModal = false,
  showTaskbar = true, // Default to true for backward compatibility
  isStartMenuOpen: externalIsStartMenuOpen,
  setIsStartMenuOpen: externalSetIsStartMenuOpen
}: MainLayoutProps) => {
  const { user, loading: authLoading } = useAuth();
  const { currentStep, isOnboardingComplete, isOnboardingInitialized } = useOnboarding();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const location = useLocation();
  const [internalIsStartMenuOpen, internalSetIsStartMenuOpen] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const taskbarRef = useRef<HTMLDivElement>(null);

  // Use scroll direction hook to control taskbar visibility
  const { shouldShowTaskbar } = useScrollDirection(10);

  // Use external state if provided, otherwise internal
  const isStartMenuOpen = externalIsStartMenuOpen !== undefined ? externalIsStartMenuOpen : internalIsStartMenuOpen;
  const setIsStartMenuOpen = externalSetIsStartMenuOpen !== undefined ? externalSetIsStartMenuOpen : internalSetIsStartMenuOpen;

  // Store current location in sessionStorage when navigation happens
  useEffect(() => {
    if (typeof window !== 'undefined' && location.pathname !== '/auth') {
      sessionStorage.setItem('lastPath', location.pathname + location.search);
    }
  }, [location]);

  // Redirect to login if no user is found and we're not on a public path
  useEffect(() => {
    const publicPaths = ['/login', '/signup', '/auth', '/reset-password', '/verify-email', '/'];
    const isPublicPath = publicPaths.some(path => location.pathname.startsWith(path));
    
    // Only redirect if auth is loaded (not still checking) and there's no user
    if (!authLoading && !user && !isPublicPath) {
      // Check if onboarding parameter is in URL
      const searchParams = new URLSearchParams(location.search);
      const isOnboarding = searchParams.get('onboarding') === 'true';
      
      if (isOnboarding) {
        // If we're in onboarding but have no user, redirect to login
        navigate('/login?redirect=/home');
      }
    }
  }, [authLoading, user, location, navigate]);

  // Restore previous location when the component mounts
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleTabFocus = () => {
      // Keep track of current pathname to compare with stored path
      const currentPath = location.pathname + location.search;
      const storedPath = sessionStorage.getItem('lastPath');

      // Only navigate if there's a significant difference and not on auth page
      if (storedPath && currentPath !== storedPath && !currentPath.includes('/auth')) {
        // In some browsers, focus events can fire multiple times
        // Use requestAnimationFrame to ensure we only navigate once per render cycle
        requestAnimationFrame(() => {
          navigate(storedPath, { replace: true });
        });
      }
    };

    // Add event listener for when the tab gets focus
    window.addEventListener('focus', handleTabFocus);

    return () => {
      window.removeEventListener('focus', handleTabFocus);
    };
  }, [navigate, location.pathname, location.search]);

  // Close start menu when clicking outside
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const handleClickOutside = (event: MouseEvent) => {
      // Don't respond to click events on elements that might be navigation items
      const target = event.target as HTMLElement;
      const isNavigationElement = target.tagName === 'BUTTON' ||
                                target.tagName === 'A' ||
                                target.closest('button') ||
                                target.closest('a');

      if (isStartMenuOpen &&
          taskbarRef.current &&
          !taskbarRef.current.contains(event.target as Node) &&
          !isNavigationElement) {
        // Only close if not clicking on the taskbar itself or navigation elements
        // Add a longer delay to prevent immediate closing
        setTimeout(() => {
          setIsStartMenuOpen(false);
        }, 200);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isStartMenuOpen]);

  // Close start menu when location changes (when navigating)
  useEffect(() => {
    setIsStartMenuOpen(false);
  }, [location.pathname]);

  // Show onboarding modal when user is authenticated and onboarding is not complete
  useEffect(() => {
    if (user && isOnboardingInitialized && !isOnboardingComplete && currentStep && currentStep !== 'completed' && !hideOnboardingModal) {
      setShowOnboarding(true);
    } else {
      setShowOnboarding(false);
    }
  }, [user, isOnboardingInitialized, isOnboardingComplete, currentStep, hideOnboardingModal]);

  // Auto-open start menu for start menu onboarding steps
  useEffect(() => {
    if (currentStep === 'start_menu_intro' || currentStep === 'start_menu_features') {
      // Small delay to ensure the onboarding modal is shown first
      const timer = setTimeout(() => {
        setIsStartMenuOpen(true);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [currentStep]);

  const handleStartClick = () => {
    setIsStartMenuOpen(!isStartMenuOpen);
  };

  const handleStartMenuClose = () => {
    setIsStartMenuOpen(false);
  };

  // Return loading state if auth is not yet initialized
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-sm text-gray-500">Loading...</div>
      </div>
    );
  }

  // Check if we should show onboarding progress (for top padding)
  const shouldShowProgress = user && isOnboardingInitialized && !isOnboardingComplete && currentStep !== 'completed';

  return (
    // Min-height screen to ensure layout fills viewport
    <div className="min-h-screen flex flex-col relative">
      {/* Main content area with padding to prevent content from being hidden behind taskbar and onboarding progress */}
      <div className={`flex-grow flex flex-col w-full ${!noPadding ? 'px-0' : ''} ${showTaskbar ? 'pb-20 sm:pb-24' : ''} ${shouldShowProgress ? 'pt-16' : ''}`}>
        {children}
      </div>
      
      {/* Show the onboarding modal unless explicitly hidden */}
      {!hideOnboardingModal && showOnboarding && (
        <OnboardingModal
          isOpen={showOnboarding}
          onClose={() => setShowOnboarding(false)}
        />
      )}

      {/* Fixed taskbar container - consistent spacing from bottom */}
      <div ref={taskbarRef} className="fixed bottom-0 left-0 right-0 z-[51] flex justify-center w-full p-2 sm:p-4 bg-transparent">
        {showTaskbar && (
          <Taskbar
            onStartClick={handleStartClick}
            isVisible={true}
            shouldShow={shouldShowTaskbar}
          />
        )}
      </div>

      {/* Start Menu */}
      <StartMenu
        isOpen={isStartMenuOpen}
        onClose={handleStartMenuClose}
      />

      {/* Onboarding hint for start menu steps */}
      {(currentStep === 'start_menu_intro' || currentStep === 'start_menu_features') && isStartMenuOpen && (
        <div className="fixed bottom-32 left-4 z-[52] pointer-events-none">
          <div className="bg-blue-500/90 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium animate-pulse">
            👆 Explore your start menu!
          </div>
        </div>
      )}
    </div>
  );
};

export default MainLayout;
