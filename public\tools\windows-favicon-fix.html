<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows Favicon Fix - CouponLink</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .container {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      background-color: #f9f9f9;
    }
    .steps {
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      border: 1px solid #eee;
      margin-top: 20px;
    }
    .favicon-preview {
      width: 64px;
      height: 64px;
      background-color: #0095F6;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px auto;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }
    .favicon-preview span {
      color: white;
      font-weight: bold;
      font-size: 32px;
    }
    .btn {
      display: inline-block;
      background-color: #0095F6;
      color: white;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 5px;
      margin: 10px 0;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .btn:hover {
      background-color: #007ad1;
    }
    h1, h2 {
      color: #0095F6;
    }
    h3 {
      margin-top: 24px;
    }
    .size-comparison {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      gap: 40px;
      margin: 30px 0;
    }
    .size {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .size-16 {
      width: 16px;
      height: 16px;
      background-color: #0095F6;
      border-radius: 2px;
    }
    .size-32 {
      width: 32px;
      height: 32px;
      background-color: #0095F6;
      border-radius: 4px;
    }
    .size-48 {
      width: 48px;
      height: 48px;
      background-color: #0095F6;
      border-radius: 6px;
    }
    .size-64 {
      width: 64px;
      height: 64px;
      background-color: #0095F6;
      border-radius: 8px;
    }
    .size p {
      margin-top: 8px;
      margin-bottom: 0;
      color: #666;
    }
    .alert {
      background-color: #ffecb3;
      border-left: 4px solid #ffc107;
      padding: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Windows Favicon Fix</h1>
  
  <div class="alert">
    <strong>Windows Problem:</strong> Your current favicon is too small to be properly visible in Windows. 
    Windows requires larger favicon sizes, especially for browser tabs and the taskbar.
  </div>
  
  <div class="container">
    <h2>Size Comparison</h2>
    
    <div class="size-comparison">
      <div class="size">
        <div class="size-16"></div>
        <p>16x16</p>
        <p>Too Small!</p>
      </div>
      <div class="size">
        <div class="size-32"></div>
        <p>32x32</p>
        <p>Better</p>
      </div>
      <div class="size">
        <div class="size-48"></div>
        <p>48x48</p>
        <p>Good</p>
      </div>
      <div class="size">
        <div class="size-64"></div>
        <p>64x64</p>
        <p>Best</p>
      </div>
    </div>
    
    <p><strong>The Solution:</strong> Windows needs a multi-size favicon.ico file containing several sizes in one file!</p>
    
    <div class="steps">
      <h3>How to Create a Proper Windows Favicon:</h3>
      <ol>
        <li>Go to <a href="https://convertico.com/" target="_blank">ConvertICO.com</a></li>
        <li>Upload your logo image (PNG format is best)</li>
        <li>In "Advanced Options":
          <ul>
            <li>Check "Generate multi-size ICO"</li>
            <li>Select ALL sizes: 16x16, 24x24, 32x32, 48x48, 64x64, 128x128</li>
          </ul>
        </li>
        <li>Click "Convert to ICO"</li>
        <li>Download the generated favicon.ico file</li>
        <li>Replace the existing favicon.ico in two locations:
          <ul>
            <li>/public/favicon/favicon.ico</li>
            <li>/public/favicon.ico</li>
          </ul>
        </li>
        <li>Clear your browser cache (Ctrl+Shift+Delete)</li>
        <li>Refresh your browser</li>
      </ol>
      
      <p>After following these steps, your favicon will be properly visible in Windows browser tabs, bookmarks, and taskbar.</p>
    </div>
  </div>
  
  <h2>Need a quick sample?</h2>
  <p>Below is a simple 64x64 "P" favicon. This is better than the tiny one but not as good as a proper multi-size ICO:</p>
  
  <div class="favicon-preview">
    <span>P</span>
  </div>
  
  <canvas id="faviconCanvas" width="64" height="64" style="display: none;"></canvas>
  
  <center>
    <a href="#" id="downloadLink" class="btn" download="favicon.ico">Download Sample Favicon (64x64)</a>
    <p><a href="/" style="display: block; margin-top: 20px; color: #0095F6;">Return to Homepage</a></p>
  </center>
  
  <script>
    // Create a larger favicon on a canvas
    window.onload = function() {
      const canvas = document.getElementById('faviconCanvas');
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = '#0095F6';
      ctx.fillRect(0, 0, 64, 64);
      
      // Rounded corners
      ctx.fillStyle = '#0095F6';
      ctx.beginPath();
      ctx.moveTo(0, 8);
      ctx.arcTo(0, 0, 8, 0, 8);
      ctx.lineTo(56, 0);
      ctx.arcTo(64, 0, 64, 8, 8);
      ctx.lineTo(64, 56);
      ctx.arcTo(64, 64, 56, 64, 8);
      ctx.lineTo(8, 64);
      ctx.arcTo(0, 64, 0, 56, 8);
      ctx.closePath();
      ctx.fill();
      
      // Text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 36px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('P', 32, 32);
      
      // Set up download link
      const downloadLink = document.getElementById('downloadLink');
      downloadLink.href = canvas.toDataURL('image/png');
      
      // When clicked
      downloadLink.addEventListener('click', function() {
        alert('This is only a temporary solution. For the best Windows visibility, please follow the steps to create a multi-size favicon.ico file.');
      });
    };
  </script>
</body>
</html> 