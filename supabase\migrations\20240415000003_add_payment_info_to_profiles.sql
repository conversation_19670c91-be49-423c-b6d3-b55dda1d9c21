-- Add payment information fields to profiles table
ALTER TABLE IF EXISTS public.profiles 
ADD COLUMN IF NOT EXISTS payment_id TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS payment_type TEXT DEFAULT NULL;

-- Add comment explaining the purpose of these fields
COMMENT ON COLUMN public.profiles.payment_id IS 'Payment identifier such as UPI ID, PayPal email, etc.';
COMMENT ON COLUMN public.profiles.payment_type IS 'Type of payment system (UPI, PayPal, etc.)';

-- Create an index on payment_type for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_payment_type ON public.profiles(payment_type);

-- Update RLS policies to ensure users can only update their own payment information
-- This assumes profiles already has RLS enabled with existing policies
-- If not, you may need to enable RLS and create appropriate policies

-- Add check constraint to validate payment_type values
ALTER TABLE public.profiles 
ADD CONSTRAINT valid_payment_types 
CHECK (
  payment_type IS NULL OR 
  payment_type IN ('UPI', 'PayPal', 'Other')
); 