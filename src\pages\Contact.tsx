import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import SEO from '@/seo/components/SEO';
import { COLORS } from '@/constants/theme';

const Contact = () => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Contact form submitted');
  };

  return (
    <MainLayout>
      <SEO 
        title="Contact Us - CouponLink"
        description="Get in touch with CouponLink. Contact our support team for help with your account, partnerships, or any questions about our coupon platform."
        keywords="contact, support, help, customer service, business inquiries, partnerships"
      />
      
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Contact Us"
          subtitle="We'd love to hear from you. Get in touch with our team."
          icon={Mail}
        />

        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Contact Information */}
            <div className="space-y-6">
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 className="text-xl font-semibold mb-4 text-gray-900">Get in Touch</h3>
                <p className="text-gray-600 mb-6">
                  Have questions about CouponLink? Need help with your account? Want to partner with us? 
                  We're here to help and would love to hear from you.
                </p>

                <div className="space-y-4">
                  {/* Business Email */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                      <Mail className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Email</p>
                      <a 
                        href="mailto:<EMAIL>" 
                        className="text-blue-600 hover:text-blue-700 transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  {/* Response Time */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Response Time</p>
                      <p className="text-gray-600">Within 24 hours</p>
                    </div>
                  </div>

                  {/* Business Hours */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Business Hours</p>
                      <p className="text-gray-600">Monday - Friday, 9AM - 6PM IST</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 text-gray-900">Quick Help</h3>
                <div className="space-y-3">
                  <a 
                    href="/help" 
                    className="block p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <p className="font-medium text-gray-900">Help Center</p>
                    <p className="text-sm text-gray-600">Find answers to common questions</p>
                  </a>
                  <a 
                    href="/terms" 
                    className="block p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <p className="font-medium text-gray-900">Terms of Service</p>
                    <p className="text-sm text-gray-600">Read our terms and conditions</p>
                  </a>
                  <a 
                    href="/privacy" 
                    className="block p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <p className="font-medium text-gray-900">Privacy Policy</p>
                    <p className="text-sm text-gray-600">Learn about our privacy practices</p>
                  </a>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-xl font-semibold mb-4 text-gray-900">Send us a Message</h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      First Name
                    </label>
                    <Input
                      id="firstName"
                      type="text"
                      required
                      className="w-full"
                      placeholder="Your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name
                    </label>
                    <Input
                      id="lastName"
                      type="text"
                      required
                      className="w-full"
                      placeholder="Your last name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    required
                    className="w-full"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <Input
                    id="subject"
                    type="text"
                    required
                    className="w-full"
                    placeholder="What's this about?"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    required
                    rows={5}
                    className="w-full"
                    placeholder="Tell us more about your inquiry..."
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full flex items-center justify-center gap-2"
                  style={{ background: COLORS.primary.gradient }}
                >
                  <Send className="h-4 w-4" />
                  Send Message
                </Button>
              </form>
            </div>
          </div>

          {/* Footer with Business Email */}
          <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 text-center">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">Business Inquiries</h3>
            <p className="text-gray-600 mb-4">
              For partnerships, business opportunities, or media inquiries, reach out to us directly:
            </p>
            <div className="flex items-center justify-center space-x-2 text-lg">
              <Mail className="h-5 w-5 text-blue-600" />
              <a 
                href="mailto:<EMAIL>" 
                className="font-semibold text-blue-600 hover:text-blue-700 transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default Contact;
