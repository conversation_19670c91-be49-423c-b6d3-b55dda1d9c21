import React, { useState, useCallback } from 'react';

interface BrandLogoProps {
  brandName: string;
  logoUrl?: string;
  website?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showFallbackText?: boolean;
}

/**
 * BrandLogo component with intelligent fallback logic:
 * 1. Try actual brand logo (if provided)
 * 2. Try Clearbit logo (if website available)
 * 3. Try Logo.dev API
 * 4. Fall back to UI-Avatars
 * 5. Finally show text fallback
 */
const BrandLogo: React.FC<BrandLogoProps> = ({
  brandName,
  logoUrl,
  website,
  size = 'md',
  className = '',
  showFallbackText = true,
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [fallbackLevel, setFallbackLevel] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showText, setShowText] = useState(false);

  // Size configurations
  const sizeConfig = {
    sm: { width: 'w-8', height: 'h-8', text: 'text-xs' },
    md: { width: 'w-12', height: 'h-12', text: 'text-sm' },
    lg: { width: 'w-16', height: 'h-16', text: 'text-base' },
    xl: { width: 'w-24', height: 'h-24', text: 'text-lg' },
  };

  const { width, height, text } = sizeConfig[size];

  // Extract domain from website URL
  const extractDomain = (url: string): string => {
    try {
      const domain = new URL(url.startsWith('http') ? url : `https://${url}`).hostname;
      return domain.replace('www.', '');
    } catch {
      return '';
    }
  };

  // Get logo sources in order of preference
  const getLogoSources = useCallback((): string[] => {
    const sources: string[] = [];
    
    // 1. Original logo URL (if provided and not already a UI-Avatar)
    if (logoUrl && !logoUrl.includes('ui-avatars.com')) {
      sources.push(logoUrl);
    }
    
    // 2. Clearbit logo (if website available)
    if (website) {
      const domain = extractDomain(website);
      if (domain) {
        sources.push(`https://logo.clearbit.com/${domain}`);
      }
    }
    
    // 3. Logo.dev API (alternative logo service)
    if (website) {
      const domain = extractDomain(website);
      if (domain) {
        sources.push(`https://img.logo.dev/${domain}?token=pk_X-1ZO13GSgeOoUrIuJ6GMQ&format=png&size=200`);
      }
    }
    
    // 4. UI-Avatars as fallback
    const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(brandName)}&background=random&color=fff&size=128&bold=true&format=png`;
    sources.push(uiAvatarUrl);
    
    return sources;
  }, [logoUrl, website, brandName]);

  // Initialize the first source
  React.useEffect(() => {
    const sources = getLogoSources();
    if (sources.length > 0) {
      setCurrentSrc(sources[0]);
      setFallbackLevel(0);
      setImageLoaded(false);
      setShowText(false);
    }
  }, [getLogoSources]);

  // Handle image load success
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setShowText(false);
  }, []);

  // Handle image load error - try next fallback
  const handleImageError = useCallback(() => {
    const sources = getLogoSources();
    const nextLevel = fallbackLevel + 1;
    
    if (nextLevel < sources.length) {
      setFallbackLevel(nextLevel);
      setCurrentSrc(sources[nextLevel]);
      setImageLoaded(false);
    } else {
      // All image sources failed, show text fallback
      setImageLoaded(false);
      setShowText(showFallbackText);
    }
  }, [fallbackLevel, getLogoSources, showFallbackText]);

  // Get brand initials for text fallback
  const getBrandInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`${width} ${height} rounded-lg flex items-center justify-center bg-gray-100 overflow-hidden ${className}`}>
      {showText ? (
        // Text fallback
        <div className={`${text} font-bold text-gray-600 text-center`}>
          {getBrandInitials(brandName)}
        </div>
      ) : (
        // Image
        <img
          src={currentSrc}
          alt={`${brandName} logo`}
          className={`w-full h-full object-contain transition-opacity duration-300 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
      )}
      
      {/* Loading state */}
      {!imageLoaded && !showText && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-pulse bg-gray-200 rounded w-3/4 h-3/4"></div>
        </div>
      )}
    </div>
  );
};

export default BrandLogo;
