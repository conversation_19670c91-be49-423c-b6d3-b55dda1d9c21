import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { 
  User, 
  AtSign, 
  Check, 
  Search, 
  LinkIcon,
  Copy,
  CheckCircle,
  Quote,
  Sparkles,
  ArrowRight
} from 'lucide-react';
import { COLORS, PLATFORMS } from '@/constants/theme';

// Function to handle image loading errors
const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  e.currentTarget.style.display = 'none';
};

// Simplified animation variants
const sharedAnimations = {
  fadeIn: {
    initial: { opacity: 0, y: 20 },
    whileInView: { opacity: 1, y: 0 },
    transition: { duration: 0.4, ease: "easeOut" },
    viewport: { once: true }
  },
  fadeInDelayed: (delay = 0.2) => ({
    initial: { opacity: 0, y: 20 },
    whileInView: { opacity: 1, y: 0 },
    transition: { duration: 0.4, delay, ease: "easeOut" },
    viewport: { once: true }
  }),
  slideFromLeft: (delay = 0.1) => ({
    initial: { opacity: 0, x: -30 },
    whileInView: { opacity: 1, x: 0 },
    transition: { duration: 0.5, delay, ease: "easeOut" },
    viewport: { once: true }
  }),
  slideFromRight: (delay = 0.1) => ({
    initial: { opacity: 0, x: 30 },
    whileInView: { opacity: 1, x: 0 },
    transition: { duration: 0.5, delay, ease: "easeOut" },
    viewport: { once: true }
  })
};

// Helper for common motion properties to avoid repetition
const motionProps = (type: keyof typeof sharedAnimations, delay = 0) => {
  const props = { ...(sharedAnimations[type] as any) };
  if (delay) {
    props.transition = { ...props.transition, ...sharedAnimations.fadeInDelayed(delay).transition };
  }
  return props;
};

// Simplified custom styles
const customStyles = `
  .bg-glass {
    background: ${COLORS.surface.glass};
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .hero-glow {
    filter: drop-shadow(0 0 12px ${COLORS.primary.bgLight});
  }
  
  .dot-pattern {
    background-image: radial-gradient(${COLORS.tertiary.bgLight} 1px, transparent 1px);
    background-size: 16px 16px;
  }
  
  .slash-pattern {
    background: repeating-linear-gradient(
      45deg,
      ${COLORS.accent.bgLight},
      ${COLORS.accent.bgLight} 2px,
      transparent 2px,
      transparent 8px
    );
  }
  
  .diagonal-pattern {
    background: repeating-linear-gradient(
      -45deg,
      ${COLORS.secondary.bgLight},
      ${COLORS.secondary.bgLight} 1px,
      transparent 1px,
      transparent 10px
    );
  }
`;

interface LandingHeroProps {
  username: string;
  setUsername: (value: string) => void;
  isChecking: boolean;
  isAvailable: boolean | null;
  handleClaimUsername: () => void;
  usernameFormRef: React.RefObject<HTMLDivElement>;
}

const LandingHero: React.FC<LandingHeroProps> = ({ 
  username, 
  setUsername, 
  isChecking, 
  isAvailable, 
  handleClaimUsername,
  usernameFormRef
}) => {
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);
  const [typedText, setTypedText] = useState("");
  const phrases = ["content", "coupons", "deals", "links"];
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);

  // Typing animation effect
  useEffect(() => {
    const phrase = phrases[currentPhraseIndex];
    let timer: NodeJS.Timeout;
    
    // Type current phrase
    if (typedText.length < phrase.length) {
      timer = setTimeout(() => {
        setTypedText(phrase.substring(0, typedText.length + 1));
      }, 100 + Math.random() * 50);
    } 
    // Wait at full phrase
    else {
      timer = setTimeout(() => {
        setTypedText("");
        setCurrentPhraseIndex((currentPhraseIndex + 1) % phrases.length);
      }, 1500);
    }
    
    return () => clearTimeout(timer);
  }, [typedText, currentPhraseIndex]);

  // Copy link function
  const copyLink = () => {
    if (username) {
      navigator.clipboard.writeText(`www.couponlink.in/${username}`);
      toast.success(`Link copied to clipboard!`);
    }
  };
  
  // Handle create account directly from nav bar
  const handleCreateAccount = () => {
    navigate('/auth?mode=signup');
  };

  return (
    <section className="w-full relative overflow-hidden min-h-[92vh] flex items-center bg-white">
      {/* Apply simplified custom styles */}
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      
      {/* Simplified background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Simple, flat shapes for visual interest */}
        <div className="absolute top-[-5%] right-[-5%] w-[40%] h-[40%] bg-primary-bgLight rounded-full opacity-25"></div>
        <div className="absolute bottom-[10%] left-[-2%] w-[35%] h-[35%] bg-secondary-bgLight rounded-full opacity-20"></div>
        <div className="absolute top-[20%] left-[5%] w-[15%] h-[15%] bg-tertiary-bgLight rounded-full opacity-15"></div>
        <div className="absolute bottom-[30%] right-[15%] w-[10%] h-[10%] dot-pattern opacity-25"></div>
        <div className="absolute top-[40%] right-[25%] w-[8%] h-[8%] slash-pattern opacity-20"></div>
        <div className="absolute bottom-[50%] left-[20%] w-[6%] h-[6%] diagonal-pattern opacity-20"></div>
      </div>
      
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4 md:py-6 lg:py-8 relative z-10">
        {/* Navbar with minimal glass effect */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8 gap-2 bg-glass p-2 sm:p-3 rounded-xl border border-primary-bgLight/40">
          <Link to="/" className="flex items-center py-1 px-3">
            <div className="h-9 w-9 sm:h-10 sm:w-10 mr-2 rounded-md overflow-hidden shadow-sm hero-glow">
              <img 
                src="/logos/logo.png" 
                alt="CouponLink Logo" 
                className="w-full h-full object-contain"
              />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-neutral-900">CouponLink</h2>
          </Link>
          <div className="flex items-center space-x-3 py-1 px-2">
            <Link to="/auth">
              <Button variant="outline" size="sm" className="border-primary-main text-primary-main hover:bg-white rounded-lg">
                Sign In
              </Button>
            </Link>
            <Button 
              size="sm" 
              className="bg-primary-main hover:bg-primary-dark text-white shadow-sm hover:shadow-md transition-all duration-300 rounded-lg"
              onClick={handleCreateAccount}
            >
              Create Account
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 md:gap-12 items-center py-6 md:py-10">
          {/* Hero headline section - centered before profile preview */}
          <div className="md:col-span-12 flex flex-col items-center text-center mb-8">
            <motion.div
              {...sharedAnimations.fadeIn}
              className="mb-6 sm:mb-8 max-w-3xl mx-auto"
            >
              <motion.div 
                {...sharedAnimations.slideFromLeft(0.1)}
                className="inline-flex items-center bg-accent-bgLight border border-accent-main/20 text-accent-dark text-sm font-medium px-3 py-1 rounded-lg mb-4"
              >
                <Sparkles className="w-3.5 h-3.5 mr-1.5 text-accent-main" />
                <span>The all-in-one influencer tool</span>
              </motion.div>
              
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-3 sm:mb-4 leading-tight">
                <div className="text-neutral-900">One <span className="text-primary-main">professional</span> link for all your <span className="text-tertiary-dark">coupons</span></div>
              </h1>
        
              <p className="text-sm sm:text-base md:text-lg text-neutral-600 mb-5 sm:mb-6 leading-relaxed">
                CouponLink gives content creators one powerful link to showcase and monetize their digital presence across all social media platforms.
              </p>
              
              <div className="flex flex-wrap gap-3 mb-6 justify-center">
                <div className="flex items-center bg-white px-3 py-1.5 rounded-lg shadow-sm border-l-4 border-l-primary-main">
                  <Check className="w-4 h-4 text-primary-main mr-2" />
                  <span className="text-xs text-primary-dark">Free account option</span>
                </div>
                
                <div className="flex items-center bg-white px-3 py-1.5 rounded-lg shadow-sm border-l-4 border-l-secondary-main">
                  <Check className="w-4 h-4 text-secondary-dark mr-2" />
                  <span className="text-xs text-secondary-dark">Custom branding</span>
                </div>
                
                <div className="flex items-center bg-white px-3 py-1.5 rounded-lg shadow-sm border-l-4 border-l-accent-main">
                  <Check className="w-4 h-4 text-accent-dark mr-2" />
                  <span className="text-xs text-accent-dark">Performance analytics</span>
                </div>
              </div>

              <div className="flex items-center gap-4 mb-6 justify-center">
                <div className="inline-flex items-center gap-2 px-3 py-2 bg-white border border-primary-bgLight rounded-lg">
                  <User className="w-4 h-4 text-primary-main" />
                  <div className="flex gap-1 items-baseline">
                    <span className="text-sm font-medium text-primary-dark">5,000+</span>
                    <span className="text-xs text-neutral-600">creators </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Browse Influencers Section - Centered with the username form */}
            <div className="w-full max-w-xl mx-auto mb-4">
              <Link to="/browse-coupons" className="flex items-center justify-center gap-2 px-4 py-3 bg-tertiary-main hover:bg-tertiary-dark text-white hover:shadow-lg transition-all duration-300 rounded-xl shadow-md font-medium w-full">
                <Search className="w-4 h-4" />
                <span className="text-base">Browse Influencers' Coupons</span>
              </Link>
            </div>
            
            {/* Username claim form - Centered */}
            <motion.div
              id="username-form"
              {...motionProps('fadeIn', 0.2)}
              className="rounded-xl p-5 shadow-md overflow-hidden bg-white border border-primary-main/20 mb-6 max-w-xl mx-auto w-full"
              ref={usernameFormRef}
            >
              {/* Subtle background decoration */}
              <div className="absolute -right-16 -top-16 w-32 h-32 slash-pattern opacity-15 rotate-15"></div>
              
              {/* Form title - More compact with horizontal layout */}
              <div className="mb-3 flex items-center">
                <div className="bg-primary-bgLight p-2 rounded-lg mr-3">
                  <AtSign className="w-5 h-5 text-primary-main" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-neutral-800">Claim Your Username</h3>
                  <p className="text-xs text-neutral-600">Get your professional CouponLink link in seconds</p>
                </div>
              </div>

              <div className="bg-white p-3 mb-3 flex items-center rounded-lg border border-primary-main/20 shadow-sm focus-within:shadow hover:shadow-md transition-all duration-300">
                <LinkIcon className="w-4 h-4 mr-2 text-primary-main" />
                <span className="text-neutral-800 font-medium text-sm md:text-base whitespace-nowrap">www.couponlink.in/</span>
                <Input 
                  type="text" 
                  placeholder="yourname" 
                  value={username}
                  onChange={(e) => {
                    setUsername(e.target.value);
                  }}
                  className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto text-base md:text-lg bg-transparent text-primary-dark placeholder:text-neutral-400 placeholder:italic"
                />
              </div>
              
              {isAvailable !== null && (
                <div className={`p-2.5 rounded-lg mb-3 transition-all duration-300 ${isAvailable ? 'bg-white border-l-4 border-l-primary-main border-t border-r border-b border-primary-bgLight' : 'bg-white border-l-4 border-l-destructive border-t border-r border-b border-destructive-bgLight'}`}>
                  <div className="flex items-center">
                    {isAvailable ? (
                      <>
                        <div className="bg-primary-bgLight p-1.5 rounded-lg mr-2">
                          <Check className="w-3.5 h-3.5 text-primary-main" />
                        </div>
                        <span className="text-sm font-medium text-primary-dark">
                          Available! Redirecting to signup...
                        </span>
                      </>
                    ) : (
                      <>
                        <div className="bg-destructive-bgLight p-1.5 rounded-lg mr-2">
                          <Search className="w-3.5 h-3.5 text-destructive" />
                        </div>
                        <span className="text-sm font-medium text-destructive">
                          Sorry, username is taken.
                        </span>
                      </>
                    )}
                  </div>
                </div>
              )}
              
              <Button 
                onClick={handleClaimUsername} 
                disabled={!username.trim() || isChecking}
                className="py-3 px-4 bg-accent-main hover:bg-accent-dark text-white font-medium text-base rounded-lg flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-300 w-full"
              >
                {isChecking ? (
                  <span className="flex items-center">
                    <span className="animate-spin mr-2 h-4 w-4 border-t-2 border-white rounded-full"></span>
                    Checking availability...
                  </span>
                ) : (
                  <span className="flex items-center">
                    Claim your CouponLink
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </span>
                )}
              </Button>
              
              <div className="mt-3 pt-2 border-t border-neutral-100 flex justify-end">
                <p className="text-xs text-neutral-500 italic">
                  <span className="inline-flex items-center">
                    <Check className="w-3 h-3 text-primary-main mr-1" />
                    Your unique URL for all platforms
                  </span>
                </p>
              </div>
            </motion.div>
          </div>
          
          {/* Profile preview section with 3D effect */}
          <div className="md:col-span-12 relative pt-10 pb-20">
            {/* Left background profile */}
            <div className="absolute left-[2%] md:left-[15%] top-12 opacity-75 transform -rotate-12 scale-90 z-0 hidden md:block" style={{ maxWidth: "330px", transform: "perspective(1000px) rotateY(25deg) rotateX(5deg) scale(0.9)" }}>
              <div className="rounded-xl border border-neutral-200 shadow-md bg-white overflow-hidden">
                <div className="bg-primary-main/80 rounded-t-xl p-1.5">
                  <div className="bg-primary-dark/20 py-1.5 px-2 flex items-center gap-1.5 rounded-lg">
                    <div className="rounded-full h-2 w-2 bg-accent-light/80"></div>
                    <div className="rounded-full h-2 w-2 bg-secondary-main/80"></div>
                    <div className="rounded-full h-2 w-2 bg-tertiary-light/80"></div>
                    <div className="bg-white/20 rounded-md text-[10px] text-white/80 px-1.5 py-0.5 ml-2 flex-1 text-center">
                      www.couponlink.in/michael
                    </div>
                  </div>
                </div>
                <div className="p-3 flex flex-col items-center">
                  <div className="w-12 h-12 rounded-lg border-2 border-white shadow-md overflow-hidden mb-1.5">
                    <img 
                      src="https://randomuser.me/api/portraits/men/22.jpg" 
                      alt="Profile" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-sm font-bold">Michael Brown</h3>
                  <p className="text-[10px] text-neutral-600">Tech Reviewer</p>
                  
                  {/* User badges */}
                  <div className="flex items-center bg-primary-bgLight/80 px-1.5 py-0.5 rounded mt-1 mb-0.5">
                    <AtSign className="w-2.5 h-2.5 text-primary-main mr-0.5" />
                    <span className="text-[9px] font-medium text-primary-main">michael</span>
                  </div>
                  <div className="flex items-center bg-accent-bgLight/80 px-1.5 py-0.5 rounded mb-1">
                    <User className="w-2.5 h-2.5 text-accent-main mr-0.5" />
                    <span className="text-[9px] font-medium text-accent-main">Pro Creator</span>
                  </div>
                  
                  {/* Social icons */}
                  <div className="flex justify-center gap-1 mb-2">
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.495 6.205a3.007 3.007 0 0 0-2.088-2.088c-1.87-.501-9.396-.501-9.396-.501s-7.507-.01-9.396.501A3.007 3.007 0 0 0 .527 6.205a31.247 31.247 0 0 0-.522 5.805 31.247 31.247 0 0 0 .522 5.783 3.007 3.007 0 0 0 2.088 2.088c1.868.502 9.396.502 9.396.502s7.506 0 9.396-.502a3.007 3.007 0 0 0 2.088-2.088 31.247 31.247 0 0 0 .5-5.783 31.247 31.247 0 0 0-.5-5.805zM9.609 15.601V8.408l6.264 3.602z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-1-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.495 6.205a3.007 3.007 0 0 0-2.088-2.088c-1.87-.501-9.396-.501-9.396-.501s-7.507-.01-9.396.501A3.007 3.007 0 0 0 .527 6.205a31.247 31.247 0 0 0-.522 5.805 31.247 31.247 0 0 0 .522 5.783 3.007 3.007 0 0 0 2.088 2.088c1.868.502 9.396.502 9.396.502s7.506 0 9.396-.502a3.007 3.007 0 0 0 2.088-2.088 31.247 31.247 0 0 0 .5-5.783 31.247 31.247 0 0 0-.5-5.805zM9.609 15.601V8.408l6.264 3.602z" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Listed Deals Header */}
                  <div className="w-full px-1 mb-1.5">
                    <div className="border border-primary-main/30 text-primary-dark bg-white hover:bg-primary-bgLight/30 transition-colors rounded text-[9px] p-1.5 text-center">
                      <span className="font-medium">Listed Deals</span>
                    </div>
                  </div>
                  
                  {/* Coupon deals - LEFT PROFILE */}
                  <div className="w-full px-1">
                    <div className="bg-white rounded-lg shadow-sm border border-neutral-100 flex flex-col mb-1.5 overflow-hidden">
                      {/* Top section with logo and details */}
                      <div className="p-1.5">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-black rounded-sm flex items-center justify-center mr-1.5 overflow-hidden">
                            <img src="https://m.media-amazon.com/images/G/01/gc/designs/livepreview/amazon_dkblue_noto_email_v2016_us-main._CB468775337_.png" alt="Amazon" className="w-4 h-4 object-contain" style={{filter: "brightness(0) invert(1)"}} />
                          </div>
                          <span className="text-[10px] font-bold text-neutral-900">Amazon Prime</span>
                          <div className="ml-auto bg-amber-50 text-amber-700 text-[8px] rounded-full py-0.5 px-1 font-medium flex items-center">
                            <span className="text-amber-400 mr-0.5">★</span> PREMIUM
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-0.5">
                          <span className="text-[8px] font-medium text-neutral-700">25% off subscription</span>
                          <span className="text-[7px] text-emerald-600 font-medium">Ends in 5 days</span>
                        </div>
                      </div>
                      
                      {/* Bottom section with code or action */}
                      <div className="bg-neutral-50 py-1 px-1.5 flex items-center justify-between">
                        <div className="flex-1 text-center text-[8px] text-neutral-600 font-medium">Premium Access Only</div>
                        <div className="bg-red-500 text-white font-medium py-0.5 px-1.5 rounded text-[7px] ml-1">
                          $5.99
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-sm border border-neutral-100 flex flex-col mb-1.5 overflow-hidden">
                      {/* Top section with logo and details */}
                      <div className="p-1.5">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-white rounded-sm flex items-center justify-center mr-1.5 overflow-hidden border border-neutral-200">
                            <img src="https://cdn.iconscout.com/icon/free/png-256/free-nike-1-202653.png" alt="Nike" className="w-4 h-4 object-contain" />
                          </div>
                          <span className="text-[10px] font-bold text-neutral-900">Nike</span>
                          <div className="ml-auto bg-green-50 text-green-600 text-[8px] rounded-full py-0.5 px-1 font-medium flex items-center">
                            FREE
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-0.5">
                          <span className="text-[8px] font-medium text-neutral-700">20% off sportswear</span>
                          <span className="text-[7px] text-green-600 font-medium">Verified</span>
                        </div>
                      </div>
                      
                      {/* Bottom section with code */}
                      <div className="bg-neutral-50 py-1 px-1.5 flex items-center justify-between">
                        <div className="flex-1 text-center font-mono text-[8px] font-medium tracking-wide">
                          NIKE20OFF
                        </div>
                        <button className="bg-green-500 text-white text-[7px] font-medium py-0.5 px-1.5 rounded flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Copy
                        </button>
                      </div>
                    </div>

                    {/* LEFT PROFILE - CouponLink Join Now mini-section */}
                    <div className="bg-neutral-50 rounded-lg shadow-sm border border-neutral-100 flex items-center justify-between p-1.5 mt-1">
                      <div className="flex items-center">
                        <div className="h-5 w-5 mr-1.5 rounded-sm overflow-hidden">
                          <img 
                            src="/logos/logo.png" 
                            alt="CouponLink Logo" 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span className="text-[10px] font-bold text-neutral-900">CouponLink</span>
                      </div>
                      <button className="bg-blue-500 text-white text-[7px] font-medium py-0.5 px-1.5 rounded hover:bg-blue-600 transition-colors">
                        Join Now
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right background profile */}
            <div className="absolute right-[2%] md:right-[15%] top-12 opacity-75 transform rotate-12 scale-90 z-0 hidden md:block" style={{ maxWidth: "330px", transform: "perspective(1000px) rotateY(-25deg) rotateX(5deg) scale(0.9)" }}>
              <div className="rounded-xl border border-neutral-200 shadow-md bg-white overflow-hidden">
                <div className="bg-primary-main/80 rounded-t-xl p-1.5">
                  <div className="bg-primary-dark/20 py-1.5 px-2 flex items-center gap-1.5 rounded-lg">
                    <div className="rounded-full h-2 w-2 bg-accent-light/80"></div>
                    <div className="rounded-full h-2 w-2 bg-secondary-main/80"></div>
                    <div className="rounded-full h-2 w-2 bg-tertiary-light/80"></div>
                    <div className="bg-white/20 rounded-md text-[10px] text-white/80 px-1.5 py-0.5 ml-2 flex-1 text-center">
                      www.couponlink.in/emily
                    </div>
                  </div>
                </div>
                <div className="p-3 flex flex-col items-center">
                  <div className="w-12 h-12 rounded-lg border-2 border-white shadow-md overflow-hidden mb-1.5">
                    <img 
                      src="https://randomuser.me/api/portraits/women/32.jpg" 
                      alt="Profile" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-sm font-bold">Emily Davis</h3>
                  <p className="text-[10px] text-neutral-600">Beauty Blogger</p>
                  
                  {/* User badges */}
                  <div className="flex items-center bg-primary-bgLight/80 px-1.5 py-0.5 rounded mt-1 mb-0.5">
                    <AtSign className="w-2.5 h-2.5 text-primary-main mr-0.5" />
                    <span className="text-[9px] font-medium text-primary-main">emily</span>
                  </div>
                  <div className="flex items-center bg-accent-bgLight/80 px-1.5 py-0.5 rounded mb-1">
                    <User className="w-2.5 h-2.5 text-accent-main mr-0.5" />
                    <span className="text-[9px] font-medium text-accent-main">Pro Creator</span>
                  </div>
                  
                  {/* Social icons */}
                  <div className="flex justify-center gap-1 mb-2">
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-1-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z" />
                      </svg>
                    </div>
                    <div className="w-5 h-5 bg-neutral-100 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.495 6.205a3.007 3.007 0 0 0-2.088-2.088c-1.87-.501-9.396-.501-9.396-.501s-7.507-.01-9.396.501A3.007 3.007 0 0 0 .527 6.205a31.247 31.247 0 0 0-.522 5.805 31.247 31.247 0 0 0 .522 5.783 3.007 3.007 0 0 0 2.088 2.088c1.868.502 9.396.502 9.396.502s7.506 0 9.396-.502a3.007 3.007 0 0 0 2.088-2.088 31.247 31.247 0 0 0 .5-5.783 31.247 31.247 0 0 0-.5-5.805zM9.609 15.601V8.408l6.264 3.602z" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Listed Deals Header */}
                  <div className="w-full px-1 mb-1.5">
                    <div className="border border-primary-main/30 text-primary-dark bg-white hover:bg-primary-bgLight/30 transition-colors rounded text-[9px] p-1.5 text-center">
                      <span className="font-medium">Listed Deals</span>
                    </div>
                  </div>
                  
                  {/* Coupon deals - RIGHT PROFILE */}
                  <div className="w-full px-1">
                    <div className="bg-white rounded-lg shadow-sm border border-neutral-100 flex flex-col mb-1.5 overflow-hidden">
                      {/* Top section with logo and details */}
                      <div className="p-1.5">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-white rounded-sm flex items-center justify-center mr-1.5 overflow-hidden border border-neutral-200">
                            <img src="https://cdn.iconscout.com/icon/free/png-256/free-nike-1-202653.png" alt="Nike" className="w-4 h-4 object-contain" />
                          </div>
                          <span className="text-[10px] font-bold text-neutral-900">Nike</span>
                          <div className="ml-auto bg-green-50 text-green-600 text-[8px] rounded-full py-0.5 px-1 font-medium flex items-center">
                            FREE
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-0.5">
                          <span className="text-[8px] font-medium text-neutral-700">20% off sportswear</span>
                          <span className="text-[7px] text-green-600 font-medium">Verified</span>
                        </div>
                      </div>
                      
                      {/* Bottom section with code */}
                      <div className="bg-neutral-50 py-1 px-1.5 flex items-center justify-between">
                        <div className="flex-1 text-center font-mono text-[8px] font-medium tracking-wide">
                          NIKE20OFF
                        </div>
                        <button className="bg-green-500 text-white text-[7px] font-medium py-0.5 px-1.5 rounded flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Copy
                        </button>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-sm border border-neutral-100 flex flex-col mb-1.5 overflow-hidden">
                      {/* Top section with logo and details */}
                      <div className="p-1.5">
                        <div className="flex items-center">
                          <div className="w-5 h-5 bg-[#1DB954] rounded-sm flex items-center justify-center mr-1.5 overflow-hidden">
                            <img src="https://storage.googleapis.com/pr-newsroom-wp/1/2018/11/Spotify_Logo_RGB_White.png" alt="Spotify" className="w-4 h-4 object-contain" />
                          </div>
                          <span className="text-[10px] font-bold text-neutral-900">Spotify</span>
                          <div className="ml-auto bg-amber-50 text-amber-700 text-[8px] rounded-full py-0.5 px-1 font-medium flex items-center">
                            <span className="text-amber-400 mr-1">★</span> PREMIUM
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-0.5">
                          <span className="text-[8px] font-medium text-neutral-700">3 months free</span>
                          <span className="text-[7px] text-blue-600 font-medium">Limited offer</span>
                        </div>
                      </div>
                      
                      {/* Bottom section with code or action */}
                      <div className="bg-neutral-50 py-1 px-1.5 flex items-center justify-between">
                        <div className="flex-1 text-center text-[8px] text-neutral-600 font-medium">Premium Access Only</div>
                        <div className="bg-red-500 text-white font-medium py-0.5 px-1.5 rounded text-[7px] ml-1">
                          $3.99
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* RIGHT PROFILE - CouponLink Join Now mini-section */}
                  <div className="bg-neutral-50 rounded-lg shadow-sm border border-neutral-100 flex items-center justify-between p-1.5 mt-1">
                    <div className="flex items-center">
                      <div className="h-5 w-5 mr-1.5 rounded-sm overflow-hidden">
                        <img 
                          src="/logos/logo.png" 
                          alt="CouponLink Logo" 
                          className="w-full h-full object-cover"
                          onError={e => (e.currentTarget.src = "https://via.placeholder.com/20/4285F4/FFFFFF?text=PV")}
                        />
                      </div>
                      <span className="text-[10px] font-bold text-neutral-900">CouponLink</span>
                    </div>
                    <button className="bg-blue-500 text-white text-[7px] font-medium py-0.5 px-1.5 rounded hover:bg-blue-600 transition-colors">
                      Join Now
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Main profile */}
            <div className="max-w-sm mx-auto relative z-10">
              <div className="rounded-xl border border-neutral-200 shadow-lg bg-white overflow-hidden">
                <div className="bg-primary-main rounded-t-xl p-1.5">
                  <div className="bg-primary-dark/20 py-1.5 px-3 flex items-center gap-2 rounded-lg">
                    <div className="rounded-full h-2.5 w-2.5 bg-accent-light"></div>
                    <div className="rounded-full h-2.5 w-2.5 bg-secondary-main"></div>
                    <div className="rounded-full h-2.5 w-2.5 bg-tertiary-light"></div>
                    <div className="bg-white/20 rounded-md text-xs text-white/80 px-2 py-0.5 ml-3 flex-1 text-center overflow-hidden text-ellipsis whitespace-nowrap">
                      www.couponlink.in/{username || 'yourname'}
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-3">
                  <div className="flex flex-col items-center mb-3">
                    <div className="w-16 h-16 rounded-lg border-4 border-white shadow-md overflow-hidden mb-2">
                      <img 
                        src="https://randomuser.me/api/portraits/women/44.jpg" 
                        alt="Profile" 
                        className="w-full h-full object-cover" 
                        onError={handleImageError}
                      />
                    </div>
                    <h3 className="text-base font-bold text-neutral-900">Sarah Johnson</h3>
                    <p className="text-xs text-neutral-600 mb-1">Lifestyle & Fitness Creator</p>
                    <div className="flex items-center bg-primary-bgLight px-2 py-0.5 rounded-md">
                      <AtSign className="w-3 h-3 text-primary-main mr-1" />
                      <span className="text-xs font-medium text-primary-main">{username || 'yourname'}</span>
                    </div>
                    <div className="flex items-center mt-1 bg-accent-bgLight px-2 py-0.5 rounded-md">
                      <User className="w-3 h-3 text-accent-main mr-1" />
                      <span className="text-xs font-medium text-accent-main">Pro Creator</span>
                      </div>
                  </div>
                  
                  <div className="mb-2">
                    <div className="border border-primary-main/30 text-primary-dark hover:bg-primary-bgLight transition-colors rounded-lg p-2 text-center cursor-pointer">
                      <span className="font-medium text-sm">Listed Deals</span>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-neutral-100 flex flex-col mb-2.5 overflow-hidden">
                    {/* Top section with logo and details */}
                    <div className="p-2.5">
                      <div className="flex items-center mb-1">
                        <div className="w-7 h-7 bg-white rounded-md flex items-center justify-center mr-2.5 overflow-hidden border border-neutral-200">
                          <img src="https://cdn.iconscout.com/icon/free/png-256/free-nike-1-202653.png" alt="Nike" className="w-5 h-5 object-contain" />
                        </div>
                        <span className="text-sm font-bold text-neutral-900">Nike</span>
                        <div className="ml-auto bg-green-50 text-green-600 text-xs rounded-full py-0.5 px-2 font-medium flex items-center">
                          FREE
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs font-medium text-neutral-700">20% off sportswear</span>
                        <span className="text-xs text-green-600 font-medium">Verified</span>
                      </div>
                    </div>
                    
                    {/* Bottom section with code */}
                    <div className="bg-neutral-50 py-2 px-2.5 flex items-center justify-between">
                      <div className="flex-1 text-center font-mono text-xs font-medium tracking-wide">
                        NIKE20OFF
                      </div>
                      <button className="bg-green-500 text-white text-xs font-medium py-1 px-2.5 rounded flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Copy
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-neutral-100 flex flex-col overflow-hidden">
                    {/* Top section with logo and details */}
                    <div className="p-2.5">
                      <div className="flex items-center mb-1">
                        <div className="w-7 h-7 bg-[#1DB954] rounded-md flex items-center justify-center mr-2.5 overflow-hidden">
                          <img src="https://storage.googleapis.com/pr-newsroom-wp/1/2018/11/Spotify_Logo_RGB_White.png" alt="Spotify" className="w-5 h-5 object-contain" />
                        </div>
                        <span className="text-sm font-bold text-neutral-900">Spotify</span>
                        <div className="ml-auto bg-amber-50 text-amber-700 text-xs rounded-full py-0.5 px-2 font-medium flex items-center">
                          <span className="text-amber-400 mr-1">★</span> PREMIUM
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs font-medium text-neutral-700">3 months free</span>
                        <span className="text-xs text-blue-600 font-medium">Limited offer</span>
                      </div>
                    </div>
                    
                    {/* Bottom section with code or action */}
                    <div className="bg-neutral-50 py-2 px-2.5 flex items-center justify-between">
                      <div className="flex-1 text-center text-xs text-neutral-600 font-medium">Premium Access Only</div>
                      <div className="bg-red-500 text-white font-medium py-1 px-2.5 rounded text-xs ml-2">
                        $3.99
                      </div>
                    </div>
                  </div>
                  
                  {/* Main profile CouponLink Join Now section */}
                  <div className="bg-neutral-50 rounded-xl shadow-sm border border-neutral-100 flex items-center justify-between p-2.5 mt-2.5">
                      <div className="flex items-center">
                      <div className="h-7 w-7 mr-2 rounded-md overflow-hidden">
                          <img 
                            src="/logos/logo.png" 
                            alt="CouponLink Logo" 
                            className="w-full h-full object-cover"
                            onError={e => (e.currentTarget.src = "https://via.placeholder.com/32/4285F4/FFFFFF?text=PV")}
                          />
                      </div>
                      <span className="text-sm font-bold text-neutral-900">CouponLink</span>
                    </div>
                    <button className="bg-blue-500 text-white text-xs font-medium py-1.5 px-3 rounded hover:bg-blue-600 transition-colors">
                      Join Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LandingHero; 