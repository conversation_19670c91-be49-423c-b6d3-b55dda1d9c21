import type { Config } from "tailwindcss";
import animate from "tailwindcss-animate";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx,js,jsx,html}",
		"./index.html",
		"!./src/components/onboarding/MenuIndicatorPopup.tsx",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'xs': '360px',
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1400px'
			}
		},
		extend: {
			zIndex: {
				'60': '60',
				'70': '70',
				'80': '80',
				'90': '90',
				'100': '100',
			},
			// System colors for forced colors mode
			colors: {
				'system': {
					'canvas': 'Canvas',
					'canvas-text': 'CanvasText',
					'button-face': 'ButtonFace',
					'button-text': 'ButtonText',
					'highlight': 'Highlight',
					'highlight-text': 'HighlightText',
					'link-text': 'LinkText',
					'active-text': 'ActiveText',
				}
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))',
					main: '#0ea76b',
					light: '#25c785',
					dark: '#0a8554',
					gradient: 'linear-gradient(135deg, #0ea76b 0%, #0a8554 100%)',
					bgLight: 'rgba(14, 167, 107, 0.1)'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))',
					main: '#e9a800',
					light: '#ffc01e',
					dark: '#c89000',
					gradient: 'linear-gradient(135deg, #e9a800 0%, #c89000 100%)',
					bgLight: 'rgba(233, 168, 0, 0.1)'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))',
					main: '#c30f69',
					light: '#e71f80',
					dark: '#a00954',
					gradient: 'linear-gradient(135deg, #c30f69 0%, #a00954 100%)',
					bgLight: 'rgba(195, 15, 105, 0.1)'
				},
				tertiary: {
					main: '#0095f6',
					light: '#20a6ff',
					dark: '#0077c8',
					gradient: 'linear-gradient(135deg, #0095f6 0%, #0077c8 100%)',
					bgLight: 'rgba(0, 149, 246, 0.1)'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				surface: {
					light: 'rgba(255, 255, 255, 0.95)',
					lightTransparent: 'rgba(255, 255, 255, 0.8)',
					dark: 'rgba(31, 41, 55, 0.95)',
					darkTransparent: 'rgba(31, 41, 55, 0.8)',
					glass: 'rgba(255, 255, 255, 0.6)'
				},
				neutral: {
					50: '#F9FAFB',
					100: '#F3F4F6',
					200: '#E5E7EB',
					300: '#D1D5DB',
					400: '#9CA3AF',
					500: '#6B7280',
					600: '#4B5563',
					700: '#374151',
					800: '#1F2937',
					900: '#111827'
				},
				brand: {
					malachite: {
						DEFAULT: '#0ea76b',
						100: '#021b13',
						200: '#053626',
						300: '#075139',
						400: '#0a6c4c',
						500: '#0ea76b',
						600: '#25c785',
						700: '#50dba0',
						800: '#8be7c1',
						900: '#c5f3e0'
					},
					yellow: {
						DEFAULT: '#e9a800',
						100: '#2f2100',
						200: '#5e4200',
						300: '#8d6400',
						400: '#bc8500',
						500: '#e9a800',
						600: '#ffc01e',
						700: '#ffd15c',
						800: '#ffe299',
						900: '#fff0d6'
					},
					pink: {
						DEFAULT: '#c30f69',
						100: '#280215',
						200: '#4f042a',
						300: '#77063f',
						400: '#9f0854',
						500: '#c30f69',
						600: '#e71f80',
						700: '#ed519c',
						800: '#f385b9',
						900: '#f9c2d9'
					},
					blue: {
						DEFAULT: '#0095f6',
						100: '#001e32',
						200: '#003c64',
						300: '#005996',
						400: '#0077c8',
						500: '#0095f6',
						600: '#20a6ff',
						700: '#52bbff',
						800: '#85d1ff',
						900: '#bde8ff'
					},
					chartreuse: {
						DEFAULT: '#98d100',
						100: '#1f2b00',
						200: '#3d5500',
						300: '#5c8000',
						400: '#7aaa00',
						500: '#98d100',
						600: '#b2ed29',
						700: '#c4f461',
						800: '#d9fa98',
						900: '#ecfdce'
					}
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'float': {
					'0%, 100%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-10px)'
					}
				},
				'scale-in': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'scale(1)'
					}
				},
				'slideDown': {
					'0%': {
						transform: 'translateY(-100%)',
						opacity: '0'
					},
					'100%': {
						transform: 'translateY(0)',
						opacity: '1'
					}
				},
				'fadeIn': {
					'0%': {
						opacity: '0'
					},
					'100%': {
						opacity: '1'
					}
				},
				'blurIn': {
					'0%': {
						backdropFilter: 'blur(0px)',
						backgroundColor: 'rgba(0, 0, 0, 0)'
					},
					'100%': {
						backdropFilter: 'blur(8px)',
						backgroundColor: 'rgba(0, 0, 0, 0.3)'
					}
				},
				'highlight-pulse': {
					'0%, 100%': {
						boxShadow: '0 0 0 0 rgba(236, 72, 153, 0)'
					},
					'50%': {
						boxShadow: '0 0 0 8px rgba(236, 72, 153, 0.2)'
					}
				},
				'share-bounce': {
					'0%, 20%, 50%, 80%, 100%': {
						transform: 'translateY(0)'
					},
					'40%': {
						transform: 'translateY(-8px)'
					},
					'60%': {
						transform: 'translateY(-4px)'
					}
				},
				'coupon-appear': {
					'0%': { 
						opacity: '0', 
						transform: 'scale(0.9) translateY(10px)' 
					},
					'100%': { 
						opacity: '1', 
						transform: 'scale(1) translateY(0)' 
					},
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'float': 'float 3s ease-in-out infinite',
				'scale-in': 'scale-in 0.2s ease-out',
				'slide-down': 'slideDown 0.3s ease-out',
				'fade-in-slow': 'fadeIn 0.3s ease-out',
				'blur-in': 'blurIn 0.3s ease-out',
				'highlight-pulse': 'highlight-pulse 1.5s ease-in-out',
				'share-bounce': 'share-bounce 1s ease-in-out',
				'coupon-appear': 'coupon-appear 0.3s ease-out',
			}
		}
	},
	plugins: [animate],
} satisfies Config;
