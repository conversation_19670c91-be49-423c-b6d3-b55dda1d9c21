import React, { useState, useEffect, useRef } from 'react';
import { Search, X, TrendingUp, Clock, ArrowRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useCoupons } from '@/hooks/useCoupons';
import { getSearchSuggestions, getPopularSearchTerms } from '@/utils/searchUtils';
import { cn } from '@/lib/utils';
import { safeParseLocalStorage, safeSetLocalStorage } from '@/utils/localStorageHelpers';

interface EnhancedSearchBarProps {
  placeholder?: string;
  className?: string;
  showPopup?: boolean; // If true, shows popup with suggestions. If false, redirects directly
  onSearch?: (query: string) => void;
  autoFocus?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const EnhancedSearchBar: React.FC<EnhancedSearchBarProps> = ({
  placeholder = "Search by brand, influencer, category, code, or description...",
  className,
  showPopup = true,
  onSearch,
  autoFocus = false,
  size = 'md'
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [popularTerms, setPopularTerms] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { data: coupons } = useCoupons();

  // Size configurations with responsive design
  const sizeConfig = {
    sm: {
      input: 'h-10 text-sm',
      button: 'h-8 px-3 text-sm',
      popup: 'w-full',
      icon: 'h-4 w-4'
    },
    md: {
      input: 'h-12 text-base',
      button: 'h-10 px-4 text-base',
      popup: 'w-full',
      icon: 'h-5 w-5'
    },
    lg: {
      input: 'h-14 text-lg',
      button: 'h-12 px-6 text-lg',
      popup: 'w-full',
      icon: 'h-6 w-6'
    }
  };

  const config = sizeConfig[size];

  // Load recent searches from localStorage
  useEffect(() => {
    const recentSearchesData = safeParseLocalStorage<string[]>('recentSearches', []);
    setRecentSearches(recentSearchesData);
  }, []);

  // Get popular terms when coupons load
  useEffect(() => {
    if (coupons && coupons.length > 0) {
      const popular = getPopularSearchTerms(coupons);
      setPopularTerms(popular.slice(0, 6));
    }
  }, [coupons]);

  // Update suggestions when query changes
  useEffect(() => {
    if (query.length >= 2 && coupons && coupons.length > 0) {
      const newSuggestions = getSearchSuggestions(coupons, query);
      setSuggestions(newSuggestions);
    } else {
      setSuggestions([]);
    }
    setSelectedIndex(-1);
  }, [query, coupons]);

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Save search to recent searches
  const saveRecentSearch = (searchQuery: string) => {
    const trimmed = searchQuery.trim();
    if (!trimmed) return;

    const updated = [trimmed, ...recentSearches.filter(s => s !== trimmed)].slice(0, 5);
    setRecentSearches(updated);
    safeSetLocalStorage('recentSearches', updated);
  };

  // Handle search submission
  const handleSearch = (searchQuery: string = query) => {
    const trimmed = searchQuery.trim();
    if (!trimmed) return;

    saveRecentSearch(trimmed);
    setIsOpen(false);
    setQuery('');

    if (onSearch) {
      onSearch(trimmed);
    } else {
      navigate(`/search?q=${encodeURIComponent(trimmed)}`);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const allItems = [...suggestions, ...recentSearches, ...popularTerms];
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % allItems.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev <= 0 ? allItems.length - 1 : prev - 1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && allItems[selectedIndex]) {
          handleSearch(allItems[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle input focus
  const handleFocus = () => {
    if (showPopup) {
      setIsOpen(true);
    }
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  return (
    <div ref={searchRef} className={cn("relative w-full", className)}>
      <form onSubmit={handleSubmit} className="relative w-full">
        <div className="relative w-full">
          <Search className={cn("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400", config.icon)} />
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={cn("pl-10 pr-24 sm:pr-28", config.input)}
            autoFocus={autoFocus}
          />

          {/* Clear button */}
          {query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className={cn(
                "absolute top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 rounded-full",
                size === 'lg' ? 'right-16' : size === 'md' ? 'right-14' : 'right-12'
              )}
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {/* Search button */}
          <Button
            type="submit"
            className={cn(
              "absolute right-1 top-1/2 transform -translate-y-1/2 rounded-md",
              config.button
            )}
          >
            <Search className={config.icon} />
          </Button>
        </div>
      </form>

      {/* Search Popup */}
      <AnimatePresence>
        {showPopup && isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={cn(
              "absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-200 z-[100] overflow-hidden",
              config.popup
            )}
            style={{
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
            }}
          >
            <div className="max-h-80 sm:max-h-96 overflow-y-auto">
              {/* Search Suggestions */}
              {suggestions.length > 0 && (
                <div className="p-3 sm:p-4 border-b border-gray-100">
                  <div className="flex items-center gap-2 mb-3">
                    <Search className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">Suggestions</span>
                  </div>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={suggestion}
                      onClick={() => handleSearch(suggestion)}
                      className={cn(
                        "w-full text-left px-3 py-2.5 sm:py-3 rounded-lg text-sm sm:text-base hover:bg-gray-50 flex items-center justify-between transition-colors duration-150",
                        selectedIndex === index && "bg-blue-50 text-blue-700 shadow-sm"
                      )}
                    >
                      <span className="truncate pr-2">{suggestion}</span>
                      <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 opacity-50 flex-shrink-0" />
                    </button>
                  ))}
                </div>
              )}

              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div className="p-3 sm:p-4 border-b border-gray-100">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">Recent</span>
                  </div>
                  {recentSearches.map((recent, index) => (
                    <button
                      key={recent}
                      onClick={() => handleSearch(recent)}
                      className={cn(
                        "w-full text-left px-3 py-2.5 sm:py-3 rounded-lg text-sm sm:text-base hover:bg-gray-50 flex items-center justify-between transition-colors duration-150",
                        selectedIndex === suggestions.length + index && "bg-blue-50 text-blue-700 shadow-sm"
                      )}
                    >
                      <span className="truncate pr-2">{recent}</span>
                      <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 opacity-50 flex-shrink-0" />
                    </button>
                  ))}
                </div>
              )}

              {/* Popular Searches */}
              {popularTerms.length > 0 && (
                <div className="p-3 sm:p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">Popular</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {popularTerms.map((term, index) => (
                      <button
                        key={term}
                        onClick={() => handleSearch(term)}
                        className={cn(
                          "px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-100 hover:bg-gray-200 rounded-full text-sm sm:text-base text-gray-700 transition-all duration-150 hover:shadow-sm",
                          selectedIndex === suggestions.length + recentSearches.length + index && "bg-blue-100 text-blue-700 shadow-sm"
                        )}
                      >
                        {term}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* No results */}
              {suggestions.length === 0 && recentSearches.length === 0 && popularTerms.length === 0 && (
                <div className="p-6 sm:p-8 text-center text-gray-500">
                  <Search className="h-8 w-8 sm:h-10 sm:w-10 mx-auto mb-3 opacity-50" />
                  <p className="text-sm sm:text-base">Start typing to see suggestions</p>
                  <p className="text-xs sm:text-sm mt-1 opacity-75">Search for brands, categories, or coupon codes</p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedSearchBar;
