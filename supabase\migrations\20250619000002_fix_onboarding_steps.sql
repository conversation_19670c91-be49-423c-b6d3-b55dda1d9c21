-- Fix onboarding steps to match the actual onboarding flow
-- Update constraint and default values

-- Update the sanitize_onboarding_step trigger function to use correct values
CREATE OR REPLACE FUNCTION public.sanitize_onboarding_step()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Ensure the onboarding_step is one of the valid values
  IF NEW.onboarding_step IS NOT NULL AND NEW.onboarding_step NOT IN ('welcome', 'profile_setup', 'interests', 'payment_setup', 'completed') THEN
    NEW.onboarding_step := 'welcome';
  END IF;
  RETURN NEW;
END;
$$;

-- Drop the old constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS valid_onboarding_step;

-- Update existing invalid values to valid ones
UPDATE profiles SET onboarding_step = 'welcome' WHERE onboarding_step = 'signup';
UPDATE profiles SET onboarding_step = 'completed' WHERE onboarding_step = 'first_coupon';

-- Add the new constraint with correct values
ALTER TABLE profiles ADD CONSTRAINT valid_onboarding_step
CHECK (onboarding_step = ANY (ARRAY[
  'welcome'::text,
  'profile_setup'::text,
  'interests'::text,
  'payment_setup'::text,
  'completed'::text
]));

-- Update the default value
ALTER TABLE profiles ALTER COLUMN onboarding_step SET DEFAULT 'welcome';
