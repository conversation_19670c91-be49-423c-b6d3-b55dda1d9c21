import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { usePremiumCoupon } from '@/hooks/usePremiumCoupon';
import { openUrlWithCorrectDomain } from '@/utils/domainFix';

// Define a more general interface that works with both types of coupon objects
interface CouponDisplay {
  id: string;
  code: string;
  title?: string;
  is_premium?: boolean;
  status?: string;
  discount_percent?: number;
  discount_amount?: number;
  discount_description?: string;
  expires_at?: string;
  affiliate_link?: string;
  brand?: {
    id?: string;
    name?: string;
    logo_url?: string;
  };
}

interface SimpleCouponListProps {
  coupons: CouponDisplay[];
  isLoading?: boolean;
}

// Helper function to format expiration date
const formatExpirationDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

const SimpleCouponList = ({ coupons, isLoading = false }: SimpleCouponListProps) => {
  const { user } = useAuth();
  
  if (isLoading) {
    return (
      <div className="space-y-3 animate-pulse">
        <div className="h-16 bg-gray-200 rounded-lg"></div>
        <div className="h-16 bg-gray-200 rounded-lg"></div>
        <div className="h-16 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  const handleCopyCode = (coupon: CouponDisplay) => {
    // Check if this is a premium coupon that needs to be purchased
    if (coupon.is_premium) {
      // If we had the usePremiumCoupon hook here, we'd check hasPurchased
      // Since we don't want to make a separate query for each coupon,
      // we'll show a message to the user to go to the coupon detail page
      toast.error('This is a premium coupon. View details to unlock.');
      return;
    }
    
    try {
      navigator.clipboard.writeText(coupon.code);
      toast.success('Coupon code copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy: ', error);
      toast.error('Failed to copy code. Please try again.');
    }
  };

  // Handler for opening affiliate links with domain correction
  const handleOpenAffiliateLink = (link: string) => {
    if (!link) return;
    openUrlWithCorrectDomain(link);
  };

  if (!coupons || coupons.length === 0) {
    return (
      <div className="text-center py-6 bg-slate-50 rounded-lg border border-slate-200">
        <p className="text-slate-600">No active coupons available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {coupons.map((coupon, index) => (
        <div key={index} className="border-[3px] border-slate-400 rounded-xl p-4 hover:shadow-lg transition-all duration-300 bg-slate-50/50 hover:border-slate-600">
          {/* Coupon Header */}
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-md bg-white mr-2 flex-shrink-0 flex items-center justify-center shadow-sm border border-slate-200">
                {coupon.brand?.logo_url ? (
                  <img 
                    src={coupon.brand.logo_url} 
                    alt={coupon.brand?.name || 'Brand'} 
                    className="w-full h-full object-contain" 
                    onError={(e) => e.currentTarget.style.display = 'none'} 
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-400" />
                )}
              </div>
              <div>
                <div className="text-sm font-medium">{coupon.brand?.name || 'Brand'}</div>
                <div className="text-xs text-slate-500">{coupon.title || 'Discount Coupon'}</div>
              </div>
            </div>
            {coupon.is_premium ? (
              <div className="text-xs bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full">
                <span className="flex items-center">
                  <Star className="w-2.5 h-2.5 fill-amber-500 text-amber-500 mr-0.5" />
                  PREMIUM
                </span>
              </div>
            ) : (
              <div className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                FREE
              </div>
            )}
          </div>
          
          {/* Coupon Code Section */}
          <div className="flex justify-between items-center my-2 p-2 bg-slate-50 rounded-md">
            <div>
              <span className="text-xs text-slate-500">Code:</span>
              {coupon.is_premium ? (
                <span className="ml-1 font-bold text-blue-600 blur-sm select-none">••••••••</span>
              ) : (
                <span className="ml-1 font-bold text-blue-600">{coupon.code}</span>
              )}
            </div>
            <Button
              size="sm"
              variant="outline"
              className="h-7 px-2 text-xs bg-white border-slate-200"
              onClick={() => handleCopyCode(coupon)}
              disabled={coupon.is_premium}
            >
              {coupon.is_premium ? (
                <><Lock className="h-3 w-3 mr-1" /> Locked</>
              ) : (
                <><Copy className="h-3 w-3 mr-1" /> Copy</>
              )}
            </Button>
          </div>
          
          {/* Discount Information */}
          <div className="flex justify-between items-center text-xs text-slate-600">
            <div>
              {coupon.discount_percent ? (
                <span className="font-medium text-emerald-600">
                  {coupon.discount_percent}% OFF
                </span>
              ) : coupon.discount_amount ? (
                <span className="font-medium text-emerald-600">
                  ${coupon.discount_amount} OFF
                </span>
              ) : coupon.discount_description ? (
                <span className="font-medium text-emerald-600">
                  {coupon.discount_description}
                </span>
              ) : null}
              
              {coupon.expires_at && (
                <span className="ml-2 text-slate-500">
                  Expires: {formatExpirationDate(coupon.expires_at)}
                </span>
              )}
            </div>
            
            {coupon.affiliate_link && (
              <Button
                size="sm"
                variant="link"
                className="h-6 text-xs text-blue-600 p-0"
                onClick={() => handleOpenAffiliateLink(coupon.affiliate_link)}
              >
                Shop Now
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SimpleCouponList; 