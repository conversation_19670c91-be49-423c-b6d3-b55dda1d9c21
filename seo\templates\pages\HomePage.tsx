import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import HeroSection from '@/components/homepage-sections/HeroSection';
import FeaturedBrands from '@/components/homepage-sections/FeaturedBrands';
import TrendingDealsSection from '@/components/homepage-sections/TrendingDealsSection';
import PremiumOffers from '@/components/homepage-sections/PremiumOffers';
import CategoriesSection from '@/components/homepage-sections/CategoriesSection';
import PageContainer from '@/components/layout/PageContainer';
import SEO from '@/seo/components/SEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import { Helmet } from 'react-helmet-async';

/**
 * Homepage for the CouponLink platform
 * Enhanced with rich structured data for search results like Perplexity
 */
const HomePage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  
  // Handle search submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };
  
  // Additional schema for rich search results
  const carouselSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "url": "https://www.couponlink.in/brands/amazon",
        "name": "Amazon Coupons",
        "description": "Latest Amazon discount codes and promotions"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "url": "https://www.couponlink.in/brands/nike",
        "name": "Nike Coupons",
        "description": "Active Nike promo codes and offers"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "url": "https://www.couponlink.in/brands/apple",
        "name": "Apple Discounts",
        "description": "Apple store discount offers and deals"
      },
      {
        "@type": "ListItem",
        "position": 4,
        "url": "https://www.couponlink.in/categories/clothing",
        "name": "Clothing Discounts",
        "description": "Clothing and fashion coupon codes"
      }
    ]
  };
  
  return (
    <>
      {/* Basic SEO components */}
      <SEO 
        title="CouponLink - Find & Share Verified Coupon Codes and Promo Offers"
        description="Discover the best coupon codes, promo offers, and discount deals from top brands. Save money with verified promotional codes updated daily at CouponLink."
        keywords="coupon codes, promo codes, discount codes, coupon deals, promotional offers, discount vouchers, online deals, verified coupons"
      />
      
      {/* Website schema */}
      <WebsiteSchema />
      
      {/* Carousel schema for rich results */}
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(carouselSchema)}
        </script>
      </Helmet>
      
      <MainLayout fullWidth>
        <PageContainer fullWidth decorationType="landing" decorationOpacity={0.8}>
          {/* Hero Section with Search */}
          <HeroSection 
            searchQuery={searchQuery} 
            setSearchQuery={setSearchQuery} 
            handleSearch={handleSearch}
          />
          
          {/* Featured Brands Grid */}
          <FeaturedBrands limit={6} />
          
          {/* Trending Deals Carousel */}
          <TrendingDealsSection />
          
          {/* Popular Categories Grid */}
          <CategoriesSection />
          
          {/* Premium Offers with CTA */}
          <PremiumOffers />
        </PageContainer>
      </MainLayout>
    </>
  );
};

export default HomePage; 