/**
 * Ad Block Detection Utility
 * Optional utility to detect if ads are being blocked
 * Use this only if your application relies on ad revenue
 */

/**
 * Detect if ad blockers are active
 * This is a simple detection method - sophisticated ad blockers may bypass this
 */
export const detectAdBlocker = async (): Promise<boolean> => {
  try {
    // Create a test element that ad blockers typically block
    const testAd = document.createElement('div');
    testAd.innerHTML = '&nbsp;';
    testAd.className = 'adsbox';
    testAd.style.position = 'absolute';
    testAd.style.left = '-10000px';
    testAd.style.width = '1px';
    testAd.style.height = '1px';
    
    document.body.appendChild(testAd);
    
    // Wait a moment for ad blockers to process
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Check if the element was hidden or removed
    const isBlocked = testAd.offsetHeight === 0 || 
                     testAd.style.display === 'none' || 
                     testAd.style.visibility === 'hidden';
    
    // Clean up
    document.body.removeChild(testAd);
    
    return isBlocked;
  } catch (error) {
    console.warn('Ad blocker detection failed:', error);
    return false;
  }
};

/**
 * Alternative detection method using fetch
 */
export const detectAdBlockerByFetch = async (): Promise<boolean> => {
  try {
    // Try to fetch a common ad-related URL
    const response = await fetch('https://googleads.g.doubleclick.net/pagead/id', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    
    // If we get here without error, ads are likely not blocked
    return false;
  } catch (error) {
    // If fetch fails, it might be blocked
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Check for specific blocking errors
    if (errorMessage.includes('ERR_BLOCKED_BY_CLIENT') || 
        errorMessage.includes('NetworkError') ||
        errorMessage.includes('Failed to fetch')) {
      return true;
    }
    
    return false;
  }
};

/**
 * React hook for ad blocker detection
 */
export const useAdBlockerDetection = () => {
  const [isAdBlockerActive, setIsAdBlockerActive] = React.useState<boolean | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  
  React.useEffect(() => {
    const checkAdBlocker = async () => {
      try {
        setIsLoading(true);
        
        // Try both detection methods
        const [domBlocked, fetchBlocked] = await Promise.all([
          detectAdBlocker(),
          detectAdBlockerByFetch()
        ]);
        
        // If either method detects blocking, consider it blocked
        setIsAdBlockerActive(domBlocked || fetchBlocked);
      } catch (error) {
        console.warn('Ad blocker detection error:', error);
        setIsAdBlockerActive(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAdBlocker();
  }, []);
  
  return { isAdBlockerActive, isLoading };
};

/**
 * Component to show a message when ad blocker is detected
 */
export const AdBlockerNotice: React.FC<{
  onDismiss?: () => void;
  className?: string;
}> = ({ onDismiss, className = '' }) => {
  const { isAdBlockerActive, isLoading } = useAdBlockerDetection();
  const [isDismissed, setIsDismissed] = React.useState(false);
  
  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };
  
  if (isLoading || !isAdBlockerActive || isDismissed) {
    return null;
  }
  
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-blue-800">
            Ad Blocker Detected
          </h3>
          <p className="mt-1 text-sm text-blue-700">
            We notice you're using an ad blocker. Our site is supported by ads. 
            Consider whitelisting us to help keep our content free.
          </p>
        </div>
        <button
          onClick={handleDismiss}
          className="ml-3 text-blue-400 hover:text-blue-600"
          aria-label="Dismiss"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
};

/**
 * Utility to gracefully handle ad loading failures
 */
export const handleAdLoadError = (error: Error, adSlotId?: string) => {
  const errorMessage = error.message;
  
  if (errorMessage.includes('ERR_BLOCKED_BY_CLIENT')) {
    console.info(`Ad blocked by client for slot: ${adSlotId || 'unknown'}`);
    // Don't log this as an error since it's expected behavior
    return;
  }
  
  // Log other ad loading errors
  console.warn('Ad loading error:', {
    message: errorMessage,
    adSlotId,
    timestamp: new Date().toISOString()
  });
};

/**
 * Suppress ad blocker console errors
 * Call this early in your app initialization if you want to reduce console noise
 */
export const suppressAdBlockerErrors = () => {
  const originalError = console.error;
  
  console.error = (...args) => {
    const message = args.join(' ');
    
    // Suppress common ad blocker error messages
    if (message.includes('ERR_BLOCKED_BY_CLIENT') ||
        message.includes('googleads.g.doubleclick.net') ||
        message.includes('Failed to load resource')) {
      return; // Don't log these errors
    }
    
    // Log all other errors normally
    originalError.apply(console, args);
  };
};

// Import React for the hook
import React from 'react';

export default {
  detectAdBlocker,
  detectAdBlockerByFetch,
  useAdBlockerDetection,
  AdBlockerNotice,
  handleAdLoadError,
  suppressAdBlockerErrors,
};
