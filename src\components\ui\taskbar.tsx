import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate, useLocation } from 'react-router-dom';
import { Search, PlusCircle, BarChart2, CheckCircle, Play } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/context/AuthContext';
import { useOnboarding } from '@/context/OnboardingContext';
import { safeParseLocalStorage, safeSetLocalStorage } from '@/utils/localStorageHelpers';
import './taskbar.css';

interface TaskbarProps {
  onStartClick: () => void;
  isVisible: boolean;
  shouldShow?: boolean; // New prop to control scroll-based visibility
}

const Taskbar = ({ onStartClick, isVisible, shouldShow = true }: TaskbarProps) => {
  const [isCreateHovered, setIsCreateHovered] = useState(false);
  const [isCreateActive, setIsCreateActive] = useState(false);
  const [isDashboardHovered, setIsDashboardHovered] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { currentStep, isOnboardingComplete, isOnboardingInitialized } = useOnboarding();
  
  // Check if we're on the create page to keep animation active
  const isCreatePage = location.pathname === '/create-coupon';
  
  // Always animate on create page
  useEffect(() => {
    if (isCreatePage) {
      setIsCreateActive(true);
    } else {
      setIsCreateActive(false);
    }
  }, [isCreatePage]);


  
  const handleSearchClick = () => {
    // Directly navigate to search page instead of opening popup
    navigate('/search');
  };
  
  const handleNavigation = (path: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Force active state for animation
    if (path === '/create-coupon') {
      setIsCreateActive(true);
    }

    // Add slight delay to ensure UI updates complete before navigation
    setTimeout(() => {
      navigate(path);
    }, 50);
  };

  // Function to handle create button click and show animation
  const handleCreateClick = (e: React.MouseEvent) => {
    // Force it to stay active and never turn off after clicking
    setIsCreateActive(true);

    // Always animate with a permanent timeout that never resolves
    safeSetLocalStorage('createButtonClicked', true);

    handleNavigation('/create-coupon', e);
  };
  
  // Handle menu button click
  const handleMenuButtonClick = () => {
    // Call the original start menu click handler
    onStartClick();
  };
  
  // Check localStorage on mount to see if button was clicked previously
  useEffect(() => {
    const wasClicked = safeParseLocalStorage('createButtonClicked', false);
    if (wasClicked) {
      setIsCreateActive(true);
    }
  }, []);

  // Calculate onboarding progress
  const onboardingSteps = ['welcome', 'website_tour', 'start_menu_intro', 'start_menu_features', 'explore_brands', 'explore_categories', 'profile_setup', 'social_links', 'payment_setup', 'first_coupon'];
  const currentStepIndex = onboardingSteps.indexOf(currentStep);
  const progressPercentage = isOnboardingComplete ? 100 : ((currentStepIndex + 1) / onboardingSteps.length) * 100;
  const shouldShowProgress = user && isOnboardingInitialized && !isOnboardingComplete && currentStep !== 'completed';

  if (!isVisible) return null;

  return (
    <>
      {/* Onboarding Progress Bar - Moved to Top */}
      {shouldShowProgress && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[50]">
          <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1.5 shadow-lg border border-blue-100">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-blue-700">Getting Started</span>
              <div className="w-20 h-1.5 bg-blue-100 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full transition-all duration-500"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
              <span className="text-xs text-blue-600 font-medium">
                {currentStepIndex + 1}/{onboardingSteps.length}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Clean Minimalist Taskbar */}
      <div className={`
        fixed bottom-5 left-0 right-0 z-[51] flex justify-center
        transition-all duration-300 ease-in-out
        ${shouldShow ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}
      `}>
        <div className="flex items-center gap-2 bg-white/20 backdrop-blur-xl rounded-full shadow-lg border border-white/30 transition-all duration-200 px-3 py-1.5">
          {/* Menu button */}
          <div className="relative">
            <Button
              variant="ghost"
              className="w-12 h-12 md:w-14 md:h-14 rounded-full p-0 flex items-center justify-center text-gray-800 hover:bg-gray-100 active:scale-95 transition-all"
              onClick={handleMenuButtonClick}
            >
              <div className="grid grid-cols-3 gap-[3px]">
                {Array(9).fill(0).map((_, i) => (
                  <div
                    key={i}
                    className="w-1.5 h-1.5 rounded-full bg-gray-800"
                  />
                ))}
              </div>
            </Button>
          </div>
          
          <Button
            variant="ghost"
            onClick={handleCreateClick}
            onMouseEnter={() => setIsCreateHovered(true)}
            onMouseLeave={() => setIsCreateHovered(false)}
            className={`
              w-12 h-12 md:w-14 md:h-14
              rounded-full p-0
              flex items-center justify-center
              text-blue-600 hover:bg-blue-50
              active:scale-95 transition-all
              relative overflow-hidden group
              ${isCreateActive || isCreatePage ? 'bg-blue-100' : ''}
            `}
          >
            <div className={`
              absolute inset-0
              bg-gradient-to-br from-blue-100/50 to-indigo-100/50 dark:from-blue-900/20 dark:to-indigo-900/20
              rounded-full
              transform transition-all duration-200
              ${isCreateHovered ? 'opacity-100' : 'opacity-0'}
            `}></div>

            <div className="relative flex items-center justify-center scale-100 md:scale-125">
              <PlusCircle
                className={`
                  w-6 h-6
                  relative z-10
                  transform transition-all duration-200
                  text-blue-600 dark:text-blue-400
                  ${isCreateHovered ? 'scale-110' : ''}
                  filter drop-shadow-[0_0_2px_rgba(37,99,235,0.3)]
                `}
                strokeWidth={2.5}
              />
            </div>

            {(isCreateHovered || isCreatePage) && (
              <span className="absolute -bottom-7 left-1/2 transform -translate-x-1/2
                bg-gradient-to-r from-blue-600 to-indigo-600
                text-white text-[10px] py-1 px-2
                rounded-full opacity-0 group-hover:opacity-100
                transition-opacity duration-200
                whitespace-nowrap shadow-sm"
              >
                Create coupon
              </span>
            )}
          </Button>
          
          <Button
            variant="ghost"
            className={`
              relative w-12 h-12 md:w-14 md:h-14 rounded-full p-0
              flex items-center justify-center text-gray-800
              hover:bg-gray-100/80 active:scale-95 transition-all
              ${isDashboardHovered ? 'bg-gray-100/80' : ''}
              group overflow-hidden
            `}
            onClick={(e) => handleNavigation('/analytics', e)}
            onMouseEnter={() => setIsDashboardHovered(true)}
            onMouseLeave={() => setIsDashboardHovered(false)}
          >
            <div className="scale-100 md:scale-125">
              <BarChart2
                className="w-6 h-6 transform transition-all duration-200"
                strokeWidth={2.5}
              />
            </div>
            {isDashboardHovered && (
              <span className="absolute -bottom-7 left-1/2 transform -translate-x-1/2
                bg-gradient-to-r from-indigo-600 to-purple-600
                text-white text-[10px] py-1 px-2
                rounded-full opacity-0 group-hover:opacity-100
                transition-opacity duration-200
                whitespace-nowrap shadow-sm"
              >
                Analytics Dashboard
              </span>
            )}
          </Button>
          
          <Button
            variant="ghost"
            className="bg-gray-50 h-12 md:h-14 rounded-full px-4 flex items-center justify-center text-gray-800 hover:bg-gray-100 active:scale-95 transition-all"
            onClick={handleSearchClick}
          >
            <Search className="w-5 h-5 mr-2 text-gray-800" strokeWidth={2.5} />
            <span className="text-sm font-medium">Search</span>
          </Button>
        </div>
      </div>
    </>
  );
};

export default Taskbar;