#!/usr/bin/env node

/**
 * Analytics Performance Optimization Script
 * 
 * This script applies all the performance optimizations for the analytics dashboard.
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('⚡ Applying Analytics Performance Optimizations\n');

// Check if we're in the right directory
if (!fs.existsSync('supabase/config.toml')) {
  console.error('❌ Error: This script must be run from the project root directory.');
  console.error('   Make sure you\'re in the directory containing supabase/config.toml');
  process.exit(1);
}

// Function to run shell commands
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} completed successfully`);
    return output;
  } catch (error) {
    console.error(`❌ Error during ${description}:`);
    console.error(error.message);
    return null;
  }
}

// Function to check if Supabase CLI is available
function checkSupabaseCLI() {
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    console.log('ℹ️  Supabase CLI not found. You can either:');
    console.log('   1. Install it: npm install -g supabase');
    console.log('   2. Apply migrations manually through Supabase Dashboard');
    return false;
  }
}

// Function to apply database optimizations
function applyDatabaseOptimizations(hasSupabaseCLI) {
  console.log('\n🗄️  Database Performance Optimizations\n');
  
  // Check if migration exists
  const migrationFile = 'supabase/migrations/20250618000003_optimize_analytics_performance.sql';
  
  if (!fs.existsSync(migrationFile)) {
    console.error(`❌ Migration file not found: ${migrationFile}`);
    return false;
  }
  
  console.log('📁 Found analytics optimization migration:');
  console.log(`   - ${migrationFile}`);
  
  if (hasSupabaseCLI) {
    // Apply migration using CLI
    const result = runCommand('supabase db push', 'Applying analytics optimization migration');
    return result !== null;
  } else {
    // Provide manual instructions
    console.log('\n📋 Manual Migration Steps:');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the contents of:');
    console.log(`   ${migrationFile}`);
    console.log('3. Execute the migration');
    return true;
  }
}

// Function to show optimization details
function showOptimizationDetails() {
  console.log('\n⚡ Performance Optimizations Applied\n');
  
  console.log('🔍 Database Indexes Created:');
  console.log('   • idx_coupon_interactions_user_date - User analytics queries');
  console.log('   • idx_coupon_interactions_type_date - Interaction type filtering');
  console.log('   • idx_premium_purchases_buyer_date - Revenue analytics');
  console.log('   • idx_profile_views_profile_date - Profile analytics');
  console.log('   • idx_coupons_influencer_views - Top coupons queries\n');
  
  console.log('🚀 RPC Functions Created:');
  console.log('   • get_interaction_stats() - Aggregated interaction data');
  console.log('   • get_revenue_stats() - Aggregated revenue data');
  console.log('   • get_daily_analytics_summary() - Daily analytics data\n');
  
  console.log('📊 Materialized View Created:');
  console.log('   • analytics_summary - Pre-computed user statistics\n');
  
  console.log('⚙️  Frontend Optimizations:');
  console.log('   • Reduced database queries from 8+ to 4');
  console.log('   • Enhanced React Query caching (10min stale time)');
  console.log('   • Fallback strategy for reliability');
  console.log('   • Optimized data processing\n');
}

// Function to show testing instructions
function showTestingInstructions() {
  console.log('\n🧪 Testing Performance Improvements\n');
  
  console.log('1. Test the optimizations:');
  console.log('   node scripts/test-analytics-performance.js\n');
  
  console.log('2. Check analytics page loading time:');
  console.log('   • Navigate to /analytics in your app');
  console.log('   • Open browser DevTools > Network tab');
  console.log('   • Refresh the page and measure load time\n');
  
  console.log('3. Expected improvements:');
  console.log('   • Load time: 3-5s → 500ms-1s');
  console.log('   • Database queries: 8+ → 4 optimized');
  console.log('   • Data transfer: 70% reduction\n');
}

// Function to show maintenance instructions
function showMaintenanceInstructions() {
  console.log('\n🔧 Maintenance Instructions\n');
  
  console.log('📅 Regular Maintenance:');
  console.log('   • Weekly: Monitor query performance');
  console.log('   • Monthly: Refresh materialized views');
  console.log('   • Quarterly: Review and optimize indexes\n');
  
  console.log('🔄 Refresh Analytics Summary (if needed):');
  console.log('   SELECT public.refresh_analytics_summary();\n');
  
  console.log('📈 Monitor Performance:');
  console.log('   • Check slow queries in Supabase Dashboard');
  console.log('   • Use EXPLAIN ANALYZE for query optimization');
  console.log('   • Monitor index usage statistics\n');
}

// Function to show troubleshooting tips
function showTroubleshooting() {
  console.log('\n🔧 Troubleshooting\n');
  
  console.log('If analytics are still slow:');
  console.log('1. Verify migrations were applied:');
  console.log('   \\d+ coupon_interactions (in Supabase SQL editor)');
  console.log('2. Check if indexes exist:');
  console.log('   \\di+ idx_coupon_interactions_user_date');
  console.log('3. Run table analysis:');
  console.log('   ANALYZE coupon_interactions;');
  console.log('4. Check browser console for RPC function errors');
  console.log('5. Test with: node scripts/test-analytics-performance.js\n');
}

// Main execution
async function main() {
  // Check prerequisites
  const hasSupabaseCLI = checkSupabaseCLI();
  
  // Apply database optimizations
  const dbSuccess = applyDatabaseOptimizations(hasSupabaseCLI);
  
  if (dbSuccess) {
    if (hasSupabaseCLI) {
      console.log('\n✅ Analytics performance optimizations applied successfully!');
    } else {
      console.log('\n📋 Analytics optimization migration is ready for manual application.');
    }
  } else {
    console.log('\n❌ Analytics optimization failed. Please check the errors above.');
  }
  
  // Show optimization details
  showOptimizationDetails();
  
  // Show testing instructions
  showTestingInstructions();
  
  // Show maintenance instructions
  showMaintenanceInstructions();
  
  // Show troubleshooting tips
  showTroubleshooting();
  
  console.log('\n🎉 Analytics performance optimization process completed!');
  console.log('📖 For detailed information, see: ANALYTICS_PERFORMANCE_OPTIMIZATION.md');
}

// Run the script
main().catch(error => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});
