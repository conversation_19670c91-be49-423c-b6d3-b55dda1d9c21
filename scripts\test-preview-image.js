#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test search preview image setup
 * Checks if the preview image exists and validates meta tags
 */

const fs = require('fs');
const path = require('path');

console.log('🖼️  Testing Search Preview Image Setup...\n');

// Check if preview image exists
const previewImagePath = 'public/images/couponlink-search-preview.png';
const imageExists = fs.existsSync(previewImagePath);

console.log('📁 File Check:');
if (imageExists) {
    const stats = fs.statSync(previewImagePath);
    const fileSizeKB = Math.round(stats.size / 1024);
    console.log(`✅ Preview image exists: ${previewImagePath}`);
    console.log(`📊 File size: ${fileSizeKB} KB`);
    
    if (fileSizeKB > 1024) {
        console.log('⚠️  Warning: Image is larger than 1MB, consider optimizing');
    } else {
        console.log('✅ File size is optimal');
    }
} else {
    console.log(`❌ Preview image missing: ${previewImagePath}`);
    console.log('📝 To create the image:');
    console.log('   1. Open: public/tools/create-preview-image.html in your browser');
    console.log('   2. Take a screenshot or save the preview');
    console.log('   3. Save as: public/images/couponlink-search-preview.png');
}

console.log('');

// Check if images directory exists
const imagesDir = 'public/images';
if (!fs.existsSync(imagesDir)) {
    console.log('📁 Creating images directory...');
    fs.mkdirSync(imagesDir, { recursive: true });
    console.log('✅ Images directory created');
} else {
    console.log('✅ Images directory exists');
}

console.log('');

// List existing images
console.log('🖼️  Existing Images:');
try {
    const files = fs.readdirSync('public/images');
    if (files.length === 0) {
        console.log('   No images found');
    } else {
        files.forEach(file => {
            const filePath = path.join('public/images', file);
            const stats = fs.statSync(filePath);
            const fileSizeKB = Math.round(stats.size / 1024);
            console.log(`   📄 ${file} (${fileSizeKB} KB)`);
        });
    }
} catch (error) {
    console.log('   Error reading images directory');
}

console.log('');

// Check SEO component configuration
console.log('🔧 SEO Configuration:');
try {
    const seoComponentPath = 'src/seo/components/SEO.tsx';
    if (fs.existsSync(seoComponentPath)) {
        const seoContent = fs.readFileSync(seoComponentPath, 'utf8');
        
        if (seoContent.includes('couponlink-search-preview.png')) {
            console.log('✅ SEO component configured for preview image');
        } else {
            console.log('⚠️  SEO component may need preview image configuration');
        }
        
        if (seoContent.includes('og:image:width')) {
            console.log('✅ Open Graph image dimensions configured');
        } else {
            console.log('⚠️  Open Graph image dimensions missing');
        }
        
        if (seoContent.includes('twitter:image:alt')) {
            console.log('✅ Twitter Card alt text configured');
        } else {
            console.log('⚠️  Twitter Card alt text missing');
        }
    } else {
        console.log('❌ SEO component not found');
    }
} catch (error) {
    console.log('❌ Error checking SEO component');
}

console.log('');

// Provide testing URLs
console.log('🧪 Testing URLs:');
console.log('After deploying, test your preview image with:');
console.log('');
console.log('🔗 Facebook Debugger:');
console.log('   https://developers.facebook.com/tools/debug/');
console.log('   Enter: https://couponlink.in');
console.log('');
console.log('🐦 Twitter Card Validator:');
console.log('   https://cards-dev.twitter.com/validator');
console.log('   Enter: https://couponlink.in');
console.log('');
console.log('💼 LinkedIn Post Inspector:');
console.log('   https://www.linkedin.com/post-inspector/');
console.log('   Enter: https://couponlink.in');
console.log('');

// Summary
console.log('📋 Summary:');
if (imageExists) {
    console.log('✅ Preview image setup is ready!');
    console.log('🚀 Next steps:');
    console.log('   1. Deploy your website');
    console.log('   2. Test with the URLs above');
    console.log('   3. Wait 1-2 weeks for Google to update search results');
} else {
    console.log('⏳ Preview image needs to be created');
    console.log('📝 Next steps:');
    console.log('   1. Open public/tools/create-preview-image.html');
    console.log('   2. Create and save the preview image');
    console.log('   3. Deploy your website');
    console.log('   4. Test with social media validators');
}

console.log('');
console.log('🎯 Expected Result:');
console.log('When people search for "couponlink" or your website URL,');
console.log('they will see your custom preview image on the right side');
console.log('of the search results, just like in your screenshot!');
