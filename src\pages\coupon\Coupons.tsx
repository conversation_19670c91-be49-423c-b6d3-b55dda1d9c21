import { useState } from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Button } from '@/components/ui/button';
import { Plus, Filter, Ticket } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useCoupons } from '@/hooks/useCoupons';
import CouponCard from '@/components/CouponCard';
import CouponsSkeleton from '@/components/CouponsSkeleton';

const Coupons = () => {
  const [sortBy, setSortBy] = useState("newest");
  const { data: coupons, isLoading, error } = useCoupons({
    limit: 50,
    includeRelations: true
  });
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="All Coupons"
          subtitle="Browse and discover all available coupon codes and deals"
          icon={Ticket}
        >
          <div className="flex items-center gap-3">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="expiring">Expiring Soon</SelectItem>
                <SelectItem value="discount">Highest Discount</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>

            <Link to="/create-coupon">
              <Button className="bg-brand-blue hover:bg-brand-blue/90">
                <Plus className="mr-2 h-4 w-4" />
                Add Coupon
              </Button>
            </Link>
          </div>
        </PageHeaderWithBackButton>
        
        {/* Coupons grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <CouponsSkeleton count={12} />
          </div>
        ) : error ? (
          <div className="text-center py-12 bg-red-50 rounded-lg">
            <h3 className="text-lg font-medium mb-2 text-red-800">Error Loading Coupons</h3>
            <p className="text-red-600">{error.message}</p>
          </div>
        ) : coupons && coupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {coupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                id={coupon.id}
                brandName={coupon.brand?.name || "Unknown Brand"}
                brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                influencerName={coupon.influencer?.full_name || ""}
                influencerImage={coupon.influencer?.avatar_url || undefined}
                discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                couponCode={coupon.code}
                category={coupon.category?.name || "Uncategorized"}
                featured={coupon.featured}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-2">No Coupons Found</h3>
            <p className="text-gray-600">Try adjusting your filters or add a new coupon.</p>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default Coupons;
