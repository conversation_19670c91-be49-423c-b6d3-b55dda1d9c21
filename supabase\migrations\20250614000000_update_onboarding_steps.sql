-- Update onboarding steps to include new website overview steps
-- This migration adds new onboarding steps for a comprehensive website tour

-- First, drop the existing constraint if it exists
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS valid_onboarding_step;

-- Add the updated constraint with new onboarding steps including start menu
ALTER TABLE public.profiles
ADD CONSTRAINT valid_onboarding_step
CHECK (
  onboarding_step IS NULL OR
  onboarding_step IN (
    'welcome',
    'website_tour',
    'start_menu_intro',
    'start_menu_features',
    'explore_brands',
    'explore_categories',
    'profile_setup',
    'social_links',
    'payment_setup',
    'first_coupon',
    'completed'
  )
);

-- Update any existing profiles that might have the old 'signup' step to 'welcome'
UPDATE public.profiles 
SET onboarding_step = 'welcome' 
WHERE onboarding_step = 'signup';

-- Add comment explaining the new onboarding flow
COMMENT ON CONSTRAINT valid_onboarding_step ON public.profiles IS 
'Enhanced onboarding flow: welcome -> website_tour -> explore_brands -> explore_categories -> profile_setup -> social_links -> payment_setup -> first_coupon -> completed';
