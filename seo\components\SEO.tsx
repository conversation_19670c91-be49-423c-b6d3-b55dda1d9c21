import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  type?: 'website' | 'article';
  canonicalPath?: string;
  noindex?: boolean;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  alternateUrls?: { [key: string]: string };
  breadcrumbs?: Array<{ name: string; url: string }>;
}

/**
 * Enhanced SEO component that handles all metadata needs
 * Improved to include more metadata for better search visibility
 */
const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  image = '/logos/logo.png',
  type = 'website',
  canonicalPath,
  noindex = false,
  publishedTime,
  modifiedTime,
  author = 'CouponLink',
  section,
  alternateUrls,
  breadcrumbs
}) => {
  const location = useLocation();
  
  // Ensure canonicalPath starts with a forward slash
  const formattedCanonicalPath = canonicalPath 
    ? (canonicalPath.startsWith('/') ? canonicalPath : `/${canonicalPath}`)
    : location.pathname;
  
  // Construct the full canonical URL
  const canonical = `https://www.couponlink.in${formattedCanonicalPath}`;
  
  // Ensure image is a full URL
  const fullImageUrl = image.startsWith('http') 
    ? image 
    : `https://www.couponlink.in${image.startsWith('/') ? image : `/${image}`}`;

  // Generate breadcrumb schema if provided
  const breadcrumbSchema = breadcrumbs ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  } : null;
  
  return (
    <Helmet>
      {/* Basic SEO */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      <meta name="author" content={author} />
      
      {/* Canonical link */}
      <link rel="canonical" href={canonical} />
      
      {/* Alternate language/region URLs */}
      {alternateUrls && Object.entries(alternateUrls).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}
      
      {/* Robots control */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <>
          <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
          <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
          <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        </>
      )}
      
      {/* Article specific metadata */}
      {type === 'article' && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          <meta property="article:author" content={author} />
        </>
      )}
      
      {/* OpenGraph tags for social sharing */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={canonical} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="CouponLink" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@CouponLink" />
      <meta name="twitter:creator" content="@CouponLink" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      
      {/* Viewport settings */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      
      {/* Mobile web app capability */}
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Breadcrumb schema if provided */}
      {breadcrumbSchema && (
        <script type="application/ld+json">
          {JSON.stringify(breadcrumbSchema)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO; 