import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import CouponCard from '@/components/CouponCard';
import { useNavigate } from 'react-router-dom';
import { usePremiumCoupons } from '@/hooks/useCoupons';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Award } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

const PremiumDeals = () => {
  const navigate = useNavigate();
  const { data: premiumCoupons, isLoading, error } = usePremiumCoupons(12); // Get up to 12 premium coupons
  const { user } = useAuth();
  const [purchasedCoupons, setPurchasedCoupons] = useState<Record<string, boolean>>({});
  
  // Fetch purchased premium coupons when component mounts
  useEffect(() => {
    const fetchPurchasedCoupons = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('premium_purchases')
          .select('coupon_id')
          .eq('buyer_id', user.id);
        
        if (error) throw error;
        
        const purchasedMap: Record<string, boolean> = {};
        data.forEach(item => {
          purchasedMap[item.coupon_id] = true;
        });
        
        setPurchasedCoupons(purchasedMap);
      } catch (error) {
        console.error('Error fetching purchased coupons:', error);
      }
    };
    
    fetchPurchasedCoupons();
  }, [user]);
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Premium Deals"
          subtitle="Exclusive premium deals with higher discounts and special offers"
          icon={Award}
        />

        {/* Deals Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <CouponsSkeleton count={12} />
          </div>
        ) : error ? (
          <div className="w-full flex justify-center items-center bg-red-50 dark:bg-red-900/20 rounded-lg p-10 shadow-sm">
            <p className="text-red-600 dark:text-red-400">There was an error loading premium deals. Please try again later.</p>
          </div>
        ) : premiumCoupons && premiumCoupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {premiumCoupons.map((coupon) => (
              <motion.div
                key={coupon.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ y: -5 }}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
              >
                <CouponCard
                  id={coupon.id}
                  brandName={coupon.brand?.name || "Unknown Brand"}
                  brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                  influencerName={coupon.influencer?.full_name || "Anonymous"}
                  influencerImage={coupon.influencer?.avatar_url}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "General"}
                  featured={coupon.featured}
                  isPremium={coupon.is_premium}
                  brandId={coupon.brand?.id}
                  price={coupon.price}
                  isLocked={!user || !purchasedCoupons[coupon.id]}
                />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="w-full flex justify-center items-center bg-white dark:bg-gray-800/20 rounded-lg p-10 shadow-sm">
            <p className="text-gray-600 dark:text-gray-400">No premium deals available at the moment.</p>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default PremiumDeals; 