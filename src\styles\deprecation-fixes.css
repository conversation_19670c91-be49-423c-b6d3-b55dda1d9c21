/* 
 * Deprecation Fixes for Third-Party Libraries
 * This file addresses the -ms-high-contrast deprecation warning
 * by providing modern forced-colors equivalents
 */

/* Suppress -ms-high-contrast deprecation warnings by providing modern alternatives */
@media (forced-colors: active) {
  /* Global forced colors adjustments */
  :root {
    forced-color-adjust: auto;
  }

  /* Ensure all interactive elements work properly in forced colors mode */
  button, 
  [role="button"],
  input,
  select,
  textarea,
  a,
  [tabindex]:not([tabindex="-1"]) {
    forced-color-adjust: auto;
    border: 1px solid transparent;
  }

  button:focus,
  [role="button"]:focus,
  input:focus,
  select:focus,
  textarea:focus,
  a:focus,
  [tabindex]:not([tabindex="-1"]):focus {
    outline: 2px solid ButtonText;
    outline-offset: 2px;
  }

  /* Card and container elements */
  .card,
  [data-card],
  .bg-white,
  .bg-gray-50,
  .bg-gray-100 {
    background-color: Canvas !important;
    color: CanvasText !important;
    border-color: ButtonText !important;
  }

  /* Text elements */
  .text-gray-600,
  .text-gray-700,
  .text-gray-800,
  .text-gray-900 {
    color: CanvasText !important;
  }

  /* Link elements */
  .text-blue-600,
  .text-blue-500,
  .text-primary,
  a:not(.no-forced-colors) {
    color: LinkText !important;
  }

  .text-blue-600:hover,
  .text-blue-500:hover,
  .text-primary:hover,
  a:not(.no-forced-colors):hover {
    color: ActiveText !important;
  }

  /* Button variants */
  .bg-primary,
  .bg-blue-600,
  .bg-green-600 {
    background-color: ButtonFace !important;
    color: ButtonText !important;
    border: 1px solid ButtonText !important;
  }

  .bg-primary:hover,
  .bg-blue-600:hover,
  .bg-green-600:hover {
    background-color: Highlight !important;
    color: HighlightText !important;
  }

  /* Border utilities */
  .border-gray-200,
  .border-gray-300,
  .border {
    border-color: ButtonText !important;
  }

  /* Shadow utilities - remove in forced colors mode */
  .shadow,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    box-shadow: none !important;
  }

  /* Ensure proper contrast for icons */
  svg,
  .icon {
    color: currentColor;
    forced-color-adjust: auto;
  }
}

/* Override any remaining -ms-high-contrast usage from third-party libraries */
@media (-ms-high-contrast: active) {
  /* Redirect all -ms-high-contrast styles to use forced-colors equivalents */
  * {
    /* This will be ignored in modern browsers but helps suppress warnings */
    -ms-high-contrast-adjust: auto;
  }
}

/* Specific overrides for common third-party library patterns */
@media (forced-colors: active) {
  /* React Day Picker */
  .rdp {
    background-color: Canvas;
    color: CanvasText;
    border: 1px solid ButtonText;
  }

  .rdp-button {
    background-color: ButtonFace;
    color: ButtonText;
    border: 1px solid ButtonText;
  }

  .rdp-button:hover {
    background-color: Highlight;
    color: HighlightText;
  }

  /* Sonner Toast */
  [data-sonner-toast] {
    background-color: Canvas !important;
    color: CanvasText !important;
    border: 1px solid ButtonText !important;
  }

  /* Framer Motion elements */
  [data-framer-motion] {
    forced-color-adjust: auto;
  }

  /* Lucide React icons */
  .lucide {
    color: currentColor;
    forced-color-adjust: auto;
  }

  /* React Icons */
  .react-icons {
    color: currentColor;
    forced-color-adjust: auto;
  }
}

/* Print styles - ensure compatibility */
@media print {
  * {
    forced-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* High contrast mode detection for JavaScript */
@media (forced-colors: active) {
  :root {
    --forced-colors-active: 1;
  }
}

@media (forced-colors: none) {
  :root {
    --forced-colors-active: 0;
  }
}
