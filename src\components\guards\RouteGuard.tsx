import { ReactNode, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useOnboarding } from '@/context/OnboardingContext';

interface RouteGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  allowOnboarding?: boolean;
}

const RouteGuard = ({ children, requireAuth = true, allowOnboarding = false }: RouteGuardProps) => {
  const { user, profile, loading } = useAuth();
  const { currentStep, isOnboardingComplete } = useOnboarding();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Don't do anything while authentication is still loading
    if (loading) {
      console.log('RouteGuard: Auth still loading, waiting...');
      return;
    }

    const checkAuth = async () => {
      console.log('RouteGuard check for path:', location.pathname);
      console.log('User status:', user ? 'Logged in' : 'Not logged in');
      console.log('Loading status:', loading);

      // Only enforce authentication after loading is complete
      if (requireAuth && !user && location.pathname !== '/auth') {
        console.log('→ Redirecting to /auth - auth required but user not logged in');
        navigate('/auth');
        return;
      }

      // If we're on the auth page and have a user, let Auth component handle it
      if (location.pathname === '/auth' && user) {
        console.log('→ On /auth with user - leaving redirect to Auth component');
        return;
      }

      console.log('→ Allowing access to:', location.pathname);
    };

    checkAuth();
  }, [user, profile, loading, isOnboardingComplete, requireAuth, allowOnboarding, navigate, currentStep, location.pathname, location.search]);

  // Show loading state while auth is being determined
  if (loading) {
    return null; // or a loading spinner if you prefer
  }

  // Show nothing while checking auth (after loading is complete)
  if (requireAuth && !user) {
    return null;
  }

  return <>{children}</>;
};

export default RouteGuard; 