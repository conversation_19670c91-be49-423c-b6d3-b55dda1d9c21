/// <reference types="https://deno.land/x/deno_types/index.d.ts" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const supabaseUrl = "https://oewgwxxajssonxavydbx.supabase.co";
// Use a safer way to access environment variables that works in both Deno and other environments
const getEnv = (key: string) => {
  // @ts-ignore - Deno exists in edge functions environment
  return typeof Deno !== 'undefined' ? Deno.env.get(key) : process.env[key];
};
const supabaseKey = getEnv("SUPABASE_ANON_KEY") || "";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Simple health check endpoint
    if (req.url.endsWith('/health')) {
      return new Response(
        JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Create a Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get any auth header if available
    const authHeader = req.headers.get("Authorization");
    let userId = null;
    
    if (authHeader) {
      // If authenticated, get the user ID
      const { data, error } = await supabase.auth.getUser(authHeader.replace("Bearer ", ""));
      if (!error && data?.user) {
        userId = data.user.id;
      }
    }
    
    console.log("Starting to parse request body");
    let reqBody;
    try {
      // Parse the request body - ONLY ONCE
      reqBody = await req.json();
      console.log("Request body parsed:", JSON.stringify(reqBody));
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      return new Response(
        JSON.stringify({ success: false, error: "Invalid JSON in request body" }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400
        }
      );
    }
    
    // Check for debug mode
    const isDebugMode = reqBody.debug === true;
    
    const { type, id, referrer, interaction = "view" } = reqBody;
    
    if (!type || !id) {
      console.error("Missing required fields:", { body: reqBody });
      throw new Error("Missing required fields: type and id");
    }
    
    // Get client info
    const userAgent = req.headers.get("user-agent") || "";
    const clientIp = req.headers.get("x-forwarded-for") || "";
    
    // Current timestamp for occurred_at field
    const currentTimestamp = new Date().toISOString();
    console.log("Generated timestamp:", currentTimestamp);
    
    // In debug mode, just return what would be inserted without actually inserting
    if (isDebugMode) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          debug: true,
          operation: type === "profile" ? "profile_view" : "coupon_interaction",
          payload: type === "profile" 
            ? {
                profile_id: id,
                viewer_id: userId,
                ip_address: clientIp,
                user_agent: userAgent,
                referrer: referrer || null
              }
            : {
                coupon_id: id,
                user_id: userId,
                interaction_type: interaction,
                ip_address: clientIp,
                user_agent: userAgent,
                occurred_at: currentTimestamp
              }
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }
    
    // Track based on type
    if (type === "profile") {
      console.log("Recording profile view for profile_id:", id);
      // Record profile view
      const { error } = await supabase
        .from("profile_views")
        .insert({
          profile_id: id,
          viewer_id: userId,
          ip_address: clientIp,
          user_agent: userAgent,
          referrer: referrer || null
        });
        
      if (error) {
        console.error("Error recording profile view:", error);
        // Just log the error but don't throw it - to avoid breaking the client
        // return a success response to the client anyway
      }
    } else if (type === "coupon") {
      console.log("Recording coupon interaction for coupon_id:", id, "interaction_type:", interaction);
      
      // For coupon interactions, try to get the table structure first
      try {
        const { error: tableError, data: tableInfo } = await supabase
          .rpc('get_table_structure', { table_name: 'coupon_interactions' });
          
        if (!tableError && tableInfo) {
          console.log("Table structure for coupon_interactions:", tableInfo);
        } else {
          console.error("Error getting table structure:", tableError);
        }
      } catch (structError) {
        console.error("Error checking table structure:", structError);
      }
      
      // Record coupon interaction
      const payload = {
        coupon_id: id,
        user_id: userId,
        interaction_type: interaction,
        ip_address: clientIp,
        user_agent: userAgent,
        occurred_at: currentTimestamp
      };
      console.log("Coupon interaction payload:", JSON.stringify(payload));
      
      // Record coupon interaction
      const { error, data } = await supabase
        .from("coupon_interactions")
        .insert(payload);
        
      if (error) {
        console.error("Error recording coupon interaction:", error);
        // Don't throw the error - just log it and return success to the client
        // This will allow the application to continue working even if analytics fail
      } else {
        console.log("Coupon interaction recorded successfully");
      }
    } else {
      console.error("Invalid tracking type:", type);
      throw new Error("Invalid tracking type");
    }
    
    // Always return success to the client
    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error tracking view:", error.message, error.stack);
    
    // Return a success response anyway to avoid breaking the client
    return new Response(
      JSON.stringify({ 
        success: true,
        error_logged: true,
        message: "Error logged but continuing normally"
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200 // Return 200 OK even on error
      }
    );
  }
});
