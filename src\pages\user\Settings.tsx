import { useState, useEffect, useRef } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { 
  User, 
  Bell, 
  Shield, 
  UserCircle, 
  Upload, 
  Trash2, 
  Plus, 
  X, 
  Twitter, 
  Instagram, 
  Facebook, 
  Youtube, 
  Twitch,
  Link,
  Loader2,
  Settings as SettingsIcon
} from 'lucide-react';
import { FaTwitter, FaInstagram, FaYoutube, FaTiktok, FaFacebookF, FaTwitch } from 'react-icons/fa';
import { Card, CardContent } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { v4 } from 'uuid';
import { COLORS } from '@/constants/theme';
import { motion, AnimatePresence } from 'framer-motion';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { useOnboarding } from '@/context/OnboardingContext';
import { useLocation } from 'react-router-dom';

// Define the social platform options
const socialPlatforms = [
  { id: 'twitter', name: 'Twitter', icon: <FaTwitter className="w-5 h-5 text-blue-400" /> },
  { id: 'instagram', name: 'Instagram', icon: <FaInstagram className="w-5 h-5 text-pink-500" /> },
  { id: 'youtube', name: 'YouTube', icon: <FaYoutube className="w-5 h-5 text-red-500" /> },
  { id: 'tiktok', name: 'TikTok', icon: <FaTiktok className="w-5 h-5 text-black" /> },
  { id: 'facebook', name: 'Facebook', icon: <FaFacebookF className="w-5 h-5 text-blue-600" /> },
  { id: 'twitch', name: 'Twitch', icon: <FaTwitch className="w-5 h-5 text-purple-500" /> },
];

const Settings = () => {
  const { user, profile, refreshProfile } = useAuth();
  const { currentStep, isOnboardingComplete, handleStepCompletion, updateStep } = useOnboarding();
  const [loading, setLoading] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState("profile");
  const didInitRef = useRef(false);
  const location = useLocation();
  
  // Social links state
  const [socialLinks, setSocialLinks] = useState<Array<{platform: string; url: string}>>([]);
  
  // Get the current onboarding context from URL params
  const searchParams = new URLSearchParams(location.search);
  const isOnboarding = searchParams.get('onboarding') === 'true';
  const onboardingStep = searchParams.get('step');

  // Form state
  const [formData, setFormData] = useState({
    fullName: '',
    username: '',
    bio: '',
    website: '',
    payment_id: '',
    payment_type: 'UPI',
  });

  // Initialize the active tab indicator and set the tab based on onboarding step
  useEffect(() => {
    // Set tab based on onboarding step if in onboarding mode
    if (isOnboarding && onboardingStep) {
      if (onboardingStep === 'profile_setup') {
        setActiveTab('profile');
      } else if (onboardingStep === 'social_links') {
        setActiveTab('profile'); // Social links are under profile tab
      } else if (onboardingStep === 'payment_setup') {
        setActiveTab('profile'); // Payment setup is under profile tab
      }
    }

    if (!didInitRef.current) {
      // Set timeout to ensure DOM is fully loaded
      const timer = setTimeout(() => {
        const indicators = document.querySelectorAll('.tab-indicator');
        indicators.forEach((indicator) => {
          const parent = indicator.parentElement;
          if (parent && parent.getAttribute('value') === activeTab) {
            indicator.setAttribute('data-state', 'active');
          } else {
            indicator.setAttribute('data-state', 'inactive');
          }
        });
        didInitRef.current = true;
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [isOnboarding, onboardingStep, activeTab]);

  // Load profile data when component mounts
  useEffect(() => {
    if (profile) {
      setFormData({
        fullName: profile.full_name || '',
        username: profile.username || '',
        bio: profile.bio || '',
        website: profile.website || '',
        payment_id: profile.payment_id || '',
        payment_type: profile.payment_type || 'UPI',
      });
      setAvatarPreview(profile.avatar_url);
      setSocialLinks(profile.social_links || []);
    }
  }, [profile]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));

    // Clear validation error when user types
    if (validationErrors[id]) {
      setValidationErrors(prev => ({ ...prev, [id]: '' }));
    }
  };
  
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  const addSocialLink = () => {
    setSocialLinks([...socialLinks, { platform: 'twitter', url: '' }]);
  };
  
  const removeSocialLink = (index: number) => {
    const newLinks = [...socialLinks];
    newLinks.splice(index, 1);
    setSocialLinks(newLinks);
  };
  
  const updateSocialLink = (index: number, field: 'platform' | 'url', value: string) => {
    const newLinks = [...socialLinks];
    newLinks[index][field] = value;
    setSocialLinks(newLinks);
  };
  
  // Validate the form data
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.fullName.trim()) {
      errors.fullName = 'Full name is required';
    }
    
    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username = 'Username can only contain letters, numbers, and underscores';
    }
    
    if (formData.website && !formData.website.startsWith('http')) {
      errors.website = 'Website must start with http:// or https://';
    }
    
    // Payment ID validation based on payment type
    if (formData.payment_id) {
      if (formData.payment_type === 'UPI' && !formData.payment_id.includes('@')) {
        errors.payment_id = 'UPI ID should include @ symbol';
      } else if (formData.payment_type === 'PayPal' && !formData.payment_id.includes('@')) {
        errors.payment_id = 'PayPal requires a valid email address';
      }
    }
    
    // Validate social links
    let validSocialLinks = true;
    socialLinks.forEach((link, index) => {
      if (link.url && !link.url.startsWith('http')) {
        errors[`socialLink${index}`] = 'URL must start with http:// or https://';
        validSocialLinks = false;
      }
    });
    
    // Filter out empty social links
    if (validSocialLinks) {
      const filteredLinks = socialLinks.filter(link => link.url.trim() !== '');
      setSocialLinks(filteredLinks);
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Upload avatar to storage and get URL
  const uploadAvatar = async (userId: string): Promise<string | null> => {
    if (!avatarFile) {
      // If no new avatar file, return the current preview or null
      return avatarPreview;
    }
    
    try {
      // Create a unique file name for the avatar
      const fileExt = avatarFile.name.split('.').pop();
      const fileName = `${userId}/${v4()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;
      
      // Check file size and type
      if (avatarFile.size > 2 * 1024 * 1024) { // 2MB limit
        throw new Error('Image size should be less than 2MB');
      }
      
      // Accept only image files
      if (!avatarFile.type.startsWith('image/')) {
        throw new Error('Only image files are allowed');
      }
      
      // Upload the file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, avatarFile, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw new Error(uploadError.message || 'Failed to upload profile image');
      }
      
      // Get the public URL for the uploaded file
      const { data } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);
      
      if (!data || !data.publicUrl) {
        throw new Error('Failed to get public URL for uploaded image');
      }
      
      return data.publicUrl;
    } catch (error: any) {
      console.error('Error uploading avatar:', error);
      toast.error(error.message || 'Failed to upload profile image');
      return avatarPreview; // Fallback to current preview on error
    }
  };
  
  // Update social links in the database
  const updateSocialLinks = async (userId: string) => {
    try {
      // First, delete the existing social links
      const { error: deleteError } = await supabase
        .from('social_links')
        .delete()
        .eq('profile_id', userId);
      
      if (deleteError) {
        throw deleteError;
      }
      
      // Then, insert the new ones if there are any
      if (socialLinks.length > 0) {
        const socialLinksToInsert = socialLinks.map(link => ({
          profile_id: userId,
          platform: link.platform,
          url: link.url,
        }));
        
        const { error: insertError } = await supabase
          .from('social_links')
          .insert(socialLinksToInsert);
        
        if (insertError) {
          throw insertError;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error updating social links:', error);
      toast.error('Failed to update social links');
      return false;
    }
  };
  
  const handleSave = async () => {
    // Validate username
    if (!formData.username) {
      setValidationErrors(prev => ({ ...prev, username: 'Username is required' }));
      return;
    }
    
    setLoading(true);
    setValidationErrors({});
    
    try {
      // Update the profile
      const { error } = await supabase
        .from('profiles')
        .update({
          username: formData.username,
          display_name: formData.fullName,
          bio: formData.bio,
          website: formData.website,
          payment_id: formData.payment_id,
          payment_type: formData.payment_type,
          updated_at: new Date().toISOString(),
          social_links: socialLinks.length > 0 ? socialLinks : null,
        })
        .eq('id', user?.id);
      
      if (error) throw error;
      
      // Upload avatar if it exists
      if (avatarFile) {
        const fileExt = avatarFile.name.split('.').pop();
        const filePath = `avatars/${user?.id}-${Date.now()}.${fileExt}`;
        
        const { error: uploadError } = await supabase.storage
          .from('profiles')
          .upload(filePath, avatarFile);
        
        if (uploadError) throw uploadError;
        
        // Update profile with avatar URL
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            avatar_url: filePath
          })
          .eq('id', user?.id);
        
        if (updateError) throw updateError;
      }
      
      // Check if this was part of the onboarding profile_setup step
      if (isOnboarding && onboardingStep === 'profile_setup') {
        // Complete the profile_setup step
        await handleStepCompletion('profile_setup', {
          display_name: formData.fullName,
          bio: formData.bio,
          website: formData.website
        });
        
        toast.success('Profile updated and onboarding step completed!');
      } else {
        toast.success('Profile updated successfully!');
      }
      
      // Refresh profile data
      await refreshProfile();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <MainLayout>
      <PageContainer>
        {/* Page header */}
        <PageHeaderWithBackButton
          title="Settings"
          subtitle="Customize your profile and preferences"
          icon={SettingsIcon}
        />
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Tabs 
            defaultValue="profile" 
            className="w-full rounded-xl p-6"
            onValueChange={(value) => {
              setActiveTab(value);
              // Find all indicator elements and update their state
              const indicators = document.querySelectorAll('.tab-indicator');
              indicators.forEach((indicator) => {
                const parent = indicator.parentElement;
                if (parent && parent.getAttribute('value') === value) {
                  indicator.setAttribute('data-state', 'active');
                } else {
                  indicator.setAttribute('data-state', 'inactive');
                }
              });
            }}
          >
            <TabsList className="flex flex-row justify-center mb-8 bg-transparent gap-1 sm:gap-2">
              <TabsTrigger 
                value="profile" 
                className="flex items-center justify-center min-w-[60px] data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:scale-110 data-[state=active]:font-medium transition-all duration-300 py-1.5 sm:py-2 px-2 sm:px-3.5 text-[10px] sm:text-xs relative rounded-md overflow-hidden"
                style={{ 
                  color: activeTab === "profile" ? "#fff" : COLORS.primary.main,
                  background: activeTab === "profile" 
                    ? `linear-gradient(135deg, ${COLORS.primary.main}, ${COLORS.primary.dark})` 
                    : `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.primary.bgLight})`,
                  border: activeTab === "profile" ? `1px solid rgba(255, 255, 255, 0.2)` : "none",
                  boxShadow: activeTab === "profile" ? `0 4px 12px rgba(0, 0, 0, 0.1)` : "none",
                }}
              >
                <UserCircle className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                <span className="truncate">Profile</span>
                <div className="absolute bottom-[-6px] left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full bg-primary data-[state=inactive]:opacity-0 data-[state=inactive]:scale-0 data-[state=active]:opacity-100 data-[state=active]:scale-100 transition-all duration-300 tab-indicator" 
                     style={{ 
                       background: `linear-gradient(135deg, ${COLORS.primary.main}, ${COLORS.primary.dark})`,
                       boxShadow: `0 0 8px ${COLORS.primary.main}` 
                     }}
                     data-state="inactive"
                />
              </TabsTrigger>
              <TabsTrigger 
                value="account" 
                className="flex items-center justify-center min-w-[60px] data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:scale-110 data-[state=active]:font-medium transition-all duration-300 py-1.5 sm:py-2 px-2 sm:px-3.5 text-[10px] sm:text-xs relative rounded-md overflow-hidden"
                style={{ 
                  color: activeTab === "account" ? "#fff" : COLORS.secondary.main,
                  background: activeTab === "account" 
                    ? `linear-gradient(135deg, ${COLORS.secondary.main}, ${COLORS.secondary.dark})` 
                    : `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.secondary.bgLight})`,
                  border: activeTab === "account" ? `1px solid rgba(255, 255, 255, 0.2)` : "none",
                  boxShadow: activeTab === "account" ? `0 4px 12px rgba(0, 0, 0, 0.1)` : "none",
                }}
              >
                <User className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                <span className="truncate">Account</span>
                <div className="absolute bottom-[-6px] left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full bg-secondary data-[state=inactive]:opacity-0 data-[state=inactive]:scale-0 data-[state=active]:opacity-100 data-[state=active]:scale-100 transition-all duration-300 tab-indicator" 
                     style={{ 
                       background: `linear-gradient(135deg, ${COLORS.secondary.main}, ${COLORS.secondary.dark})`,
                       boxShadow: `0 0 8px ${COLORS.secondary.main}` 
                     }}
                     data-state="inactive"
                />
              </TabsTrigger>
              <TabsTrigger 
                value="notifications" 
                className="flex items-center justify-center min-w-[60px] data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:scale-110 data-[state=active]:font-medium transition-all duration-300 py-1.5 sm:py-2 px-2 sm:px-3.5 text-[10px] sm:text-xs relative rounded-md overflow-hidden"
                style={{ 
                  color: activeTab === "notifications" ? "#fff" : COLORS.tertiary.main,
                  background: activeTab === "notifications" 
                    ? `linear-gradient(135deg, ${COLORS.tertiary.main}, ${COLORS.tertiary.dark})` 
                    : `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.tertiary.bgLight})`,
                  border: activeTab === "notifications" ? `1px solid rgba(255, 255, 255, 0.2)` : "none",
                  boxShadow: activeTab === "notifications" ? `0 4px 12px rgba(0, 0, 0, 0.1)` : "none",
                }}
              >
                <Bell className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                <span className="truncate">Alerts</span>
                <div className="absolute bottom-[-6px] left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full bg-tertiary data-[state=inactive]:opacity-0 data-[state=inactive]:scale-0 data-[state=active]:opacity-100 data-[state=active]:scale-100 transition-all duration-300 tab-indicator" 
                     style={{ 
                       background: `linear-gradient(135deg, ${COLORS.tertiary.main}, ${COLORS.tertiary.dark})`,
                       boxShadow: `0 0 8px ${COLORS.tertiary.main}` 
                     }}
                     data-state="inactive"
                />
              </TabsTrigger>
              <TabsTrigger 
                value="privacy" 
                className="flex items-center justify-center min-w-[60px] data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:scale-110 data-[state=active]:font-medium transition-all duration-300 py-1.5 sm:py-2 px-2 sm:px-3.5 text-[10px] sm:text-xs relative rounded-md overflow-hidden"
                style={{ 
                  color: activeTab === "privacy" ? "#fff" : COLORS.accent.main,
                  background: activeTab === "privacy" 
                    ? `linear-gradient(135deg, ${COLORS.accent.main}, ${COLORS.accent.dark})` 
                    : `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.accent.bgLight})`,
                  border: activeTab === "privacy" ? `1px solid rgba(255, 255, 255, 0.2)` : "none",
                  boxShadow: activeTab === "privacy" ? `0 4px 12px rgba(0, 0, 0, 0.1)` : "none",
                }}
              >
                <Shield className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                <span className="truncate">Privacy</span>
                <div className="absolute bottom-[-6px] left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full bg-accent data-[state=inactive]:opacity-0 data-[state=inactive]:scale-0 data-[state=active]:opacity-100 data-[state=active]:scale-100 transition-all duration-300 tab-indicator" 
                     style={{ 
                       background: `linear-gradient(135deg, ${COLORS.accent.main}, ${COLORS.accent.dark})`,
                       boxShadow: `0 0 8px ${COLORS.accent.main}` 
                     }}
                     data-state="inactive"
                />
              </TabsTrigger>
            </TabsList>
            
            <AnimatePresence mode="wait">
              {activeTab === "profile" && (
                <TabsContent key="profile-tab" value="profile" className="mt-0">
                  <motion.form
                    onSubmit={handleSave}
                    className="space-y-8"
                    data-onboarding="profile-form"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex flex-col md:flex-row gap-8">
                      {/* Avatar section */}
                      <div className="w-full md:w-1/3">
                        <div 
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                          style={{ 
                            background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <div className="flex flex-col items-center">
                            <div className="relative mb-4 group">
                              <div className="w-32 h-32 rounded-full overflow-hidden border-2" style={{ borderColor: COLORS.primary.light }}>
                                <img
                                  src={avatarPreview || '/images/default-avatar.svg'}
                                  alt="Avatar preview"
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 rounded-full flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100">
                                <label htmlFor="avatar-upload" className="cursor-pointer p-3 bg-white rounded-full shadow-md">
                                  <Upload className="w-5 h-5" style={{ color: COLORS.primary.main }} />
                                </label>
                              </div>
                            </div>
                            <input
                              type="file"
                              id="avatar-upload"
                              className="hidden"
                              accept="image/*"
                              onChange={handleAvatarChange}
                            />
                            <label 
                              htmlFor="avatar-upload" 
                              className="text-sm cursor-pointer hover:underline"
                              style={{ color: COLORS.primary.main }}
                            >
                              Change profile photo
                            </label>
                            
                            {avatarPreview && (
                              <Button 
                                type="button" 
                                variant="ghost" 
                                size="sm" 
                                className="mt-2"
                                style={{ color: COLORS.accent.main }}
                                onClick={() => {
                                  setAvatarFile(null);
                                  setAvatarPreview(null);
                                }}
                              >
                                <Trash2 className="w-4 h-4 mr-1" />
                                Remove photo
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Profile details section */}
                      <div className="w-full md:w-2/3 space-y-5">
                        <div 
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                          style={{ 
                            background: `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label 
                                htmlFor="fullName" 
                                className="text-base font-medium mb-2 block"
                                style={{ color: COLORS.neutral[800] }}
                              >
                                Full Name
                              </Label>
                              <Input 
                                id="fullName" 
                                value={formData.fullName}
                                onChange={handleInputChange}
                                placeholder="Your full name"
                                className={`bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base ${validationErrors.fullName ? 'ring-2 ring-red-500' : ''}`}
                                style={{ 
                                  borderColor: COLORS.tertiary.light,
                                  color: COLORS.neutral[800]
                                }}
                              />
                              {validationErrors.fullName && (
                                <p className="text-red-500 text-xs mt-1">{validationErrors.fullName}</p>
                              )}
                            </div>
                            
                            <div>
                              <Label 
                                htmlFor="username" 
                                className="text-base font-medium mb-2 block"
                                style={{ color: COLORS.neutral[800] }}
                              >
                                Username
                              </Label>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2" style={{ color: COLORS.neutral[500] }}>
                                  @
                                </span>
                                <Input 
                                  id="username" 
                                  value={formData.username}
                                  onChange={handleInputChange}
                                  placeholder="username"
                                  className={`bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base pl-8 ${validationErrors.username ? 'ring-2 ring-red-500' : ''}`}
                                  style={{ 
                                    borderColor: COLORS.tertiary.light,
                                    color: COLORS.neutral[800]
                                  }}
                                />
                              </div>
                              {validationErrors.username && (
                                <p className="text-red-500 text-xs mt-1">{validationErrors.username}</p>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div 
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                          style={{ 
                            background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <Label 
                            htmlFor="bio" 
                            className="text-base font-medium mb-2 block"
                            style={{ color: COLORS.neutral[800] }}
                          >
                            Bio
                          </Label>
                          <Textarea 
                            id="bio" 
                            value={formData.bio}
                            onChange={handleInputChange}
                            placeholder="Tell the world about yourself"
                            className="bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm min-h-[120px] text-base"
                            style={{ 
                              borderColor: COLORS.secondary.light,
                              color: COLORS.neutral[800]
                            }}
                          />
                          <p className="text-xs mt-2" style={{ color: COLORS.neutral[500] }}>
                            Brief description for your profile. URLs are hyperlinked.
                          </p>
                        </div>
                        
                        <div 
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                          style={{ 
                            background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <Label 
                            htmlFor="website" 
                            className="text-base font-medium mb-2 block"
                            style={{ color: COLORS.neutral[800] }}
                          >
                            Website
                          </Label>
                          <Input 
                            id="website" 
                            value={formData.website}
                            onChange={handleInputChange}
                            placeholder="https://yourwebsite.com"
                            className={`bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base ${validationErrors.website ? 'ring-2 ring-red-500' : ''}`}
                            style={{ 
                              borderColor: COLORS.accent.light,
                              color: COLORS.neutral[800]
                            }}
                          />
                          {validationErrors.website && (
                            <p className="text-red-500 text-xs mt-1">{validationErrors.website}</p>
                          )}
                        </div>
                        
                        {/* Social links section */}
                        <div
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md"
                          data-onboarding="social-links"
                          style={{
                            background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.tertiary.bgLight})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <div className="flex items-center justify-between mb-4">
                            <Label 
                              className="text-base font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Social Links
                            </Label>
                            <Button 
                              type="button" 
                              variant="outline" 
                              size="sm" 
                              onClick={addSocialLink}
                              className="h-8 bg-white/80 border-0 hover:bg-white/90 shadow-sm"
                              style={{ color: COLORS.tertiary.main }}
                            >
                              <Plus className="w-4 h-4 mr-1" />
                              Add Link
                            </Button>
                          </div>
                          
                          {socialLinks.length === 0 ? (
                            <div 
                              className="text-center py-8 rounded-lg flex flex-col items-center"
                              style={{ 
                                background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
                                border: `1px solid rgba(255, 255, 255, 0.4)`
                              }}
                            >
                              <div 
                                className="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                                style={{ background: COLORS.tertiary.bgLight }}
                              >
                                <Link className="w-5 h-5" style={{ color: COLORS.tertiary.main }} />
                              </div>
                              <p className="text-sm mb-3" style={{ color: COLORS.neutral[600] }}>No social links added yet</p>
                              <Button 
                                type="button" 
                                size="sm" 
                                onClick={addSocialLink}
                                className="shadow-sm text-white"
                                style={{ background: COLORS.tertiary.gradient }}
                              >
                                Add your first social link
                              </Button>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {socialLinks.map((link, index) => (
                                <div 
                                  key={index} 
                                  className="flex gap-2 items-center p-2 rounded-lg"
                                  style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                                >
                                  <select
                                    value={link.platform}
                                    onChange={(e) => updateSocialLink(index, 'platform', e.target.value)}
                                    className="rounded-md border-0 p-2 text-sm flex-shrink-0 w-36 bg-white/90 shadow-sm"
                                    style={{ color: COLORS.neutral[800] }}
                                  >
                                    {socialPlatforms.map(platform => (
                                      <option key={platform.id} value={platform.id}>
                                        {platform.name}
                                      </option>
                                    ))}
                                  </select>
                                  <Input
                                    value={link.url}
                                    onChange={(e) => updateSocialLink(index, 'url', e.target.value)}
                                    placeholder={`Your ${link.platform} URL`}
                                    className={`flex-1 bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base ${validationErrors[`socialLink${index}`] ? 'ring-2 ring-red-500' : ''}`}
                                    style={{ color: COLORS.neutral[800] }}
                                  />
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => removeSocialLink(index)}
                                    className="text-gray-400 hover:text-red-500 h-9 w-9 bg-white/50 hover:bg-white/80 rounded-full"
                                  >
                                    <X className="w-4 h-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Add Payment Information Section - right after Social Links section */}
                        <div
                          className="backdrop-blur-sm rounded-xl p-6 shadow-md mt-6"
                          data-onboarding="payment-section"
                          style={{
                            background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.primary.bgLight})`,
                            border: `1px solid rgba(255, 255, 255, 0.2)`
                          }}
                        >
                          <div className="flex items-center justify-between mb-4">
                            <Label 
                              className="text-base font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Payment Information
                            </Label>
                          </div>
                          
                          <div className="space-y-4">
                            <div>
                              <Label 
                                htmlFor="payment_type" 
                                className="text-sm font-medium mb-1 block"
                                style={{ color: COLORS.neutral[700] }}
                              >
                                Payment Type
                              </Label>
                              <select
                                id="payment_type"
                                value={formData.payment_type}
                                onChange={(e) => setFormData(prev => ({ ...prev, payment_type: e.target.value }))}
                                className="w-full rounded-md border-0 p-2 text-sm bg-white/90 shadow-sm"
                                style={{ color: COLORS.neutral[800] }}
                              >
                                <option value="UPI">UPI (India)</option>
                                <option value="PayPal">PayPal</option>
                                <option value="Other">Other</option>
                              </select>
                              <p className="text-xs mt-1" style={{ color: COLORS.neutral[500] }}>
                                Select how you'd like to receive payments
                              </p>
                            </div>
                            
                            <div>
                              <Label 
                                htmlFor="payment_id" 
                                className="text-sm font-medium mb-1 block"
                                style={{ color: COLORS.neutral[700] }}
                              >
                                {formData.payment_type === 'UPI' ? 'UPI ID' : 
                                 formData.payment_type === 'PayPal' ? 'PayPal Email' : 
                                 'Payment ID'}
                              </Label>
                              <Input 
                                id="payment_id" 
                                value={formData.payment_id}
                                onChange={(e) => setFormData(prev => ({ ...prev, payment_id: e.target.value }))}
                                placeholder={
                                  formData.payment_type === 'UPI' ? 'yourname@upi' : 
                                  formData.payment_type === 'PayPal' ? '<EMAIL>' : 
                                  'Your payment identifier'
                                }
                                className={`bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base ${validationErrors.payment_id ? 'ring-2 ring-red-500' : ''}`}
                                style={{ color: COLORS.neutral[800] }}
                              />
                              {validationErrors.payment_id && (
                                <p className="text-red-500 text-xs mt-1">{validationErrors.payment_id}</p>
                              )}
                              <p className="text-xs mt-1" style={{ color: COLORS.neutral[500] }}>
                                {formData.payment_type === 'UPI' ? 'Your UPI ID where you can receive payments' : 
                                 formData.payment_type === 'PayPal' ? 'Your PayPal email address' : 
                                 'ID used to receive payments through your preferred service'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-end mt-8">
                      <motion.button 
                        type="submit" 
                        disabled={loading}
                        className="flex items-center px-8 py-2.5 rounded-full text-white font-medium shadow-md"
                        style={{ background: COLORS.primary.gradient }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {loading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                          </span>
                        ) : 'Save Changes'}
                      </motion.button>
                    </div>
                  </motion.form>
                </TabsContent>
              )}
              
              {activeTab === "account" && (
                <TabsContent key="account-tab" value="account" className="mt-0">
                  <motion.div 
                    className="space-y-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div 
                      className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                      style={{ 
                        background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                        border: `1px solid rgba(255, 255, 255, 0.2)`
                      }}
                    >
                      <h3 
                        className="text-lg font-medium mb-4"
                        style={{ color: COLORS.neutral[800] }}
                      >
                        Email Address
                      </h3>
                      <div className="mb-4">
                        <Label 
                          htmlFor="email"
                          className="text-base font-medium mb-2 block"
                          style={{ color: COLORS.neutral[700] }}
                        >
                          Current Email
                        </Label>
                        <div className="flex items-center gap-2">
                          <Input 
                            id="email" 
                            value={user?.email || ''}
                            readOnly
                            className="bg-white/80 border-0 shadow-sm text-base"
                            style={{ color: COLORS.neutral[600] }}
                          />
                          <Button 
                            variant="outline"
                            className="whitespace-nowrap bg-white/80 border-0 shadow-sm hover:bg-white/90"
                            style={{ color: COLORS.secondary.main }}
                          >
                            Change Email
                          </Button>
                        </div>
                      </div>
                      
                      <div className="pt-4 border-t" style={{ borderColor: 'rgba(255, 255, 255, 0.3)' }}>
                        <h3 
                          className="text-lg font-medium mb-4"
                          style={{ color: COLORS.neutral[800] }}
                        >
                          Password
                        </h3>
                        <Button 
                          variant="outline"
                          className="bg-white/80 border-0 shadow-sm hover:bg-white/90"
                          style={{ color: COLORS.secondary.main }}
                        >
                          Change Password
                        </Button>
                      </div>
                    </div>
                    
                    <div 
                      className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                      style={{ 
                        background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                        border: `1px solid rgba(255, 255, 255, 0.2)`
                      }}
                    >
                      <div className="flex items-start gap-4">
                        <div 
                          className="p-3 rounded-full"
                          style={{ background: 'rgba(255, 255, 255, 0.5)' }}
                        >
                          <Trash2 
                            className="w-5 h-5" 
                            style={{ color: COLORS.accent.main }}
                          />
                        </div>
                        <div className="flex-1">
                          <h3 
                            className="text-lg font-medium mb-2"
                            style={{ color: COLORS.accent.main }}
                          >
                            Delete Account
                          </h3>
                          <p className="mb-4" style={{ color: COLORS.neutral[700] }}>
                            Once you delete your account, all of your data will be permanently removed. This action cannot be undone.
                          </p>
                          <Button 
                            variant="destructive"
                            className="bg-white/80 border-0 shadow-sm hover:bg-white/90 text-red-600 hover:text-red-700"
                          >
                            Delete Account
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </TabsContent>
              )}
              
              {activeTab === "notifications" && (
                <TabsContent key="notifications-tab" value="notifications" className="mt-0">
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div 
                      className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                      style={{ 
                        background: `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`,
                        border: `1px solid rgba(255, 255, 255, 0.2)`
                      }}
                    >
                      <h3 
                        className="text-lg font-medium mb-6"
                        style={{ color: COLORS.neutral[800] }}
                      >
                        Email Notification Preferences
                      </h3>
                      
                      <div className="space-y-5">
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              New Coupon Alerts
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Receive emails about new coupons in categories you follow
                            </p>
                          </div>
                          <Switch 
                            defaultChecked 
                            className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-400 data-[state=checked]:to-indigo-500"
                          />
                        </div>
                        
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Engagement Updates
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Emails when users interact with your coupons
                            </p>
                          </div>
                          <Switch 
                            defaultChecked 
                            className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-400 data-[state=checked]:to-indigo-500"
                          />
                        </div>
                        
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Coupon Expiration
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Get notified before your coupons expire
                            </p>
                          </div>
                          <Switch 
                            defaultChecked 
                            className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-400 data-[state=checked]:to-indigo-500"
                          />
                        </div>
                        
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Weekly Performance Digest
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Get a weekly summary of your coupon performance
                            </p>
                          </div>
                          <Switch 
                            defaultChecked 
                            className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-400 data-[state=checked]:to-indigo-500"
                          />
                        </div>
                        
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Marketing Emails
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Promotional emails and platform updates
                            </p>
                          </div>
                          <Switch className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-400 data-[state=checked]:to-indigo-500" />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </TabsContent>
              )}
              
              {activeTab === "privacy" && (
                <TabsContent key="privacy-tab" value="privacy" className="mt-0">
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div 
                      className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                      style={{ 
                        background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                        border: `1px solid rgba(255, 255, 255, 0.2)`
                      }}
                    >
                      <h3 
                        className="text-lg font-medium mb-6"
                        style={{ color: COLORS.neutral[800] }}
                      >
                        Privacy & Security
                      </h3>
                      
                      <div className="space-y-5">
                        <div 
                          className="flex justify-between items-center py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Profile Visibility
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Make your profile and coupons visible to everyone
                            </p>
                          </div>
                          <Switch 
                            defaultChecked 
                            className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-pink-400 data-[state=checked]:to-purple-500"
                          />
                        </div>
                        
                        <div 
                          className="flex items-center justify-between py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Two-Factor Authentication
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Add an extra layer of security to your account
                            </p>
                          </div>
                          <Button 
                            variant="outline"
                            className="bg-white/80 border-0 shadow-sm hover:bg-white/90"
                            style={{ color: COLORS.accent.main }}
                          >
                            Set Up
                          </Button>
                        </div>
                        
                        <div 
                          className="flex items-center justify-between py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Activity Log
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              View and manage your account activity
                            </p>
                          </div>
                          <Button 
                            variant="outline"
                            className="bg-white/80 border-0 shadow-sm hover:bg-white/90"
                            style={{ color: COLORS.accent.main }}
                          >
                            View Log
                          </Button>
                        </div>
                        
                        <div 
                          className="flex items-center justify-between py-4 px-4 rounded-lg"
                          style={{ background: 'rgba(255, 255, 255, 0.6)' }}
                        >
                          <div>
                            <h4 
                              className="font-medium"
                              style={{ color: COLORS.neutral[800] }}
                            >
                              Data Export
                            </h4>
                            <p className="text-sm" style={{ color: COLORS.neutral[500] }}>
                              Download a copy of your data
                            </p>
                          </div>
                          <Button 
                            variant="outline"
                            className="bg-white/80 border-0 shadow-sm hover:bg-white/90"
                            style={{ color: COLORS.accent.main }}
                          >
                            Request Export
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </TabsContent>
              )}
            </AnimatePresence>
          </Tabs>
        </motion.div>
      </PageContainer>
    </MainLayout>
  );
};

export default Settings;
