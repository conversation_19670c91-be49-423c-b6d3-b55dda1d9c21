import React from 'react';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import CouponCard from '@/components/CouponCard';
import { useNavigate } from 'react-router-dom';
import { useTrendingCoupons } from '@/hooks/useCoupons';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { TrendingUp } from 'lucide-react';

const TrendingDeals = () => {
  const navigate = useNavigate();
  const { data: trendingCoupons, isLoading, error } = useTrendingCoupons();
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Trending Deals"
          subtitle="Discover the hottest deals that everyone's talking about right now"
          icon={TrendingUp}
        />

        {/* Deals Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <CouponsSkeleton count={12} />
          </div>
        ) : error ? (
          <div className="w-full flex justify-center items-center bg-red-50 dark:bg-red-900/20 rounded-lg p-10 shadow-sm">
            <p className="text-red-600 dark:text-red-400">There was an error loading trending deals. Please try again later.</p>
          </div>
        ) : trendingCoupons && trendingCoupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {trendingCoupons.map((coupon) => (
              <motion.div
                key={coupon.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ y: -5 }}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
              >
                <CouponCard
                  id={coupon.id}
                  brandName={coupon.brand?.name || "Unknown Brand"}
                  brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                  influencerName={coupon.influencer?.full_name || "Anonymous"}
                  influencerImage={coupon.influencer?.avatar_url}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "General"}
                  featured={coupon.featured}
                  isPremium={coupon.is_premium}
                  brandId={coupon.brand?.id}
                  price={coupon.price}
                />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="w-full flex justify-center items-center bg-white dark:bg-gray-800/20 rounded-lg p-10 shadow-sm">
            <p className="text-gray-600 dark:text-gray-400">No trending deals available at the moment.</p>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default TrendingDeals; 