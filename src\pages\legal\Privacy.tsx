import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Shield } from 'lucide-react';
import SEO from '@/seo/components/SEO';
import { Link } from 'react-router-dom';

const Privacy = () => {
  return (
    <MainLayout>
      <SEO 
        title="Privacy Policy - CouponLink"
        description="Learn how CouponLink collects, uses, and protects your personal information. Our privacy policy explains our data practices and your rights."
        keywords="privacy policy, data protection, personal information, data collection, privacy rights"
      />
      
      <PageContainer>
        <PageHeaderWithBackButton
          title="Privacy Policy"
          subtitle="Last updated: March 2024"
          icon={Shield}
        />
        
        <div className="prose prose-lg max-w-none dark:prose-invert">
          <h2>1. Information We Collect</h2>
          <p>
            We collect information that you provide directly to us, including:
          </p>
          <ul>
            <li>Account information (name, email, username)</li>
            <li>Profile information (avatar, bio)</li>
            <li>Usage data and preferences</li>
            <li>Communication with our support team</li>
          </ul>

          <h2>2. How We Use Your Information</h2>
          <p>
            We use the collected information for:
          </p>
          <ul>
            <li>Providing and improving our services</li>
            <li>Personalizing your experience</li>
            <li>Processing transactions</li>
            <li>Communicating with you about our services</li>
            <li>Analyzing usage patterns and trends</li>
          </ul>

          <h2>3. Information Sharing</h2>
          <p>
            We may share your information with:
          </p>
          <ul>
            <li>Service providers and partners</li>
            <li>Other users (public profile information)</li>
            <li>Legal authorities when required by law</li>
          </ul>

          <h2>4. Data Security</h2>
          <p>
            We implement appropriate security measures to protect your information, including:
          </p>
          <ul>
            <li>Encryption of sensitive data</li>
            <li>Regular security assessments</li>
            <li>Access controls and monitoring</li>
            <li>Secure data storage practices</li>
          </ul>

          <h2>5. Cookies and Tracking</h2>
          <p>
            We use cookies and similar technologies to:
          </p>
          <ul>
            <li>Remember your preferences</li>
            <li>Analyze site usage</li>
            <li>Personalize content</li>
            <li>Improve our services</li>
          </ul>

          <h2>6. Your Rights</h2>
          <p>
            You have the right to:
          </p>
          <ul>
            <li>Access your personal information</li>
            <li>Correct inaccurate data</li>
            <li>Request deletion of your data</li>
            <li>Opt-out of marketing communications</li>
            <li>Export your data</li>
          </ul>

          <h2>7. Children's Privacy</h2>
          <p>
            Our service is not intended for children under 13. We do not knowingly collect 
            information from children under 13 years old.
          </p>

          <h2>8. International Data Transfers</h2>
          <p>
            Your information may be transferred to and processed in countries other than your own. 
            We ensure appropriate safeguards are in place for such transfers.
          </p>

          <h2>9. Changes to Privacy Policy</h2>
          <p>
            We may update this privacy policy periodically. We will notify you of any material changes 
            through our service or via email.
          </p>

          <h2>10. Contact Us</h2>
          <p>
            For privacy-related questions or concerns, contact us at:
          </p>
          <ul>
            <li>Email: <EMAIL></li>
            <li>Address: [Your Business Address]</li>
          </ul>

          <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Related Documents:
              <Link to="/terms" className="text-blue-600 dark:text-blue-400 hover:underline ml-2">Terms of Service</Link>
            </p>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default Privacy; 