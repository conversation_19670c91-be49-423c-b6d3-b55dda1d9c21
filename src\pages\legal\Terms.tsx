import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { ScrollText } from 'lucide-react';
import SEO from '@/seo/components/SEO';
import { Link } from 'react-router-dom';

const Terms = () => {
  return (
    <MainLayout>
      <SEO 
        title="Terms of Service - CouponLink"
        description="Read our terms of service to understand the rules and guidelines for using CouponLink's coupon sharing platform."
        keywords="terms of service, terms and conditions, user agreement, legal terms, couponlink terms"
      />
      
      <PageContainer>
        <PageHeaderWithBackButton
          title="Terms of Service"
          subtitle="Last updated: March 2024"
          icon={ScrollText}
        />
        
        <div className="prose prose-lg max-w-none dark:prose-invert">
          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing and using CouponLink ("we," "our," or "us"), you agree to be bound by these Terms of Service. 
            If you do not agree to these terms, please do not use our service.
          </p>

          <h2>2. Description of Service</h2>
          <p>
            CouponLink is a platform that allows users to discover, share, and use coupon codes and promotional offers. 
            We provide a marketplace for influencers and brands to share promotional offers with users.
          </p>

          <h2>3. User Accounts</h2>
          <p>
            To access certain features of our service, you must create an account. You are responsible for:
          </p>
          <ul>
            <li>Maintaining the confidentiality of your account</li>
            <li>All activities that occur under your account</li>
            <li>Providing accurate and current information</li>
          </ul>

          <h2>4. User Conduct</h2>
          <p>
            When using CouponLink, you agree not to:
          </p>
          <ul>
            <li>Post fraudulent or misleading coupon codes</li>
            <li>Violate any applicable laws or regulations</li>
            <li>Impersonate other users or entities</li>
            <li>Interfere with the proper functioning of the service</li>
          </ul>

          <h2>5. Premium Coupons</h2>
          <p>
            Some coupons on our platform may require payment to access. For these premium coupons:
          </p>
          <ul>
            <li>All sales are final unless otherwise stated</li>
            <li>Refunds may be issued at our discretion</li>
            <li>We do not guarantee savings amounts</li>
          </ul>

          <h2>6. Intellectual Property</h2>
          <p>
            All content on CouponLink, including but not limited to text, graphics, logos, and software, 
            is our property or the property of our licensors and is protected by copyright and other intellectual property laws.
          </p>

          <h2>7. Limitation of Liability</h2>
          <p>
            CouponLink is provided "as is" without any warranties. We are not responsible for:
          </p>
          <ul>
            <li>The accuracy of coupon codes or offers</li>
            <li>Any losses incurred from using our service</li>
            <li>Technical issues or service interruptions</li>
          </ul>

          <h2>8. Changes to Terms</h2>
          <p>
            We reserve the right to modify these terms at any time. Continued use of CouponLink after changes 
            constitutes acceptance of the new terms.
          </p>

          <h2>9. Contact Information</h2>
          <p>
            For questions about these terms, please contact us at:
          </p>
          <ul>
            <li>Email: <EMAIL></li>
            <li>Address: [Your Business Address]</li>
          </ul>

          <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Related Documents:
              <Link to="/privacy" className="text-blue-600 dark:text-blue-400 hover:underline ml-2">Privacy Policy</Link>
            </p>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default Terms; 