import { useN<PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Monitor,
  ShoppingBag,
  Utensils,
  Plane,
  Heart,
  Home,
  Briefcase,
  Gift,
  Smartphone,
  ShoppingCart,
  Scissors,
  Grid3x3,
  Car,
  BookOpen,
  Dog,
  Baby,
  Gamepad2,
  Gem,
  Inbox,
  Tent,
  Download,
  Dumbbell,
  Music,
  Tv
} from 'lucide-react';
import { useCategories } from '@/hooks/useCategories';
import { FiArrowRight } from 'react-icons/fi';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Extend the Category interface from useCategories
interface ExtendedCategory {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  is_featured?: boolean;
  display_order?: number;
}

const CategoriesSection = () => {
  const navigate = useNavigate();
  const { data: categories, isLoading } = useCategories();

  // Get coupon counts for categories
  const { data: categoryCounts } = useQuery({
    queryKey: ['category-counts'],
    queryFn: async () => {
      try {
        if (!categories || categories.length === 0) return {};
        
        const counts: Record<string, number> = {};
        
        await Promise.all(
          categories.map(async (category) => {
            const { count, error } = await supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('status', 'active')
              .eq('category_id', category.id);
            
            if (!error) {
              counts[category.id] = count || 0;
            }
          })
        );
        
        return counts;
      } catch (error) {
        console.error('Error fetching category counts:', error);
        return {};
      }
    },
    enabled: !!categories && categories.length > 0
  });

  // Category icon mapping
  const getCategoryIcon = (name: string) => {
    const iconMap = {
      'Electronics': <Monitor className="h-6 w-6" />,
      'Fashion': <ShoppingBag className="h-6 w-6" />,
      'Food': <Utensils className="h-6 w-6" />,
      'Travel': <Plane className="h-6 w-6" />,
      'Beauty': <Heart className="h-6 w-6" />,
      'Home': <Home className="h-6 w-6" />,
      'Sports': <Briefcase className="h-6 w-6" />,
      'Health': <Heart className="h-6 w-6" />,
      'Technology': <Smartphone className="h-6 w-6" />,
      'Services': <Scissors className="h-6 w-6" />,
      'Gifts': <Gift className="h-6 w-6" />,
      'Automotive': <Car className="h-6 w-6" />,
      'Education': <BookOpen className="h-6 w-6" />,
      'Pets': <Dog className="h-6 w-6" />,
      'Baby and Kids': <Baby className="h-6 w-6" />,
      'Gaming': <Gamepad2 className="h-6 w-6" />,
      'Jewelry': <Gem className="h-6 w-6" />,
      'Office': <Inbox className="h-6 w-6" />,
      'Outdoors': <Tent className="h-6 w-6" />,
      'Software': <Download className="h-6 w-6" />,
      'Streaming': <Tv className="h-6 w-6" />,
      'Fitness': <Dumbbell className="h-6 w-6" />,
      'Entertainment': <Music className="h-6 w-6" />
    };
    
    return iconMap[name] || <ShoppingCart className="h-6 w-6" />;
  };

  // Color mapping for categories
  const getCategoryColor = (name: string) => {
    const colorMap = {
      'Electronics': 'from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600',
      'Fashion': 'from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600',
      'Food': 'from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600',
      'Travel': 'from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600',
      'Beauty': 'from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600',
      'Home': 'from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600',
      'Sports': 'from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600',
      'Health': 'from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600',
      'Technology': 'from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600',
      'Services': 'from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600',
      'Gifts': 'from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600',
      'Automotive': 'from-slate-600 to-gray-700 hover:from-slate-700 hover:to-gray-800',
      'Education': 'from-amber-500 to-yellow-400 hover:from-amber-600 hover:to-yellow-500',
      'Pets': 'from-amber-400 to-orange-300 hover:from-amber-500 hover:to-orange-400',
      'Baby and Kids': 'from-sky-400 to-blue-300 hover:from-sky-500 hover:to-blue-400',
      'Gaming': 'from-fuchsia-600 to-purple-600 hover:from-fuchsia-700 hover:to-purple-700',
      'Jewelry': 'from-amber-300 to-yellow-200 hover:from-amber-400 hover:to-yellow-300',
      'Office': 'from-slate-500 to-gray-400 hover:from-slate-600 hover:to-gray-500',
      'Outdoors': 'from-lime-600 to-green-500 hover:from-lime-700 hover:to-green-600',
      'Software': 'from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',
      'Streaming': 'from-red-600 to-rose-500 hover:from-red-700 hover:to-rose-600',
      'Fitness': 'from-emerald-500 to-green-400 hover:from-emerald-600 hover:to-green-500',
      'Entertainment': 'from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600'
    };
    
    return colorMap[name] || 'from-gray-500 to-slate-500 hover:from-gray-600 hover:to-slate-600';
  };

  // Get the top categories with the most coupons or featured categories
  const topCategories = categories
    ?.sort((a, b) => {
      const categoryA = a as unknown as ExtendedCategory;
      const categoryB = b as unknown as ExtendedCategory;
      
      // First sort by featured status (featured categories first)
      if (categoryA.is_featured && !categoryB.is_featured) return -1;
      if (!categoryA.is_featured && categoryB.is_featured) return 1;
      
      // Then sort by display order (lower numbers first)
      if (categoryA.display_order !== categoryB.display_order) {
        return (categoryA.display_order || 1000) - (categoryB.display_order || 1000);
      }
      
      // Finally sort by coupon count
      return (categoryCounts?.[b.id] || 0) - (categoryCounts?.[a.id] || 0);
    })
    .slice(0, 6) || [];

  return (
    <section className="w-full py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 right-10 w-96 h-96 bg-brand-malachite-100/30 dark:bg-brand-malachite-900/10 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-10 left-20 w-64 h-64 bg-brand-malachite-100/20 dark:bg-brand-malachite-900/10 rounded-full blur-3xl opacity-20"></div>
        </div>

        <div className="flex items-center justify-between mb-10">
          <motion.h2 
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-2xl font-bold text-gray-900 dark:text-white flex items-center"
          >
            <span className="bg-brand-malachite-100 dark:bg-brand-malachite-900/50 text-brand-malachite dark:text-brand-malachite-400 p-2 rounded-lg mr-3">
              <Grid3x3 className="h-6 w-6" />
            </span>
            Popular Categories
          </motion.h2>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Link 
              to="/categories" 
              className="text-brand-malachite dark:text-brand-malachite-400 hover:text-brand-malachite-600 dark:hover:text-brand-malachite-300 flex items-center text-sm font-medium group"
            >
              View all <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 gap-6">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="rounded-xl bg-gray-100 dark:bg-gray-800 h-32 animate-pulse"></div>
            ))
          ) : (
            // Top 6 categories by coupon count
            topCategories.map((category, index) => (
              <motion.div 
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="overflow-hidden rounded-xl relative"
              >
                <Link 
                  to={`/categories/${encodeURIComponent(category.name)}`}
                  className={`block h-full bg-gradient-to-br ${getCategoryColor(category.name)} text-white p-6 transition-all duration-300 shadow-md hover:shadow-xl`}
                >
                  <div className="flex flex-col h-full relative z-10">
                    <div className="flex items-center mb-3">
                      <div className="p-2 bg-white/20 rounded-lg">
                        {getCategoryIcon(category.name)}
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold">{category.name}</h3>
                    <p className="mt-1 text-sm text-white/80">
                      {categoryCounts?.[category.id] || 0} {(categoryCounts?.[category.id] || 0) === 1 ? 'coupon' : 'coupons'}
                    </p>
                    <div className="mt-auto pt-4 text-sm flex items-center text-white/90 hover:text-white group">
                      View deals <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
                    </div>
                  </div>
                  
                  {/* Decorative circles */}
                  <div className="absolute top-0 right-0 w-24 h-24 rounded-full bg-white/10 -translate-y-1/2 translate-x-1/2"></div>
                  <div className="absolute bottom-0 left-0 w-16 h-16 rounded-full bg-white/5 translate-y-1/2 -translate-x-1/2"></div>
                </Link>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default CategoriesSection; 