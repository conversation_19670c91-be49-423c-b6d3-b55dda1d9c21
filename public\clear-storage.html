<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Storage - CouponLink</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #155724;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .storage-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear Storage - CouponLink</h1>
        
        <div class="warning">
            <strong>⚠️ Warning:</strong> This tool will help you clear corrupted localStorage data that might be causing JSON parsing errors in the CouponLink application.
        </div>

        <div class="info">
            <h3>Current localStorage items:</h3>
            <div id="storageList"></div>
        </div>

        <button onclick="scanStorage()">🔍 Scan for Issues</button>
        <button onclick="clearCorrupted()" class="danger">🗑️ Clear Corrupted Data</button>
        <button onclick="clearAll()" class="danger">💥 Clear All Data</button>
        <button onclick="goBack()">← Back to App</button>

        <div id="results"></div>
    </div>

    <script>
        function displayStorage() {
            const list = document.getElementById('storageList');
            list.innerHTML = '';
            
            if (localStorage.length === 0) {
                list.innerHTML = '<div class="storage-item">No localStorage items found</div>';
                return;
            }

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const item = document.createElement('div');
                item.className = 'storage-item';
                item.innerHTML = `<strong>${key}:</strong> ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`;
                list.appendChild(item);
            }
        }

        function scanStorage() {
            const results = document.getElementById('results');
            const issues = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                
                // Skip simple string values
                if (value === 'true' || value === 'false' || !value.includes('{') && !value.includes('[')) {
                    continue;
                }
                
                try {
                    JSON.parse(value);
                } catch (error) {
                    issues.push({ key, value, error: error.message });
                }
            }
            
            if (issues.length === 0) {
                results.innerHTML = '<div class="success">✅ No corrupted JSON data found in localStorage!</div>';
            } else {
                let html = '<div class="warning"><strong>Found ' + issues.length + ' corrupted items:</strong><ul>';
                issues.forEach(issue => {
                    html += `<li><strong>${issue.key}:</strong> ${issue.error}</li>`;
                });
                html += '</ul></div>';
                results.innerHTML = html;
            }
            
            displayStorage();
        }

        function clearCorrupted() {
            const issues = [];
            
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                
                // Skip simple string values
                if (value === 'true' || value === 'false' || !value.includes('{') && !value.includes('[')) {
                    continue;
                }
                
                try {
                    JSON.parse(value);
                } catch (error) {
                    localStorage.removeItem(key);
                    issues.push(key);
                }
            }
            
            const results = document.getElementById('results');
            if (issues.length === 0) {
                results.innerHTML = '<div class="success">✅ No corrupted data found to clear!</div>';
            } else {
                results.innerHTML = '<div class="success">✅ Cleared ' + issues.length + ' corrupted items: ' + issues.join(', ') + '</div>';
            }
            
            displayStorage();
        }

        function clearAll() {
            if (confirm('Are you sure you want to clear ALL localStorage data? This will log you out and reset all preferences.')) {
                localStorage.clear();
                document.getElementById('results').innerHTML = '<div class="success">✅ All localStorage data cleared!</div>';
                displayStorage();
            }
        }

        function goBack() {
            window.location.href = '/';
        }

        // Initialize
        displayStorage();
    </script>
</body>
</html>
