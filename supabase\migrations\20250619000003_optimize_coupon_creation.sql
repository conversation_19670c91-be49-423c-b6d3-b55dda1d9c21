-- Optimize coupon creation performance
-- This migration includes several optimizations to improve coupon creation speed

-- 1. Add unique constraint on coupon code to prevent duplicates and improve performance
ALTER TABLE coupons ADD CONSTRAINT IF NOT EXISTS unique_coupon_code UNIQUE (code);

-- 2. Optimize RLS policy for coupons to avoid repeated auth.uid() calls
DROP POLICY IF EXISTS "Influencers can manage their own coupons" ON coupons;

CREATE POLICY "Influencers can manage their own coupons" ON coupons
FOR ALL
TO public
USING (
  (SELECT auth.uid()) = influencer_id 
  OR 
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = (SELECT auth.uid()) 
    AND profiles.role = 'admin'::user_role
  )
);

-- 3. Create index on status column if it doesn't exist (for faster queries)
CREATE INDEX IF NOT EXISTS idx_coupons_status ON coupons (status);

-- 4. Add index on code column for faster duplicate checking
CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons (code);

-- 5. Optimize the coupon search index
DROP INDEX IF EXISTS idx_coupons_search;
CREATE INDEX idx_coupons_search ON coupons USING gin (
  to_tsvector('english', 
    COALESCE(title, '') || ' ' || 
    COALESCE(code, '') || ' ' || 
    COALESCE(discount_description, '')
  )
) WHERE status = 'active';

-- 6. Add composite index for common query patterns
CREATE INDEX IF NOT EXISTS idx_coupons_active_created ON coupons (status, created_at DESC) 
WHERE status = 'active';

-- 7. Add index for influencer dashboard queries
CREATE INDEX IF NOT EXISTS idx_coupons_influencer_status ON coupons (influencer_id, status, created_at DESC);

-- Performance notes:
-- - Unique constraint on code prevents duplicate inserts and speeds up validation
-- - Optimized RLS policy reduces auth function calls during inserts
-- - Additional indexes improve query performance for common patterns
-- - Search index is limited to active coupons for better performance
