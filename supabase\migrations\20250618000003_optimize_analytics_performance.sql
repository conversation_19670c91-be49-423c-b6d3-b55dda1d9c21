-- Optimize Analytics Performance Migration
-- This migration creates optimized database functions and indexes for faster analytics loading

-- ============================================================================
-- 1. CREATE INDEXES FOR ANALYTICS PERFORMANCE
-- ============================================================================

-- Index for coupon_interactions table
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_user_date 
ON public.coupon_interactions(user_id, occurred_at DESC);

CREATE INDEX IF NOT EXISTS idx_coupon_interactions_type_date 
ON public.coupon_interactions(interaction_type, occurred_at DESC);

-- Index for premium_purchases table
CREATE INDEX IF NOT EXISTS idx_premium_purchases_buyer_date 
ON public.premium_purchases(buyer_id, purchased_at DESC);

-- Index for profile_views table
CREATE INDEX IF NOT EXISTS idx_profile_views_profile_date 
ON public.profile_views(profile_id, created_at DESC);

-- Index for coupons table
CREATE INDEX IF NOT EXISTS idx_coupons_influencer_views 
ON public.coupons(influencer_id, view_count DESC);

-- ============================================================================
-- 2. CREATE OPTIMIZED RPC FUNCTIONS
-- ============================================================================

-- Function to get interaction statistics for current and previous periods
CREATE OR REPLACE FUNCTION public.get_interaction_stats(
  p_user_id UUID,
  p_from_date TIMESTAMPTZ,
  p_to_date TIMESTAMPTZ,
  p_prev_from_date TIMESTAMPTZ,
  p_prev_to_date TIMESTAMPTZ
)
RETURNS TABLE (
  current_views BIGINT,
  current_clicks BIGINT,
  current_copies BIGINT,
  previous_views BIGINT,
  previous_clicks BIGINT,
  previous_copies BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  WITH current_stats AS (
    SELECT
      COUNT(*) FILTER (WHERE interaction_type = 'view') as views,
      COUNT(*) FILTER (WHERE interaction_type = 'click') as clicks,
      COUNT(*) FILTER (WHERE interaction_type = 'copy') as copies
    FROM public.coupon_interactions
    WHERE coupon_interactions.user_id = p_user_id
    AND occurred_at >= p_from_date
    AND occurred_at <= p_to_date
  ),
  previous_stats AS (
    SELECT
      COUNT(*) FILTER (WHERE interaction_type = 'view') as views,
      COUNT(*) FILTER (WHERE interaction_type = 'click') as clicks,
      COUNT(*) FILTER (WHERE interaction_type = 'copy') as copies
    FROM public.coupon_interactions
    WHERE coupon_interactions.user_id = p_user_id
    AND occurred_at >= p_prev_from_date
    AND occurred_at <= p_prev_to_date
  )
  SELECT
    COALESCE(c.views, 0)::BIGINT as current_views,
    COALESCE(c.clicks, 0)::BIGINT as current_clicks,
    COALESCE(c.copies, 0)::BIGINT as current_copies,
    COALESCE(p.views, 0)::BIGINT as previous_views,
    COALESCE(p.clicks, 0)::BIGINT as previous_clicks,
    COALESCE(p.copies, 0)::BIGINT as previous_copies
  FROM current_stats c
  CROSS JOIN previous_stats p;
END;
$$;

-- Function to get revenue statistics for current and previous periods
CREATE OR REPLACE FUNCTION public.get_revenue_stats(
  p_user_id UUID,
  p_from_date TIMESTAMPTZ,
  p_to_date TIMESTAMPTZ,
  p_prev_from_date TIMESTAMPTZ,
  p_prev_to_date TIMESTAMPTZ
)
RETURNS TABLE (
  current_revenue NUMERIC,
  current_sales_count BIGINT,
  previous_revenue NUMERIC,
  previous_sales_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  WITH current_stats AS (
    SELECT
      COALESCE(SUM(amount), 0) as revenue,
      COUNT(*) as sales_count
    FROM public.premium_purchases
    WHERE buyer_id = p_user_id
    AND purchased_at >= p_from_date
    AND purchased_at <= p_to_date
    AND status = 'completed'
  ),
  previous_stats AS (
    SELECT
      COALESCE(SUM(amount), 0) as revenue,
      COUNT(*) as sales_count
    FROM public.premium_purchases
    WHERE buyer_id = p_user_id
    AND purchased_at >= p_prev_from_date
    AND purchased_at <= p_prev_to_date
    AND status = 'completed'
  )
  SELECT
    c.revenue as current_revenue,
    c.sales_count as current_sales_count,
    p.revenue as previous_revenue,
    p.sales_count as previous_sales_count
  FROM current_stats c
  CROSS JOIN previous_stats p;
END;
$$;

-- Function to get daily analytics aggregated data
CREATE OR REPLACE FUNCTION public.get_daily_analytics_summary(
  p_user_id UUID,
  p_from_date TIMESTAMPTZ,
  p_to_date TIMESTAMPTZ
)
RETURNS TABLE (
  date_bucket DATE,
  total_views BIGINT,
  total_clicks BIGINT,
  total_copies BIGINT,
  total_revenue NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  WITH daily_interactions AS (
    SELECT
      DATE(occurred_at) as interaction_date,
      COUNT(*) FILTER (WHERE interaction_type = 'view') as views,
      COUNT(*) FILTER (WHERE interaction_type = 'click') as clicks,
      COUNT(*) FILTER (WHERE interaction_type = 'copy') as copies
    FROM public.coupon_interactions
    WHERE coupon_interactions.user_id = p_user_id
    AND occurred_at >= p_from_date
    AND occurred_at <= p_to_date
    GROUP BY DATE(occurred_at)
  ),
  daily_revenue AS (
    SELECT
      DATE(purchased_at) as purchase_date,
      COALESCE(SUM(amount), 0) as revenue
    FROM public.premium_purchases
    WHERE buyer_id = p_user_id
    AND purchased_at >= p_from_date
    AND purchased_at <= p_to_date
    AND status = 'completed'
    GROUP BY DATE(purchased_at)
  )
  SELECT
    COALESCE(di.interaction_date, dr.purchase_date) as date_bucket,
    COALESCE(di.views, 0) as total_views,
    COALESCE(di.clicks, 0) as total_clicks,
    COALESCE(di.copies, 0) as total_copies,
    COALESCE(dr.revenue, 0) as total_revenue
  FROM daily_interactions di
  FULL OUTER JOIN daily_revenue dr ON di.interaction_date = dr.purchase_date
  ORDER BY date_bucket;
END;
$$;

-- ============================================================================
-- 3. CREATE MATERIALIZED VIEW FOR POPULAR ANALYTICS (OPTIONAL)
-- ============================================================================

-- Create a materialized view for frequently accessed analytics data
CREATE MATERIALIZED VIEW IF NOT EXISTS public.analytics_summary AS
SELECT 
  p.id as user_id,
  p.username,
  p.role,
  COUNT(DISTINCT c.id) as total_coupons,
  COALESCE(SUM(c.view_count), 0) as total_coupon_views,
  COALESCE(SUM(c.copy_count), 0) as total_coupon_copies,
  COUNT(DISTINCT pp.id) as total_premium_sales,
  COALESCE(SUM(pp.amount), 0) as total_revenue
FROM public.profiles p
LEFT JOIN public.coupons c ON p.id = c.influencer_id
LEFT JOIN public.premium_purchases pp ON p.id = pp.seller_id AND pp.status = 'completed'
WHERE p.role IN ('influencer', 'user')
GROUP BY p.id, p.username, p.role;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_analytics_summary_user_id 
ON public.analytics_summary(user_id);

-- ============================================================================
-- 4. GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION public.get_interaction_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_revenue_stats TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_daily_analytics_summary TO authenticated;

-- Grant select on materialized view
GRANT SELECT ON public.analytics_summary TO authenticated;

-- ============================================================================
-- 5. CREATE REFRESH FUNCTION FOR MATERIALIZED VIEW
-- ============================================================================

-- Function to refresh analytics summary (can be called periodically)
CREATE OR REPLACE FUNCTION public.refresh_analytics_summary()
RETURNS void 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_summary;
END;
$$;

GRANT EXECUTE ON FUNCTION public.refresh_analytics_summary TO service_role;
