import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import CouponCard from '../CouponCard';

interface Coupon {
  id: string;
  status: string;
  brand?: {
    name: string;
    logo_url: string;
  };
  influencer?: {
    full_name: string;
    avatar_url?: string;
  };
  discount_description?: string;
  discount_percent?: number;
  expires_at?: string;
  code: string;
  category?: {
    name: string;
  };
  featured?: boolean;
  is_premium?: boolean;
  price?: number;
}

interface UserCouponTabsProps {
  coupons: Coupon[] | undefined;
  isLoading: boolean;
  userName?: string;
  userAvatar?: string;
}

const UserCouponTabs = ({ coupons, isLoading, userName, userAvatar }: UserCouponTabsProps) => {
  const activeCoupons = coupons?.filter(coupon => coupon.status === 'active') || [];
  const expiredCoupons = coupons?.filter(coupon => coupon.status === 'expired') || [];

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <Tabs defaultValue="active" className="w-full">
      <TabsList className="mb-6">
        <TabsTrigger value="active">
          Active Coupons ({activeCoupons.length})
        </TabsTrigger>
        <TabsTrigger value="expired">
          Expired Coupons ({expiredCoupons.length})
        </TabsTrigger>
      </TabsList>

      <TabsContent value="active">
        {activeCoupons.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activeCoupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                id={coupon.id}
                brandName={coupon.brand?.name || ""}
                brandLogo={coupon.brand?.logo_url || ""}
                influencerName={userName || coupon.influencer?.full_name || ""}
                influencerImage={userAvatar || coupon.influencer?.avatar_url}
                discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% off`}
                expirationTime={coupon.expires_at || ""}
                couponCode={coupon.code}
                category={coupon.category?.name || ""}
                featured={coupon.featured}
                isPremium={coupon.is_premium}
                isLocked={false}
                price={coupon.price || 0}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">No Active Coupons</h3>
            <p className="text-gray-600">
              This user hasn't created any active coupons yet.
            </p>
          </div>
        )}
      </TabsContent>

      <TabsContent value="expired">
        {expiredCoupons.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {expiredCoupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                id={coupon.id}
                brandName={coupon.brand?.name || ""}
                brandLogo={coupon.brand?.logo_url || ""}
                influencerName={userName || coupon.influencer?.full_name || ""}
                influencerImage={userAvatar || coupon.influencer?.avatar_url}
                discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% off`}
                expirationTime={coupon.expires_at || ""}
                couponCode={coupon.code}
                category={coupon.category?.name || ""}
                featured={coupon.featured}
                isPremium={coupon.is_premium}
                isLocked={false}
                price={coupon.price || 0}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">No Expired Coupons</h3>
            <p className="text-gray-600">
              This user hasn't created any expired coupons yet.
            </p>
          </div>
        )}
      </TabsContent>
    </Tabs>
  );
};

export default UserCouponTabs; 