import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { ShoppingBag } from 'lucide-react';
import { Brand } from '@/hooks/useBrands';

interface BrandCardProps {
  brand: Brand;
}

const BrandCard = ({ brand }: BrandCardProps) => {
  return (
    <Link 
      to={`/brands/${brand.id}`}
      className="block group"
    >
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300 h-full">
        <div className="p-4 flex flex-col h-full">
          {/* Brand Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center p-2 overflow-hidden">
              {brand.logo_url ? (
                <img 
                  src={brand.logo_url} 
                  alt={brand.name}
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-200 text-xl font-bold text-gray-400">
                  {brand.name.charAt(0)}
                </div>
              )}
            </div>
            <Badge 
              variant="outline" 
              className="text-xs font-medium"
            >
              <ShoppingBag className="w-3 h-3 mr-1" />
              {brand.coupons_count} coupons
            </Badge>
          </div>
          
          {/* Brand Details */}
          <div className="flex-grow">
            <h3 className="font-semibold text-lg mb-1 text-gray-800 group-hover:text-brand-blue-500 transition-colors">{brand.name}</h3>
            {brand.website && (
              <p className="text-xs text-gray-500 truncate">{brand.website.replace(/^https?:\/\//, '')}</p>
            )}
          </div>
          
          {/* Action Footer */}
          <div className="mt-4 pt-3 border-t border-gray-100">
            <p className="text-sm text-brand-blue-600 group-hover:text-brand-blue-700 transition-colors font-medium">
              View all coupons
            </p>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default BrandCard; 