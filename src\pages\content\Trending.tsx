import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, SortDesc, TrendingUp, Clock, CheckCircle, BadgePercent, X } from 'lucide-react';
import CouponCard from '@/components/CouponCard';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import BackButton from '@/components/BackButton';
import { useCoupons } from '@/hooks/useCoupons';
import { useAuth } from '@/context/AuthContext';
import { Badge } from '@/components/ui/badge';
import { simpleSearchCoupons } from '@/utils/searchUtils';

const Trending = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('trending');
  
  // Fetch real coupons from Supabase using the hook
  const { data: coupons, isLoading, error } = useCoupons({
    limit: 100,
    includeRelations: true
  });

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'fashion', label: 'Fashion' },
    { value: 'electronics', label: 'Electronics' },
    { value: 'food', label: 'Food & Dining' },
    { value: 'travel', label: 'Travel' },
    { value: 'beauty', label: 'Beauty' }
  ];

  const sortOptions = [
    { value: 'trending', label: 'Trending' },
    { value: 'newest', label: 'Newest First' },
    { value: 'expiring', label: 'Expiring Soon' }
  ];

  // Filter and sort coupons from the real data
  const filteredCoupons = coupons
    ? (() => {
        // First apply search filter using enhanced search
        let searchFiltered = searchQuery
          ? simpleSearchCoupons(coupons, searchQuery)
          : coupons;

        // Then apply category filter
        const categoryFiltered = searchFiltered.filter(coupon => {
          const matchesCategory = selectedCategory === 'all' ||
            (coupon.category?.name || '').toLowerCase() === selectedCategory.toLowerCase();
          return matchesCategory;
        });

        // Finally apply sorting
        return categoryFiltered.sort((a, b) => {
          switch (sortBy) {
            case 'trending':
              return (b.view_count || 0) - (a.view_count || 0);
            case 'newest':
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            case 'expiring':
              if (!a.expires_at) return 1;
              if (!b.expires_at) return -1;
              return new Date(a.expires_at).getTime() - new Date(b.expires_at).getTime();
            default:
              return 0;
          }
        });
      })()
    : [];

  return (
    <MainLayout fullWidth={false}>
      <div className="w-full max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <BackButton />
            <h1 className="text-2xl font-bold flex items-center">
              <TrendingUp className="h-6 w-6 text-pink-500 mr-2" />
              Trending Coupons
            </h1>
          </div>
          
          {user && (
            <Button
              variant="outline"
              className="gap-2 border-pink-200 text-pink-700 hover:bg-pink-50"
              onClick={() => navigate('/create-coupon')}
            >
              <BadgePercent className="h-4 w-4" />
              <span>Create Coupon</span>
            </Button>
          )}
        </div>
        
        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search by brand, influencer, category, code, or description..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <div className="flex items-center">
                    <Filter className="w-4 h-4 mr-2 text-pink-500" />
                    <SelectValue placeholder="Category" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <div className="flex items-center">
                    <SortDesc className="w-4 h-4 mr-2 text-pink-500" />
                    <SelectValue placeholder="Sort by" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Active filters display */}
          {(selectedCategory !== 'all' || searchQuery) && (
            <div className="mt-3 flex items-center gap-2">
              <span className="text-sm text-gray-500">Active filters:</span>
              {selectedCategory !== 'all' && (
                <Badge variant="outline" className="flex items-center gap-1 bg-pink-50 text-pink-700 border-pink-200">
                  {categories.find(c => c.value === selectedCategory)?.label}
                  <button 
                    className="ml-1"
                    onClick={() => setSelectedCategory('all')}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {searchQuery && (
                <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200">
                  Search: {searchQuery}
                  <button 
                    className="ml-1"
                    onClick={() => setSearchQuery('')}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              <Button 
                variant="link" 
                className="text-xs text-gray-500 py-0 h-auto"
                onClick={() => {
                  setSelectedCategory('all');
                  setSearchQuery('');
                }}
              >
                Clear all
              </Button>
            </div>
          )}
        </div>
        
        {/* Stats Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-xl p-4 border border-pink-100 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-pink-100 rounded-full">
                <TrendingUp className="h-5 w-5 text-pink-600" />
              </div>
              <h3 className="font-semibold">Trending Deals</h3>
            </div>
            <p className="text-sm text-gray-600">Discover coupons with the highest engagement</p>
          </div>
          
          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-4 border border-blue-100 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-blue-100 rounded-full">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-semibold">Expiring Soon</h3>
            </div>
            <p className="text-sm text-gray-600">Grab these deals before they're gone forever</p>
          </div>
          
          <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-xl p-4 border border-green-100 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-green-100 rounded-full">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-semibold">Verified Deals</h3>
            </div>
            <p className="text-sm text-gray-600">All deals are verified and tested by our team</p>
          </div>
        </div>
        
        {/* Coupons grid */}
        {isLoading ? (
          <CouponsSkeleton count={8} />
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-xl p-8 text-center text-red-700">
            <p>Error loading coupons. Please try again later.</p>
            <Button 
              variant="outline" 
              className="mt-4 border-red-200 text-red-600 hover:bg-red-50"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        ) : filteredCoupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCoupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                id={coupon.id}
                brandName={coupon.brand?.name || "Unknown Brand"}
                brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                influencerName={coupon.influencer?.full_name || "Anonymous"}
                influencerImage={coupon.influencer?.avatar_url}
                discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                couponCode={coupon.code}
                category={coupon.category?.name || "Uncategorized"}
                featured={coupon.featured}
                isPremium={coupon.is_premium}
                price={coupon.price}
              />
            ))}
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
            <h3 className="text-lg font-medium mb-2 text-gray-800">No Coupons Found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your filters or search query.</p>
            <Button 
              variant="outline" 
              onClick={() => {
                setSelectedCategory('all');
                setSearchQuery('');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Trending; 