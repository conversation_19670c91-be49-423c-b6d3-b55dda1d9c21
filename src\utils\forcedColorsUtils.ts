/**
 * Utilities for handling Forced Colors Mode (Windows High Contrast)
 * This replaces the deprecated -ms-high-contrast detection
 */

/**
 * Check if forced colors mode is active
 * This is the modern replacement for -ms-high-contrast detection
 */
export const isForcedColorsActive = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    // Modern way to detect forced colors mode
    return window.matchMedia('(forced-colors: active)').matches;
  } catch (error) {
    // Fallback for older browsers
    console.warn('Forced colors detection not supported:', error);
    return false;
  }
};

/**
 * Get the current forced colors preference
 */
export const getForcedColorsPreference = (): 'none' | 'active' | 'unknown' => {
  if (typeof window === 'undefined') return 'unknown';
  
  try {
    if (window.matchMedia('(forced-colors: active)').matches) {
      return 'active';
    }
    if (window.matchMedia('(forced-colors: none)').matches) {
      return 'none';
    }
    return 'unknown';
  } catch (error) {
    console.warn('Forced colors preference detection failed:', error);
    return 'unknown';
  }
};

/**
 * Listen for changes in forced colors mode
 */
export const onForcedColorsChange = (callback: (isActive: boolean) => void): (() => void) => {
  if (typeof window === 'undefined') return () => {};
  
  try {
    const mediaQuery = window.matchMedia('(forced-colors: active)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      callback(e.matches);
    };
    
    // Add listener
    mediaQuery.addEventListener('change', handleChange);
    
    // Return cleanup function
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  } catch (error) {
    console.warn('Forced colors change listener setup failed:', error);
    return () => {};
  }
};

/**
 * Apply forced colors aware styling
 */
export const getForcedColorsStyles = (normalStyles: React.CSSProperties, forcedColorsStyles: React.CSSProperties): React.CSSProperties => {
  if (isForcedColorsActive()) {
    return { ...normalStyles, ...forcedColorsStyles };
  }
  return normalStyles;
};

/**
 * CSS class names for forced colors mode
 */
export const forcedColorsClasses = {
  // System colors
  canvasText: 'text-[CanvasText]',
  canvas: 'bg-[Canvas]',
  buttonText: 'text-[ButtonText]',
  buttonFace: 'bg-[ButtonFace]',
  highlight: 'bg-[Highlight]',
  highlightText: 'text-[HighlightText]',
  linkText: 'text-[LinkText]',
  activeText: 'text-[ActiveText]',
  
  // Utility classes
  forcedColorsAuto: 'forced-color-adjust-auto',
  forcedColorsNone: 'forced-color-adjust-none',
} as const;

/**
 * Hook for React components to handle forced colors mode
 */
export const useForcedColors = () => {
  const [isActive, setIsActive] = React.useState(false);
  
  React.useEffect(() => {
    // Set initial state
    setIsActive(isForcedColorsActive());
    
    // Listen for changes
    const cleanup = onForcedColorsChange(setIsActive);
    
    return cleanup;
  }, []);
  
  return {
    isActive,
    preference: getForcedColorsPreference(),
    getStyles: getForcedColorsStyles,
    classes: forcedColorsClasses,
  };
};

// Import React for the hook
import React from 'react';

/**
 * Component wrapper that applies forced colors mode styles
 */
export const ForcedColorsProvider: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  const { isActive } = useForcedColors();
  
  return (
    <div 
      className={`${className} ${isActive ? 'forced-colors-active' : 'forced-colors-none'}`}
      data-forced-colors={isActive ? 'active' : 'none'}
    >
      {children}
    </div>
  );
};

/**
 * Utility to suppress -ms-high-contrast deprecation warnings
 * This function doesn't do anything functional, but helps with code organization
 */
export const suppressHighContrastDeprecationWarnings = () => {
  // This is a no-op function that serves as documentation
  // The actual suppression is handled by CSS overrides
  console.info('Using modern forced-colors mode instead of deprecated -ms-high-contrast');
};

/**
 * Get system color values in forced colors mode
 */
export const getSystemColors = () => {
  if (!isForcedColorsActive()) {
    return null;
  }
  
  try {
    const computedStyle = getComputedStyle(document.documentElement);
    
    return {
      canvas: computedStyle.getPropertyValue('Canvas'),
      canvasText: computedStyle.getPropertyValue('CanvasText'),
      buttonFace: computedStyle.getPropertyValue('ButtonFace'),
      buttonText: computedStyle.getPropertyValue('ButtonText'),
      highlight: computedStyle.getPropertyValue('Highlight'),
      highlightText: computedStyle.getPropertyValue('HighlightText'),
      linkText: computedStyle.getPropertyValue('LinkText'),
      activeText: computedStyle.getPropertyValue('ActiveText'),
    };
  } catch (error) {
    console.warn('Failed to get system colors:', error);
    return null;
  }
};

export default {
  isForcedColorsActive,
  getForcedColorsPreference,
  onForcedColorsChange,
  getForcedColorsStyles,
  useForcedColors,
  ForcedColorsProvider,
  forcedColorsClasses,
  suppressHighContrastDeprecationWarnings,
  getSystemColors,
};
