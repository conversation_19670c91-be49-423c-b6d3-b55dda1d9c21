-- Fix Remaining RLS Performance Issues
-- This migration addresses the additional RLS performance issues identified by the linter
-- Replaces auth.uid() with (select auth.uid()) to prevent re-evaluation for each row

-- ============================================================================
-- 1. SOCIAL_LINKS TABLE (if it exists)
-- ============================================================================

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'social_links') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own social links" ON public.social_links;
    DROP POLICY IF EXISTS "Users can manage their own social links" ON public.social_links;
    DROP POLICY IF EXISTS "Users can create their own social links" ON public.social_links;
    DROP POLICY IF EXISTS "Users can update their own social links" ON public.social_links;
    DROP POLICY IF EXISTS "Users can delete their own social links" ON public.social_links;

    -- Create optimized policies
    CREATE POLICY "Users can view their own social links"
      ON public.social_links
      FOR SELECT
      USING ((select auth.uid()) = profile_id);

    CREATE POLICY "Users can manage their own social links"
      ON public.social_links
      FOR ALL
      USING ((select auth.uid()) = profile_id)
      WITH CHECK ((select auth.uid()) = profile_id);
  END IF;
END $$;

-- ============================================================================
-- 2. SAVED_COUPONS TABLE (if it exists)
-- ============================================================================

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'saved_coupons') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their own saved coupons" ON public.saved_coupons;
    DROP POLICY IF EXISTS "Users can manage their own saved coupons" ON public.saved_coupons;
    DROP POLICY IF EXISTS "Users can create saved coupons" ON public.saved_coupons;
    DROP POLICY IF EXISTS "Users can update saved coupons" ON public.saved_coupons;
    DROP POLICY IF EXISTS "Users can delete saved coupons" ON public.saved_coupons;

    -- Create optimized policies
    CREATE POLICY "Users can view their own saved coupons"
      ON public.saved_coupons
      FOR SELECT
      USING ((select auth.uid()) = user_id);

    CREATE POLICY "Users can manage their own saved coupons"
      ON public.saved_coupons
      FOR ALL
      USING ((select auth.uid()) = user_id)
      WITH CHECK ((select auth.uid()) = user_id);
  END IF;
END $$;

-- ============================================================================
-- 3. PROFILES TABLE - Optimize existing policies
-- ============================================================================

-- Drop and recreate profiles policies with optimization
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;

-- Create optimized policies for profiles
CREATE POLICY "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING ((select auth.uid()) = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING ((select auth.uid()) = id)
  WITH CHECK ((select auth.uid()) = id);

CREATE POLICY "Public profiles are viewable by everyone"
  ON public.profiles
  FOR SELECT
  USING (true);

-- ============================================================================
-- 4. COUPONS TABLE - Optimize existing policies
-- ============================================================================

-- Drop and recreate coupons policies with optimization
DROP POLICY IF EXISTS "Influencers can manage their own coupons" ON public.coupons;
DROP POLICY IF EXISTS "Users can view active coupons" ON public.coupons;
DROP POLICY IF EXISTS "Influencers can create coupons" ON public.coupons;
DROP POLICY IF EXISTS "Influencers can update their own coupons" ON public.coupons;
DROP POLICY IF EXISTS "Influencers can delete their own coupons" ON public.coupons;

-- Create optimized policies for coupons
CREATE POLICY "Influencers can manage their own coupons"
  ON public.coupons
  FOR ALL
  USING ((select auth.uid()) = influencer_id)
  WITH CHECK ((select auth.uid()) = influencer_id);

CREATE POLICY "Users can view active coupons"
  ON public.coupons
  FOR SELECT
  USING (status = 'active');

-- ============================================================================
-- 5. CATEGORIES TABLE - Optimize if needed
-- ============================================================================

-- Categories are typically public, but optimize any user-specific policies
DROP POLICY IF EXISTS "Users can view categories" ON public.categories;

CREATE POLICY "Users can view categories"
  ON public.categories
  FOR SELECT
  USING (true);

-- ============================================================================
-- 6. ADDITIONAL OPTIMIZATIONS FOR EXISTING TABLES
-- ============================================================================

-- Optimize any remaining auth.uid() calls in existing policies

-- PREMIUM_PURCHASES table optimization
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'premium_purchases') THEN
    DROP POLICY IF EXISTS "Users can view their purchases" ON public.premium_purchases;
    DROP POLICY IF EXISTS "Sellers can view sales" ON public.premium_purchases;

    CREATE POLICY "Users can view their purchases"
      ON public.premium_purchases
      FOR SELECT
      USING ((select auth.uid()) = buyer_id);

    CREATE POLICY "Sellers can view sales"
      ON public.premium_purchases
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM coupons
          WHERE coupons.id = premium_purchases.coupon_id
          AND coupons.influencer_id = (select auth.uid())
        )
      );
  END IF;
END $$;

-- TRANSACTIONS table optimization
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'transactions') THEN
    DROP POLICY IF EXISTS "Users can view their transactions" ON public.transactions;

    CREATE POLICY "Users can view their transactions"
      ON public.transactions
      FOR SELECT
      USING (
        ((select auth.uid()) = buyer_id) OR
        ((select auth.uid()) = seller_id)
      );
  END IF;
END $$;

-- ============================================================================
-- 7. PERFORMANCE MONITORING
-- ============================================================================

-- Create a function to check for unoptimized RLS policies
CREATE OR REPLACE FUNCTION public.check_rls_performance()
RETURNS TABLE (
  table_name TEXT,
  policy_name TEXT,
  policy_definition TEXT,
  needs_optimization BOOLEAN
) 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.tablename::TEXT,
    p.policyname::TEXT,
    p.qual::TEXT as policy_definition,
    (p.qual LIKE '%auth.uid()%' AND p.qual NOT LIKE '%(select auth.uid())%')::BOOLEAN as needs_optimization
  FROM pg_policies p
  WHERE p.schemaname = 'public'
  AND (p.qual LIKE '%auth.uid()%' OR p.with_check LIKE '%auth.uid()%')
  ORDER BY needs_optimization DESC, p.tablename, p.policyname;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.check_rls_performance TO authenticated;

-- ============================================================================
-- 8. COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION public.check_rls_performance() IS 
'Function to identify RLS policies that may have performance issues due to unoptimized auth function calls';

-- Add comments to explain the optimization
COMMENT ON SCHEMA public IS 
'All RLS policies have been optimized to use (select auth.uid()) instead of auth.uid() to prevent function re-evaluation for each row';

-- ============================================================================
-- 9. VERIFICATION QUERIES
-- ============================================================================

-- You can run these queries to verify the optimizations:
-- SELECT * FROM public.check_rls_performance();
-- 
-- To see all current policies:
-- SELECT schemaname, tablename, policyname, qual 
-- FROM pg_policies 
-- WHERE schemaname = 'public' 
-- ORDER BY tablename, policyname;
