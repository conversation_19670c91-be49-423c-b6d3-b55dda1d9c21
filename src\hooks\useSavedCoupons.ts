import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Coupon } from './useCoupons';
import { toast } from 'sonner';

export interface Collection {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  coupon_count?: number;
}

export interface SavedCoupon {
  id: string;
  user_id: string;
  coupon_id: string;
  collection_id: string | null;
  notes: string | null;
  saved_at: string;
  coupon?: Coupon;
}

// Check if a coupon is saved by the current user
export const useIsCouponSaved = (userId?: string, couponId?: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['is-coupon-saved', userId, couponId],
    queryFn: async () => {
      if (!userId || !couponId) return false;
      
      try {
        const { data, error } = await supabase
          .from('saved_coupons')
          .select('id')
          .eq('user_id', userId)
          .eq('coupon_id', couponId)
          .maybeSingle();
        
        if (error) throw error;
        
        return !!data;
      } catch (error) {
        console.error('Error checking if coupon is saved:', error);
        return false;
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled : !!userId && !!couponId,
    retry: 1,
    staleTime: 30000, // 30 seconds
  });
};

// Get all saved coupons for a user
export const useSavedCoupons = (userId?: string, collectionId?: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['saved-coupons', userId, collectionId],
    queryFn: async () => {
      if (!userId) return [];
      
      try {
        let query = supabase
          .from('saved_coupons')
          .select(`
            *,
            coupon:coupons(
              *,
              brand:brands(*),
              influencer:profiles(id, full_name, username, avatar_url),
              category:categories(*)
            )
          `)
          .eq('user_id', userId);
        
        // Filter by collection if specified
        if (collectionId) {
          query = query.eq('collection_id', collectionId);
        }
        
        const { data, error } = await query.order('saved_at', { ascending: false });
        
        if (error) throw error;
        
        return data || [];
      } catch (error) {
        console.error('Error fetching saved coupons:', error);
        throw error;
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled : !!userId,
    retry: 1,
  });
};

// Get all collections for the current user
export const useCollections = (userId?: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['collections', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      try {
        const { data, error } = await supabase
          .from('collections')
          .select('*')
          .eq('user_id', userId)
          .order('name');
        
        if (error) throw error;
        
        // Get count of coupons in each collection
        const collectionsWithCount = await Promise.all(
          data.map(async (collection) => {
            try {
              const { count, error: countError } = await supabase
                .from('saved_coupons')
                .select('*', { count: 'exact', head: true })
                .eq('collection_id', collection.id);
              
              if (countError) throw countError;
              
              return {
                ...collection,
                coupon_count: count || 0
              };
            } catch (err) {
              console.error(`Error getting count for collection ${collection.id}:`, err);
              return {
                ...collection,
                coupon_count: 0
              };
            }
          })
        );
        
        return collectionsWithCount as Collection[];
      } catch (error) {
        console.error('Error fetching collections:', error);
        throw error;
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled : !!userId,
    retry: 1,
  });
};

// Save a coupon
export const useSaveCoupon = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      userId, 
      couponId, 
      collectionId = null,
      notes = '' 
    }: { 
      userId: string;
      couponId: string;
      collectionId?: string | null;
      notes?: string;
    }) => {
      try {
        console.log("Saving coupon with data:", { userId, couponId, collectionId, notes });
        
        // Check if the coupon is already saved
        const { data: existingData, error: checkError } = await supabase
          .from('saved_coupons')
          .select('id')
          .eq('user_id', userId)
          .eq('coupon_id', couponId)
          .maybeSingle();
          
        if (checkError) {
          console.error("Error checking if coupon exists:", checkError);
          throw new Error(`Error checking for existing saved coupon: ${checkError.message}`);
        }
        
        // If coupon is already saved, update it
        if (existingData?.id) {
          console.log("Coupon already saved, updating:", existingData.id);
          const { error: updateError } = await supabase
            .from('saved_coupons')
            .update({
              collection_id: collectionId,
              notes,
              saved_at: new Date()
            })
            .eq('id', existingData.id);
            
          if (updateError) {
            console.error("Error updating saved coupon:", updateError);
            throw new Error(`Failed to update saved coupon: ${updateError.message}`);
          }
          
          return { id: existingData.id, action: 'updated' };
        }
        
        // Otherwise insert a new saved coupon
        console.log("Inserting new saved coupon");
        const { data: insertData, error: insertError } = await supabase
          .from('saved_coupons')
          .insert({
            user_id: userId,
            coupon_id: couponId,
            collection_id: collectionId,
            notes,
            saved_at: new Date()
          })
          .select('id')
          .single();
          
        if (insertError) {
          console.error("Error inserting saved coupon:", insertError);
          
          // Check if this is a foreign key constraint error (likely on collection_id)
          if (insertError.code === '23503') {
            console.log("Foreign key constraint error, trying without collection_id");
            // Try again without the collection ID
            const { data: retryData, error: retryError } = await supabase
              .from('saved_coupons')
              .insert({
                user_id: userId,
                coupon_id: couponId,
                notes,
                saved_at: new Date()
              })
              .select('id')
              .single();
              
            if (retryError) {
              console.error("Error on retry:", retryError);
              throw new Error(`Failed to save coupon: ${retryError.message}`);
            }
            
            return { id: retryData.id, action: 'inserted' };
          }
          
          throw new Error(`Failed to save coupon: ${insertError.message}`);
        }
        
        return { id: insertData.id, action: 'inserted' };
      } catch (error: any) {
        console.error('Error in save coupon mutation:', error);
        // Return a standardized error to prevent component crash
        throw new Error(error?.message || 'Failed to save coupon');
      }
    },
    onSuccess: (_, variables) => {
      try {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['saved-coupons', variables.userId] });
        queryClient.invalidateQueries({ queryKey: ['is-coupon-saved', variables.userId, variables.couponId] });
        if (variables.collectionId) {
          queryClient.invalidateQueries({ 
            queryKey: ['saved-coupons', variables.userId, variables.collectionId] 
          });
        }
      } catch (error) {
        console.error('Error refreshing cache after saving coupon:', error);
      }
    },
    onError: (error: any) => {
      console.error('Mutation error:', error);
      const errorMessage = error?.message || 'Failed to save coupon. Please check your database setup.';
      toast.error(errorMessage);
    }
  });
};

// Remove a saved coupon
export const useRemoveSavedCoupon = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      userId, 
      couponId 
    }: { 
      userId: string;
      couponId: string;
    }) => {
      try {
        const { error } = await supabase
          .from('saved_coupons')
          .delete()
          .eq('user_id', userId)
          .eq('coupon_id', couponId);
        
        if (error) throw error;
        
        return { userId, couponId };
      } catch (error) {
        console.error('Error removing saved coupon:', error);
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['saved-coupons', variables.userId] });
      queryClient.invalidateQueries({ queryKey: ['is-coupon-saved', variables.userId, variables.couponId] });
      queryClient.invalidateQueries({ queryKey: ['collections', variables.userId] });
      toast.success('Coupon removed from saved items');
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast.error('Failed to remove coupon. Please try again.');
    }
  });
};

// Create a new collection
export const useCreateCollection = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      userId, 
      name, 
      description = '',
      isDefault = false
    }: { 
      userId: string;
      name: string;
      description?: string;
      isDefault?: boolean;
    }) => {
      try {
        const { data, error } = await supabase
          .from('collections')
          .insert({
            user_id: userId,
            name,
            description,
            is_default: isDefault
          })
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error creating collection:', error);
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['collections', variables.userId] });
      toast.success('Collection created successfully');
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast.error('Failed to create collection. Please check your database setup.');
    }
  });
};

// Update a collection
export const useUpdateCollection = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      collectionId, 
      name, 
      description,
      isDefault
    }: { 
      collectionId: string;
      name?: string;
      description?: string;
      isDefault?: boolean;
    }) => {
      try {
        const updateData: any = { updated_at: new Date().toISOString() };
        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (isDefault !== undefined) updateData.is_default = isDefault;
        
        const { data, error } = await supabase
          .from('collections')
          .update(updateData)
          .eq('id', collectionId)
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error updating collection:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['collections'] });
      toast.success('Collection updated successfully');
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast.error('Failed to update collection. Please try again.');
    }
  });
};

// Delete a collection
export const useDeleteCollection = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      collectionId, 
      userId,
      deleteCoupons = false
    }: { 
      collectionId: string;
      userId: string;
      deleteCoupons?: boolean;
    }) => {
      try {
        if (deleteCoupons) {
          // Delete all saved coupons in this collection
          const { error: deleteError } = await supabase
            .from('saved_coupons')
            .delete()
            .eq('collection_id', collectionId)
            .eq('user_id', userId);
            
          if (deleteError) throw deleteError;
        } else {
          // Just remove the collection_id, keeping the saved coupons
          const { error: updateError } = await supabase
            .from('saved_coupons')
            .update({ collection_id: null })
            .eq('collection_id', collectionId)
            .eq('user_id', userId);
            
          if (updateError) throw updateError;
        }
        
        // Delete the collection
        const { error } = await supabase
          .from('collections')
          .delete()
          .eq('id', collectionId)
          .eq('user_id', userId);
        
        if (error) throw error;
        return { collectionId, userId };
      } catch (error) {
        console.error('Error deleting collection:', error);
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['collections', variables.userId] });
      queryClient.invalidateQueries({ queryKey: ['saved-coupons', variables.userId] });
      toast.success('Collection deleted successfully');
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast.error('Failed to delete collection. Please try again.');
    }
  });
}; 