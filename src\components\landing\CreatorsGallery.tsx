import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Star, Users, Award } from 'lucide-react';
import { COLORS } from '@/constants/theme';

interface ProfileThumbnailProps {
  name: string;
  color: string;
  followers: string;
  rotation: number;
  delay: number;
  badgeColor: string;
  category?: string;
  image?: string;
}

const ProfileThumbnail: React.FC<ProfileThumbnailProps> = ({ 
  name, 
  color, 
  followers, 
  rotation, 
  delay, 
  badgeColor,
  category,
  image
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20, rotate: rotation * 2 }}
    whileInView={{ opacity: 1, y: 0, rotate: rotation }}
    transition={{ duration: 0.5, delay }}
    viewport={{ once: true }}
    className="group"
  >
    <div className="relative aspect-square h-20 md:h-24 bg-gradient-to-br from-gray-50 to-gray-100 rounded-full overflow-hidden shadow-md border-2 border-white hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 hover:rotate-0">
      <img 
        src={image || `https://ui-avatars.com/api/?name=${name.replace(' ', '+')}&background=${color}&color=fff&size=150`}
        alt={name}
        className="w-full h-full object-cover"
        loading="lazy"
        onError={(e) => e.currentTarget.style.display = 'none'}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end items-center p-2">
        <h3 className="text-white font-medium text-sm truncate text-center">{name}</h3>
        <p className="text-white/90 text-xs truncate text-center">{followers}</p>
        {category && (
          <span className="text-[8px] bg-white/30 text-white px-1.5 py-0.5 rounded-full mt-0.5">
            {category}
          </span>
        )}
      </div>
      {badgeColor === 'primary' && (
        <div className="absolute -bottom-0.5 -right-0.5 bg-primary-main text-white p-1.5 rounded-full shadow-lg">
          <CheckCircle className="w-2.5 h-2.5" />
        </div>
      )}
      {badgeColor === 'secondary' && (
        <div className="absolute -bottom-0.5 -right-0.5 bg-secondary-main text-white p-1.5 rounded-full shadow-lg">
          <CheckCircle className="w-2.5 h-2.5" />
        </div>
      )}
      {badgeColor === 'tertiary' && (
        <div className="absolute -bottom-0.5 -right-0.5 bg-tertiary-main text-white p-1.5 rounded-full shadow-lg">
          <CheckCircle className="w-2.5 h-2.5" />
        </div>
      )}
    </div>
  </motion.div>
);

const CreatorsGallery = () => {
  // Animation variants for the section
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    whileInView: { opacity: 1, y: 0 },
    transition: { duration: 0.5 },
    viewport: { once: true }
  };

  // Updated creators data with categories and real images
  const creators = [
    { name: 'Emma Roberts', followers: '125K', color: 'FF6B6B', rotation: -5, delay: 0.1, badgeColor: 'primary', category: 'Fashion', image: 'https://randomuser.me/api/portraits/women/44.jpg' },
    { name: 'Alex Chen', followers: '89K', color: '4ECDC4', rotation: 3, delay: 0.2, badgeColor: 'primary', category: 'Tech', image: 'https://randomuser.me/api/portraits/men/22.jpg' },
    { name: 'Mia Johnson', followers: '230K', color: 'FF9F1C', rotation: -3, delay: 0.3, badgeColor: 'primary', category: 'Lifestyle', image: 'https://randomuser.me/api/portraits/women/32.jpg' },
    { name: 'Noah Williams', followers: '72K', color: '6A0572', rotation: 5, delay: 0.15, badgeColor: 'secondary', category: 'Gaming', image: 'https://randomuser.me/api/portraits/men/32.jpg' },
    { name: 'Sarah Parker', followers: '310K', color: '1A936F', rotation: -6, delay: 0.25, badgeColor: 'secondary', category: 'Fitness', image: 'https://randomuser.me/api/portraits/women/68.jpg' },
    { name: 'Daniel Brown', followers: '183K', color: 'E71D36', rotation: 4, delay: 0.35, badgeColor: 'secondary', category: 'Food', image: 'https://randomuser.me/api/portraits/men/15.jpg' },
    { name: 'Taylor Swift', followers: '92M', color: '540D6E', rotation: -2, delay: 0.4, badgeColor: 'tertiary', category: 'Music', image: 'https://randomuser.me/api/portraits/women/12.jpg' },
    { name: 'Ryan Reynolds', followers: '44M', color: '3D348B', rotation: 3, delay: 0.45, badgeColor: 'tertiary', category: 'Movies', image: 'https://randomuser.me/api/portraits/men/11.jpg' },
    { name: 'Zoe Miller', followers: '65K', color: 'F18F01', rotation: -4, delay: 0.5, badgeColor: 'primary', category: 'Travel', image: 'https://randomuser.me/api/portraits/women/54.jpg' }
  ];

  return (
    <section className="w-full py-16 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/patterns/circles.svg')] opacity-[0.025] mix-blend-overlay"></div>
      <div className="absolute -left-20 top-20 w-60 h-60 bg-primary-bgLight rounded-full blur-[70px] opacity-40"></div>
      <div className="absolute -right-20 bottom-20 w-60 h-60 bg-secondary-bgLight rounded-full blur-[70px] opacity-40"></div>
      
      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <motion.div
          {...fadeIn}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center px-4 py-1.5 bg-primary-bgLight text-primary-main rounded-full mb-4 text-sm font-medium">
            <Star className="w-4 h-4 mr-2" />
            Join Our Growing Community
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-3 bg-clip-text text-transparent" style={{ backgroundImage: `linear-gradient(to right, ${COLORS.primary.main}, ${COLORS.secondary.main})` }}>
            Trusted by 10,000+ Creators
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Join the thousands of content creators, influencers, and brands who trust CouponLink to manage and share their promotional codes.
          </p>
        </motion.div>
        
        {/* Stats Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-12"
        >
          <div className="bg-white rounded-xl py-3 px-6 shadow-md flex items-center border border-gray-100">
            <div className="mr-3 bg-primary-bgLight h-10 w-10 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-primary-main" />
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-main">10K+</div>
              <div className="text-sm text-gray-500">Active Creators</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl py-3 px-6 shadow-md flex items-center border border-gray-100">
            <div className="mr-3 bg-secondary-bgLight h-10 w-10 rounded-full flex items-center justify-center">
              <Star className="h-5 w-5 text-secondary-main" />
            </div>
            <div>
              <div className="text-2xl font-bold text-secondary-main">150M+</div>
              <div className="text-sm text-gray-500">Combined Audience</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl py-3 px-6 shadow-md flex items-center border border-gray-100">
            <div className="mr-3 bg-tertiary-bgLight h-10 w-10 rounded-full flex items-center justify-center">
              <Award className="h-5 w-5 text-tertiary-main" />
            </div>
            <div>
              <div className="text-2xl font-bold text-tertiary-main">98%</div>
              <div className="text-sm text-gray-500">Satisfaction Rate</div>
            </div>
          </div>
        </motion.div>
        
        {/* Creator Profiles Grid - Scattered Design */}
        <div className="flex items-center justify-center py-8 overflow-visible mx-auto max-w-4xl">
          {/* First row */}
          <div className="flex justify-center -mt-6 z-10">
            <div className="transform translate-x-4">{creators[0] && <ProfileThumbnail {...creators[0]} />}</div>
            <div className="transform -translate-y-10 -translate-x-4">{creators[1] && <ProfileThumbnail {...creators[1]} />}</div>
            <div className="transform translate-y-6">{creators[2] && <ProfileThumbnail {...creators[2]} />}</div>
          </div>
        </div>
        
        {/* Middle row - more prominent */}
        <div className="flex justify-center -mt-8 items-center z-20">
          <div className="transform -translate-x-16">{creators[3] && <ProfileThumbnail {...creators[3]} />}</div>
          <div className="transform scale-125 z-30">{creators[4] && <ProfileThumbnail {...creators[4]} />}</div>
          <div className="transform translate-x-16">{creators[5] && <ProfileThumbnail {...creators[5]} />}</div>
        </div>
        
        {/* Last row */}
        <div className="flex justify-center -mt-8 z-10">
          <div className="transform translate-x-8 translate-y-4">{creators[6] && <ProfileThumbnail {...creators[6]} />}</div>
          <div className="transform -translate-y-6">{creators[7] && <ProfileThumbnail {...creators[7]} />}</div>
          <div className="transform -translate-x-8 translate-y-2">{creators[8] && <ProfileThumbnail {...creators[8]} />}</div>
        </div>
      </div>
    </section>
  );
};

export default CreatorsGallery; 