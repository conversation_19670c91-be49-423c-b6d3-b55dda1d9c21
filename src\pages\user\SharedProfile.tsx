import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { 
  User,
  Facebook,
  Twitter,
  Linkedin,
  Link as LinkIcon,
  Tag,
  Star,
  Check,
  Copy,
  ExternalLink,
  Clock,
  MapPin,
  BarChart3,
  Award
} from 'lucide-react';
import { Coupon } from '@/types/coupon';
import { toast } from 'sonner';
import { COLORS } from '@/constants/theme';
import { fixDomainName } from '@/utils/domainFix';

// Define a custom Profile type
interface Profile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string | null;
  bio?: string;
  website?: string;
  social_links?: Array<{ platform: string; url: string }>;
  is_verified?: boolean | null;
}

interface SharedProfileProps {
  user: Profile | undefined;
  userLoading: boolean;
  coupons: Coupon[];
  couponsLoading: boolean;
  profileUrl: string;
  copyToClipboard: () => void;
  shareProfile: () => void;
  copied: boolean;
  shouldHighlightShare: boolean;
  shareSectionRef: React.RefObject<HTMLDivElement>;
}

const SharedProfile: React.FC<SharedProfileProps> = ({
  user: displayUser,
  userLoading,
  coupons: displayCoupons,
  couponsLoading,
  profileUrl,
  shouldHighlightShare,
  shareSectionRef
}) => {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  
  // Calculate active coupons count
  const activeCouponsCount = displayCoupons?.filter(c => c.status === 'active').length || 0;
  
  // Calculate premium coupons count
  const premiumCouponsCount = displayCoupons?.filter(c => 'is_premium' in c && c.is_premium).length || 0;
  
  // Handle copying coupon code
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    toast.success('Coupon code copied to clipboard!');
    setTimeout(() => setCopiedCode(null), 2000);
  };
  
  // Function to render content skeleton during loading
  const renderSkeletons = () => (
    <div className="animate-pulse max-w-md mx-auto">
      <div className="flex flex-col items-center mb-8">
        <div className="w-24 h-24 rounded-full bg-gray-200 mb-4" />
        <div className="h-6 bg-gray-200 rounded w-40 mb-2" />
        <div className="h-4 bg-gray-200 rounded w-28 mb-6" />
        <div className="w-full h-20 bg-gray-200 rounded mb-8" />
      </div>
      <div className="space-y-4">
        <div className="h-32 bg-gray-200 rounded-xl w-full" />
        <div className="h-32 bg-gray-200 rounded-xl w-full" />
        <div className="h-32 bg-gray-200 rounded-xl w-full" />
      </div>
    </div>
  );

  return (
    <div className="bg-neutral-50 min-h-screen py-8">
      <div className="max-w-md mx-auto px-4">
        {userLoading ? (
          renderSkeletons()
        ) : (
          <>
            {/* Profile card with clean design */}
            <div className="mb-8 bg-white rounded-xl shadow-md overflow-hidden border border-neutral-100">
              <div className="pt-6 px-6 pb-6 text-center">
                {/* Profile photo */}
                <div className="mb-4 mx-auto">
                  <div className="w-24 h-24 rounded-full overflow-hidden shadow-md border-4 border-white mx-auto">
                    <img
                      src={getAvatarUrl(displayUser?.avatar_url, displayUser?.id, displayUser?.username)}
                      alt={displayUser?.full_name || 'User'}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                
                {/* User info */}
                <div className="mb-4">
                  <h1 className="text-xl font-semibold text-neutral-800 mb-1">{displayUser?.full_name || "Influencer"}</h1>
                  <div className="flex items-center justify-center text-neutral-500 text-sm">
                    @{displayUser?.username || "username"}
                    {displayUser?.is_verified && (
                      <span className="inline-flex items-center justify-center bg-tertiary-main rounded-full w-4 h-4 ml-1.5" title="Verified Account">
                        <Check className="w-2.5 h-2.5 text-white" strokeWidth={3} />
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Bio */}
                {displayUser?.bio && (
                  <div className="mb-5">
                    <p className="text-neutral-600 text-sm leading-relaxed text-center mx-auto">
                      {displayUser.bio}
                    </p>
                  </div>
                )}
                
                {/* Stats */}
                <div className="flex gap-3 mb-5 justify-center">
                  <div className="py-1.5 px-3 bg-primary-bgLight rounded-lg">
                    <div className="flex items-center text-primary-dark">
                      <Tag className="w-3.5 h-3.5 mr-1.5" />
                      <span className="text-sm font-medium">{activeCouponsCount} Active</span>
                    </div>
                  </div>
                  
                  <div className="py-1.5 px-3 bg-secondary-bgLight rounded-lg">
                    <div className="flex items-center text-secondary-dark">
                      <Star className="w-3.5 h-3.5 mr-1.5" />
                      <span className="text-sm font-medium">{premiumCouponsCount} Premium</span>
                    </div>
                  </div>
                </div>
                
                {/* Social Links */}
                <div className="flex flex-wrap justify-center gap-2.5 mb-1">
                  {displayUser?.social_links && Array.isArray(displayUser.social_links) && displayUser.social_links.map((link, index) => {
                    let icon = null;
                    let bgColor = "";
                    
                    if (link.platform === "twitter") {
                      icon = <Twitter className="w-3.5 h-3.5" />;
                      bgColor = "bg-tertiary-main";
                    } else if (link.platform === "instagram") {
                      icon = (
                        <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" />
                        </svg>
                      );
                      bgColor = "bg-accent-main";
                    } else if (link.platform === "youtube") {
                      icon = (
                        <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                        </svg>
                      );
                      bgColor = "bg-accent-main";
                    } else if (link.platform === "facebook") {
                      icon = <Facebook className="w-3.5 h-3.5" />;
                      bgColor = "bg-tertiary-main";
                    } else if (link.platform === "linkedin") {
                      icon = <Linkedin className="w-3.5 h-3.5" />;
                      bgColor = "bg-tertiary-main";
                    } else {
                      icon = <LinkIcon className="w-3.5 h-3.5" />;
                      bgColor = "bg-neutral-600";
                    }
                    
                    return (
                      <a 
                        key={index}
                        href={link.url} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className={`${bgColor} text-white p-1.5 rounded-md hover:opacity-90 shadow-sm flex items-center justify-center`}
                        aria-label={`${link.platform} profile`}
                      >
                        {icon}
                      </a>
                    );
                  })}
                  
                  {displayUser?.website && (
                    <a 
                      href={displayUser.website} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="bg-neutral-600 text-white p-1.5 rounded-md hover:opacity-90 shadow-sm flex items-center justify-center"
                      aria-label="Website"
                    >
                      <LinkIcon className="w-3.5 h-3.5" />
                    </a>
                  )}
                </div>
              </div>
            </div>
            
            {/* Deals Header */}
            <div className="mb-5">
              <h2 className="text-center text-lg font-medium text-neutral-800 flex items-center justify-center gap-2">
                <Award className="w-4 h-4 text-primary-main" />
                Exclusive Deals
              </h2>
              <div className="h-0.5 w-16 mx-auto mt-2 bg-primary-main rounded-full"></div>
            </div>
            
            {/* Coupon Cards */}
            <div className="space-y-4 mb-8">
              {couponsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-32 bg-neutral-200 rounded-lg w-full"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  {displayCoupons.length === 0 ? (
                    <div className="text-center py-8 bg-white rounded-lg shadow-sm border border-neutral-100">
                      <Tag className="h-8 w-8 text-neutral-300 mx-auto mb-2" />
                      <h3 className="text-neutral-700 font-medium mb-1">No Deals Available</h3>
                      <p className="text-neutral-500 text-sm">This influencer hasn't added any coupon deals yet.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {displayCoupons.map((coupon) => {
                        // Format the discount text
                        const discountText = (coupon as any).discount_percent 
                          ? `${(coupon as any).discount_percent}% OFF` 
                          : (coupon as any).discount_description || 'Special Deal';
                        
                        // Format expiration date
                        const expiryDate = (coupon as any).expires_at || ('expiry_date' in coupon ? coupon.expiry_date : undefined);
                        const formattedExpiryDate = expiryDate ? new Date(expiryDate).toLocaleDateString() : '';
                        
                        // Is premium
                        const isPremium = 'is_premium' in coupon && (coupon as any).is_premium;
                        
                        return (
                          <div 
                            key={coupon.id} 
                            className="bg-white rounded-lg shadow-sm border border-neutral-100 overflow-hidden"
                          >
                            {/* Premium ribbon (if premium) */}
                            {isPremium && (
                              <div className="w-full h-1.5 bg-gradient-to-r from-secondary-main to-secondary-dark"></div>
                            )}
                            
                            <div className="p-4">
                              {/* Brand and discount info */}
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2.5">
                                  {/* Brand logo */}
                                  <div className="w-10 h-10 rounded overflow-hidden bg-neutral-50 border border-neutral-100 flex-shrink-0 flex items-center justify-center">
                                    {coupon.brand?.logo_url ? (
                                      <img 
                                        src={coupon.brand.logo_url} 
                                        alt={coupon.brand.name || 'Brand'} 
                                        className="w-full h-full object-contain" 
                                      />
                                    ) : (
                                      <div className="w-full h-full bg-gradient-to-br from-tertiary-main to-tertiary-dark flex items-center justify-center">
                                        <span className="text-white font-medium text-sm">
                                          {(coupon.brand?.name?.charAt(0) || 'B').toUpperCase()}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                  
                                  {/* Brand name and title */}
                                  <div className="min-w-0">
                                    <h3 className="font-medium text-neutral-800 text-sm truncate">
                                      {coupon.brand?.name || 'Brand'}
                                    </h3>
                                    <p className="text-xs text-neutral-500 truncate">
                                      {coupon.title || 'Discount Coupon'}
                                    </p>
                                  </div>
                                </div>
                                
                                {/* Premium badge */}
                                {isPremium && (
                                  <span className="inline-flex items-center bg-secondary-bgLight text-secondary-dark text-xs px-2 py-0.5 rounded">
                                    <Star className="w-3 h-3 mr-0.5 fill-secondary-main text-secondary-main" />
                                    Premium
                                  </span>
                                )}
                              </div>
                              
                              {/* Discount info */}
                              <div className="mb-3 flex justify-between items-center">
                                <div className="flex items-center">
                                  <BarChart3 className="text-primary-main w-3.5 h-3.5 mr-1" />
                                  <p className="text-primary-main font-medium">
                                    {discountText}
                                  </p>
                                </div>
                                
                                {expiryDate && (
                                  <p className="text-xs text-neutral-500 flex items-center whitespace-nowrap">
                                    <Clock className="w-3 h-3 mr-1" />
                                    Expires: {formattedExpiryDate}
                                  </p>
                                )}
                              </div>
                              
                              {/* Coupon code */}
                              <div className="bg-neutral-50 border border-neutral-200 rounded p-2 mb-3 flex justify-between items-center">
                                <div className="font-mono text-sm text-neutral-800">
                                  {coupon.code}
                                </div>
                                <Button 
                                  size="sm"
                                  variant="ghost"
                                  className={`h-7 px-2 ${
                                    copiedCode === coupon.code 
                                      ? 'text-primary-main bg-primary-bgLight' 
                                      : 'text-tertiary-main'
                                  }`}
                                  onClick={() => handleCopyCode(coupon.code)}
                                >
                                  {copiedCode === coupon.code ? (
                                    <span className="flex items-center text-xs">
                                      <Check className="w-3 h-3 mr-1" />
                                      Copied!
                                    </span>
                                  ) : (
                                    <span className="flex items-center text-xs">
                                      <Copy className="w-3 h-3 mr-1" />
                                      Copy
                                    </span>
                                  )}
                                </Button>
                              </div>
                              
                              {/* Shop link */}
                              {(coupon as any).affiliate_link && (
                                <a
                                  href={fixDomainName((coupon as any).affiliate_link)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block w-full text-center bg-primary-main hover:bg-primary-dark text-white text-xs font-medium py-2 rounded transition-colors"
                                >
                                  <span className="flex items-center justify-center">
                                    Shop Now
                                    <ExternalLink className="w-3 h-3 ml-1.5" />
                                  </span>
                                </a>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </>
              )}
            </div>
            
            {/* Footer */}
            <div 
              className="bg-white rounded-lg shadow-sm border border-neutral-100 overflow-hidden mb-6"
              ref={shareSectionRef}
            >
              <div className="px-4 py-3.5 flex items-center justify-between gap-3">
                <div className="flex items-center gap-2.5">
                  <img 
                    src="/logos/logo.png" 
                    alt="CouponLink Logo" 
                    className="w-8 h-8 object-contain" 
                  />
                  <div>
                    <h3 className="text-neutral-800 font-medium text-sm">CouponLink</h3>
                    <p className="text-neutral-500 text-xs">Share your codes!</p>
                  </div>
                </div>
                <Button 
                  asChild 
                  size="sm"
                  className="bg-primary-main hover:bg-primary-dark text-white h-8"
                >
                  <Link to="/landing">Join Now</Link>
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SharedProfile;
