#!/usr/bin/env node

/**
 * Security Fixes Application Script
 * 
 * This script helps apply the security fixes identified by Supabase linter.
 * It handles both database migrations and provides guidance for manual auth configuration.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔒 Applying Security Fixes for Supabase Database\n');

// Check if we're in the right directory
if (!fs.existsSync('supabase/config.toml')) {
  console.error('❌ Error: This script must be run from the project root directory.');
  console.error('   Make sure you\'re in the directory containing supabase/config.toml');
  process.exit(1);
}

// Function to run shell commands
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} completed successfully`);
    return output;
  } catch (error) {
    console.error(`❌ Error during ${description}:`);
    console.error(error.message);
    return null;
  }
}

// Function to check if Supabase CLI is available
function checkSupabaseCLI() {
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    console.log('ℹ️  Supabase CLI not found. You can either:');
    console.log('   1. Install it: npm install -g supabase');
    console.log('   2. Apply migrations manually through Supabase Dashboard');
    return false;
  }
}

// Function to apply database migrations
function applyDatabaseFixes(hasSupabaseCLI) {
  console.log('\n🗄️  Database Security Fixes\n');

  // Check if migrations exist
  const migration1 = 'supabase/migrations/20250618000001_fix_security_issues.sql';
  const migration2 = 'supabase/migrations/20250618000002_fix_auth_security_settings.sql';

  if (!fs.existsSync(migration1)) {
    console.error(`❌ Migration file not found: ${migration1}`);
    return false;
  }

  if (!fs.existsSync(migration2)) {
    console.error(`❌ Migration file not found: ${migration2}`);
    return false;
  }

  console.log('📁 Found security fix migrations:');
  console.log(`   - ${migration1}`);
  console.log(`   - ${migration2}`);

  if (hasSupabaseCLI) {
    // Apply migrations using CLI
    const result = runCommand('supabase db push', 'Applying database migrations');
    return result !== null;
  } else {
    // Provide manual instructions
    console.log('\n📋 Manual Migration Steps:');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the contents of each migration file:');
    console.log(`   - ${migration1}`);
    console.log(`   - ${migration2}`);
    console.log('3. Execute each migration in order');
    return true;
  }
}

// Function to provide auth configuration guidance
function provideAuthGuidance() {
  console.log('\n🔐 Manual Auth Configuration Required\n');
  
  console.log('The following settings must be configured manually in the Supabase Dashboard:\n');
  
  console.log('1️⃣  OTP Expiry Configuration:');
  console.log('   • Go to: Supabase Dashboard > Authentication > Settings');
  console.log('   • Find: "Email OTP expiry" setting');
  console.log('   • Set to: 3600 seconds (1 hour) or less');
  console.log('   • Recommended: 1800 seconds (30 minutes)\n');
  
  console.log('2️⃣  Leaked Password Protection:');
  console.log('   • Go to: Supabase Dashboard > Authentication > Settings');
  console.log('   • Find: "Leaked password protection" setting');
  console.log('   • Action: Enable this feature');
  console.log('   • Note: This checks passwords against HaveIBeenPwned.org\n');
  
  console.log('🔗 Dashboard URL: https://supabase.com/dashboard/project/[your-project-id]/auth/settings');
}

// Function to verify fixes
function verifyFixes() {
  console.log('\n✅ Verification Steps\n');
  
  console.log('To verify the database fixes were applied correctly:');
  console.log('1. Run: supabase db diff');
  console.log('2. Check that all functions now have SET search_path = \'\'\n');
  
  console.log('To verify auth settings:');
  console.log('1. Check the Supabase Dashboard settings');
  console.log('2. Run the validation function in your database:');
  console.log('   SELECT * FROM public.validate_auth_security_settings();\n');
}

// Function to show security benefits
function showSecurityBenefits() {
  console.log('\n🛡️  Security Benefits\n');
  
  console.log('✅ Function Search Path Protection:');
  console.log('   • Prevents SQL injection through search path manipulation');
  console.log('   • All functions now use explicit schema references\n');
  
  console.log('✅ Reduced OTP Attack Window:');
  console.log('   • Limits time window for OTP-based attacks');
  console.log('   • Follows security best practices\n');
  
  console.log('✅ Password Security Enhancement:');
  console.log('   • Prevents use of known compromised passwords');
  console.log('   • Protects users from credential stuffing attacks\n');
}

// Main execution
async function main() {
  // Check prerequisites
  const hasSupabaseCLI = checkSupabaseCLI();

  // Apply database fixes
  const dbSuccess = applyDatabaseFixes(hasSupabaseCLI);

  if (dbSuccess) {
    if (hasSupabaseCLI) {
      console.log('\n✅ Database security fixes applied successfully!');
    } else {
      console.log('\n📋 Database migration files are ready for manual application.');
    }
  } else {
    console.log('\n❌ Database security fixes failed. Please check the errors above.');
  }

  // Provide auth configuration guidance
  provideAuthGuidance();

  // Show verification steps
  verifyFixes();

  // Show security benefits
  showSecurityBenefits();

  console.log('\n🎉 Security fixes process completed!');
  console.log('📖 For detailed information, see: SECURITY_FIXES.md');
}

// Run the script
main().catch(error => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});
