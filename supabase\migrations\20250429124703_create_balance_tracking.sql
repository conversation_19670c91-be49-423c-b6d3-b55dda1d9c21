-- Create a wallet balance table to track earnings for influencers
CREATE TABLE IF NOT EXISTS public.user_wallets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  available_balance NUMERIC DEFAULT 0.00 NOT NULL,
  pending_balance NUMERIC DEFAULT 0.00 NOT NULL,
  lifetime_earnings NUMERIC DEFAULT 0.00 NOT NULL,
  withdrawn_amount NUMERIC DEFAULT 0.00 NOT NULL,
  currency TEXT DEFAULT 'USD' NOT NULL,
  last_updated TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  
  CONSTRAINT user_wallets_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create index for fast lookups
CREATE INDEX IF NOT EXISTS user_wallets_user_id_idx ON public.user_wallets(user_id);

-- Set up Row Level Security
ALTER TABLE public.user_wallets ENABLE ROW LEVEL SECURITY;

-- Users can view their own wallet balance
CREATE POLICY "Users can view their own wallet"
  ON public.user_wallets
  FOR SELECT
  USING (auth.uid() = user_id);

-- Create a table for withdrawal requests
CREATE TABLE IF NOT EXISTS public.withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  amount NUMERIC NOT NULL,
  currency TEXT DEFAULT 'USD' NOT NULL,
  status TEXT DEFAULT 'pending' NOT NULL,
  payout_method TEXT NOT NULL,
  payout_details JSONB NOT NULL,
  requested_at TIMESTAMPTZ DEFAULT now(),
  processed_at TIMESTAMPTZ,
  processor_reference TEXT,
  notes TEXT,
  
  CONSTRAINT withdrawal_requests_user_id_fkey FOREIGN KEY (user_id) 
    REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT withdrawal_requests_amount_check CHECK (amount > 0)
);

-- Create indexes for withdrawal requests
CREATE INDEX IF NOT EXISTS withdrawal_requests_user_id_idx 
  ON public.withdrawal_requests(user_id);
CREATE INDEX IF NOT EXISTS withdrawal_requests_status_idx 
  ON public.withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS withdrawal_requests_requested_at_idx 
  ON public.withdrawal_requests(requested_at);

-- Set up Row Level Security
ALTER TABLE public.withdrawal_requests ENABLE ROW LEVEL SECURITY;

-- Users can view their own withdrawal requests
CREATE POLICY "Users can view their own withdrawal requests"
  ON public.withdrawal_requests
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can create withdrawal requests
CREATE POLICY "Users can create withdrawal requests"
  ON public.withdrawal_requests
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create function to process a transaction and update wallet balances
CREATE OR REPLACE FUNCTION public.process_transaction_and_update_wallet()
RETURNS TRIGGER AS $$
BEGIN
  -- Ensure the seller wallet exists
  INSERT INTO public.user_wallets (user_id)
  VALUES (NEW.seller_id)
  ON CONFLICT (user_id) DO NOTHING;
  
  -- Update the seller's wallet based on transaction status
  IF NEW.status = 'completed' THEN
    -- Add the seller amount to available balance and lifetime earnings
    UPDATE public.user_wallets
    SET 
      available_balance = available_balance + NEW.seller_amount,
      lifetime_earnings = lifetime_earnings + NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
    
  ELSIF NEW.status = 'pending' THEN
    -- Add the seller amount to pending balance only
    UPDATE public.user_wallets
    SET 
      pending_balance = pending_balance + NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new transactions
CREATE TRIGGER update_wallet_on_transaction
AFTER INSERT ON public.transactions
FOR EACH ROW
EXECUTE FUNCTION public.process_transaction_and_update_wallet();

-- Create trigger for updated transactions
CREATE OR REPLACE FUNCTION public.update_wallet_on_transaction_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- If status changed from pending to completed
  IF OLD.status = 'pending' AND NEW.status = 'completed' THEN
    UPDATE public.user_wallets
    SET 
      available_balance = available_balance + NEW.seller_amount,
      pending_balance = pending_balance - NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
  -- If status changed from pending to failed/canceled
  ELSIF OLD.status = 'pending' AND (NEW.status = 'failed' OR NEW.status = 'canceled') THEN
    UPDATE public.user_wallets
    SET 
      pending_balance = pending_balance - NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for transaction status updates
CREATE TRIGGER update_wallet_on_transaction_update
AFTER UPDATE OF status ON public.transactions
FOR EACH ROW
WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION public.update_wallet_on_transaction_status_change(); 