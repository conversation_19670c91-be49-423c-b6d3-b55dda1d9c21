import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Loader2, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

interface PremiumCouponPaymentProps {
  couponId: string;
  price: number;
  title: string;
}

export default function PremiumCouponPayment({ couponId, price, title }: PremiumCouponPaymentProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();

  const handlePurchase = async () => {
    setIsLoading(true);
    
    // Show coming soon message
    toast({
      title: "Coming Soon!",
      description: "We're working hard to bring you premium coupons. This feature will be available soon!",
    });
    
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return (
    <button 
      className="w-full p-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium shadow-md z-50 rounded-md cursor-pointer flex items-center justify-center"
      onClick={handlePurchase}
      disabled={isLoading}
      type="button"
    >
      {isLoading ? (
        <>
          <Clock className="mr-2 h-4 w-4" />
          Coming Soon
        </>
      ) : (
        <span>Buy Now ${price.toFixed(2)}</span>
      )}
    </button>
  );
} 