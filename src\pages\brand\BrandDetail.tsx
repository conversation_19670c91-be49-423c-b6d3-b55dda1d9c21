import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useParams, Link } from 'react-router-dom';
import { useBrand, useBrandCoupons } from '@/hooks/useBrands';
import { AlertCircle, Globe, ShoppingBag, ExternalLink, Tag, CalendarClock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import CouponCard from '@/components/CouponCard';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import BrandSchema from '../../../seo/schemas/BrandSchema';
import SEO from '../../../seo/components/SEO';
import { openUrlWithCorrectDomain } from '@/utils/domainFix';

const BrandDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { data: brand, isLoading: brandLoading } = useBrand(id || '');
  const { data: coupons, isLoading: couponsLoading } = useBrandCoupons(id || '');
  
  // Loading state
  if (brandLoading) {
    return (
      <MainLayout>
        <PageContainer decorationType="minimal">
          <div className="animate-pulse">
            <div className="h-8 w-48 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 w-64 bg-gray-200 rounded"></div>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }
  
  // Brand not found state
  if (!brand) {
    return (
      <MainLayout>
        <PageContainer decorationType="minimal">
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h1 className="text-2xl font-bold mb-2">Brand Not Found</h1>
            <p className="text-gray-600 mb-6">
              The brand you're looking for doesn't exist or has been removed.
            </p>
            <Button asChild>
              <Link to="/brands">
                Browse All Brands
              </Link>
            </Button>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      {/* SEO */}
      <SEO
        title={`${brand.name} Coupons & Promo Codes`}
        description={`Save with verified ${brand.name} coupon codes and promotional offers. Find the latest deals and discounts.`}
      />
      <BrandSchema
        id={brand.id}
        name={brand.name}
        logoUrl={brand.logo_url || undefined}
        websiteUrl={brand.website || undefined}
        couponsCount={coupons?.length || 0}
      />
      
      <PageContainer decorationType="minimal">
        <div className="mb-8">
          <PageHeaderWithBackButton
            title={brand.name}
            subtitle="View brand details and available coupons"
            icon={ShoppingBag}
          />
          
          {/* Brand Info Card */}
          <Card className="mt-6">
            <CardContent className="p-6">
              <div className="flex items-start gap-6">
                {/* Brand Logo */}
                <div className="h-24 w-24 bg-gray-100 rounded-lg flex items-center justify-center p-2">
                  <img 
                    src={brand.logo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(brand.name)}&background=random&color=fff&size=128&bold=true`}
                    alt={`${brand.name} logo`}
                    className="max-h-full max-w-full object-contain"
                    onError={(e) => {
                      const target = e.currentTarget;
                      target.onerror = null;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(brand.name)}&background=random&color=fff&size=128&bold=true`;
                    }}
                  />
                </div>
                
                {/* Brand Details */}
                <div className="flex-grow">
                  <h1 className="text-2xl font-bold mb-2">{brand.name}</h1>
                  
                  {/* Website Link */}
                  {brand.website && (
                    <a 
                      href={brand.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm text-gray-600 hover:text-brand-blue-500 mb-3"
                      onClick={(e) => {
                        e.preventDefault();
                        openUrlWithCorrectDomain(brand.website || '');
                      }}
                    >
                      <Globe className="h-4 w-4 mr-1" />
                      {brand.website.replace(/^https?:\/\//, '')}
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  )}
                  
                  {/* Stats */}
                  <div className="flex items-center gap-4">
                    <Badge variant="secondary" className="text-xs">
                      <Tag className="h-3 w-3 mr-1" />
                      {coupons?.length || 0} Active Coupons
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      <CalendarClock className="h-3 w-3 mr-1" />
                      Last Updated {new Date().toLocaleDateString()}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Coupons Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold flex items-center gap-2">
              <Tag className="h-5 w-5 text-brand-pink-500" />
              Active Coupons from {brand.name}
            </h2>
          </div>
          
          {couponsLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-64 w-full rounded-lg" />
              ))}
            </div>
          ) : coupons && coupons.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {coupons.map(coupon => (
                <CouponCard
                  key={coupon.id}
                  id={coupon.id}
                  brandName={brand.name}
                  brandLogo={brand.logo_url || "/placeholder.svg"}
                  brandWebsite={brand.website}
                  influencerName={coupon.influencer?.full_name || ""}
                  influencerImage={coupon.influencer?.avatar_url || undefined}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "Uncategorized"}
                  featured={coupon.featured}
                  isPremium={coupon.is_premium}
                  brandId={brand.id}
                />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center shadow-sm">
              <ShoppingBag className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">No active coupons</h3>
              <p className="text-gray-600 dark:text-gray-300">
                There are no active coupons for {brand.name} at the moment.
                Check back later for new deals!
              </p>
            </div>
          )}
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default BrandDetail; 