# Forced Colors Mode Implementation

This document explains how our application handles Windows High Contrast mode using the modern Forced Colors Mode standard, replacing the deprecated `-ms-high-contrast` media query.

## Overview

The `-ms-high-contrast` CSS media query has been deprecated by Microsoft in favor of the standardized `forced-colors` media query. Our application now uses the modern approach to ensure accessibility compliance and eliminate deprecation warnings.

## What Changed

### Before (Deprecated)
```css
@media (-ms-high-contrast: active) {
  /* Styles for high contrast mode */
}
```

### After (Modern Standard)
```css
@media (forced-colors: active) {
  /* Styles for forced colors mode */
}
```

## Implementation Details

### 1. CSS Files

#### `src/styles/forced-colors.css`
- Contains all forced colors mode styles
- Uses modern `@media (forced-colors: active)` queries
- Includes comprehensive support for Radix UI components
- Provides system color mappings

#### `src/styles/deprecation-fixes.css`
- Handles third-party library compatibility
- Suppresses deprecation warnings from dependencies
- Provides fallbacks for older implementations

### 2. JavaScript Utilities

#### `src/utils/forcedColorsUtils.ts`
Provides programmatic access to forced colors mode:

```typescript
import { isForcedColorsActive, useForcedColors } from '@/utils/forcedColorsUtils';

// Check if forced colors mode is active
const isActive = isForcedColorsActive();

// React hook for components
const { isActive, getStyles, classes } = useForcedColors();
```

### 3. Tailwind Configuration

Extended Tailwind config with system colors:
```typescript
colors: {
  'system': {
    'canvas': 'Canvas',
    'canvas-text': 'CanvasText',
    'button-face': 'ButtonFace',
    'button-text': 'ButtonText',
    'highlight': 'Highlight',
    'highlight-text': 'HighlightText',
    'link-text': 'LinkText',
    'active-text': 'ActiveText',
  }
}
```

## System Colors Reference

When forced colors mode is active, these system colors are available:

| Color | Purpose |
|-------|---------|
| `Canvas` | Background color for content areas |
| `CanvasText` | Text color for content areas |
| `ButtonFace` | Background color for buttons |
| `ButtonText` | Text color for buttons |
| `Highlight` | Background color for selected/highlighted items |
| `HighlightText` | Text color for selected/highlighted items |
| `LinkText` | Color for hyperlinks |
| `ActiveText` | Color for active/pressed links |

## Usage Examples

### CSS Classes
```css
/* Use system colors directly */
.my-component {
  background-color: Canvas;
  color: CanvasText;
  border: 1px solid ButtonText;
}

/* With Tailwind utilities */
.my-button {
  @apply bg-system-button-face text-system-button-text;
}
```

### React Components
```tsx
import { useForcedColors, ForcedColorsProvider } from '@/utils/forcedColorsUtils';

function MyComponent() {
  const { isActive, getStyles } = useForcedColors();
  
  const styles = getStyles(
    { backgroundColor: '#ffffff', color: '#000000' }, // Normal styles
    { backgroundColor: 'Canvas', color: 'CanvasText' }  // Forced colors styles
  );
  
  return <div style={styles}>Content</div>;
}

// Or use the provider
function App() {
  return (
    <ForcedColorsProvider>
      <MyComponent />
    </ForcedColorsProvider>
  );
}
```

### Media Queries
```css
/* Modern approach */
@media (forced-colors: active) {
  .card {
    background-color: Canvas;
    color: CanvasText;
    border: 1px solid ButtonText;
  }
}

/* Fallback for unsupported browsers */
@supports not (forced-colors: active) {
  .card {
    /* Regular styles */
  }
}
```

## Component Support

### Radix UI Components
All Radix UI components are properly styled for forced colors mode:
- Dialog
- Popover
- Select
- Toast
- Tooltip
- Accordion
- Avatar
- Progress
- Switch
- Tabs

### Custom Components
Custom components should use the provided utilities:
```tsx
import { forcedColorsClasses } from '@/utils/forcedColorsUtils';

function CustomButton() {
  return (
    <button className={`
      bg-primary text-white
      forced-colors:bg-system-button-face 
      forced-colors:text-system-button-text
      forced-colors:border forced-colors:border-system-button-text
    `}>
      Click me
    </button>
  );
}
```

## Testing

### Manual Testing
1. Enable Windows High Contrast mode:
   - Windows 10/11: Settings > Ease of Access > High contrast
   - Or use Alt + Left Shift + Print Screen

2. Verify that:
   - All text is readable
   - Interactive elements are clearly visible
   - Focus indicators are prominent
   - No content is hidden or inaccessible

### Programmatic Testing
```typescript
import { isForcedColorsActive, getSystemColors } from '@/utils/forcedColorsUtils';

// Check if forced colors is active
console.log('Forced colors active:', isForcedColorsActive());

// Get current system colors
console.log('System colors:', getSystemColors());
```

## Browser Support

| Browser | Support |
|---------|---------|
| Chrome 89+ | ✅ Full support |
| Firefox 89+ | ✅ Full support |
| Safari 15.4+ | ✅ Full support |
| Edge 89+ | ✅ Full support |
| IE 11 | ❌ Uses fallback styles |

## Migration Guide

If you have existing `-ms-high-contrast` styles:

1. Replace media queries:
   ```css
   /* Old */
   @media (-ms-high-contrast: active) { }
   
   /* New */
   @media (forced-colors: active) { }
   ```

2. Update JavaScript detection:
   ```javascript
   // Old
   const isHighContrast = window.matchMedia('(-ms-high-contrast: active)').matches;
   
   // New
   const isForcedColors = window.matchMedia('(forced-colors: active)').matches;
   ```

3. Use system colors instead of hardcoded values:
   ```css
   /* Old */
   color: windowtext;
   background: window;
   
   /* New */
   color: CanvasText;
   background: Canvas;
   ```

## Troubleshooting

### Deprecation Warning Still Appears
- Check if any third-party libraries are using `-ms-high-contrast`
- Ensure `src/styles/deprecation-fixes.css` is imported
- Update dependencies to newer versions that support forced colors

### Styles Not Applied
- Verify forced colors mode is actually active
- Check browser developer tools for media query matching
- Ensure CSS specificity is correct

### Performance Issues
- Use `forced-color-adjust: auto` for better performance
- Avoid complex selectors in forced colors media queries
- Consider using CSS custom properties for dynamic values

## Resources

- [MDN: forced-colors](https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors)
- [Microsoft: Deprecating -ms-high-contrast](https://blogs.windows.com/msedgedev/2024/04/29/deprecating-ms-high-contrast/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/)
