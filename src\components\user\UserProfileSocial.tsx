import { <PERSON><PERSON> } from '@/components/ui/button';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { FaTwitter, FaInstagram, FaYoutube, FaTiktok, FaGlobe, FaFacebookF, FaTwitch } from 'react-icons/fa';
import { cn } from '@/lib/utils';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ExternalLink } from 'lucide-react';
import { openUrlWithCorrectDomain } from '@/utils/domainFix';

interface SocialLink {
  platform: string;
  url: string;
}

interface UserProfileSocialProps {
  socialLinks?: SocialLink[];
  website?: string | null;
  variant?: 'icons' | 'buttons';
  className?: string;
  isCurrentUser: boolean;
  navigate: (path: string) => void;
}

const platformIcons: Record<string, JSX.Element> = {
  twitter: <FaTwitter className="h-3.5 w-3.5 text-blue-400" />,
  instagram: <FaInstagram className="h-3.5 w-3.5 text-pink-500" />,
  youtube: <FaYoutube className="h-3.5 w-3.5 text-red-500" />,
  tiktok: <FaTiktok className="h-3.5 w-3.5" />,
  facebook: <FaFacebookF className="h-3.5 w-3.5 text-blue-600" />,
  twitch: <FaTwitch className="h-3.5 w-3.5 text-purple-500" />,
};

const platformColors: Record<string, { border: string; hover: string }> = {
  twitter: { border: 'border-blue-200', hover: 'hover:bg-blue-50' },
  instagram: { border: 'border-pink-200', hover: 'hover:bg-pink-50' },
  youtube: { border: 'border-red-200', hover: 'hover:bg-red-50' },
  tiktok: { border: 'border-gray-200', hover: 'hover:bg-gray-50' },
  facebook: { border: 'border-blue-200', hover: 'hover:bg-blue-50' },
  twitch: { border: 'border-purple-200', hover: 'hover:bg-purple-50' },
};

const getSocialIcon = (platform: string) => {
  const size = "w-4 h-4";
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      return <FaTwitter className={size} />;
    case 'instagram':
      return <FaInstagram className={size} />;
    case 'youtube':
      return <FaYoutube className={size} />;
    case 'tiktok':
      return <FaTiktok className={size} />;
    case 'facebook':
      return <FaFacebookF className={size} />;
    case 'twitch':
      return <FaTwitch className={size} />;
    default:
      return <FaGlobe className={size} />;
  }
};

// Helper function to extract username from social media URLs
const extractUsername = (url: string, platform: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.replace(/^\/+|\/+$/g, '');
    
    // Just return the pathname for most platforms
    return pathname || url;
  } catch (e) {
    // If URL parsing fails, just return the original URL
    return url;
  }
};

const UserProfileSocial = ({ socialLinks = [], website, variant = 'icons', className, isCurrentUser, navigate }: UserProfileSocialProps) => {
  if (!socialLinks?.length && !website) return null;

  return (
    <Card className="w-full bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base font-medium">Websites & Social</CardTitle>
          {isCurrentUser && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 text-xs"
              onClick={() => navigate('/settings/social')}
            >
              Edit Links
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="grid gap-4">
        {/* Display the website URL separately if available */}
        {website && (
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 flex items-center justify-center rounded-full bg-slate-100">
              {getSocialIcon(website)}
            </div>
            <div className="flex-grow">
              <h3 className="text-sm font-medium">Website</h3>
              <p className="text-xs text-slate-500 truncate">{website}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => openUrlWithCorrectDomain(website)}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Display social links */}
        {socialLinks && socialLinks.length > 0 && (
          <>
            {socialLinks.map((link, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="h-8 w-8 flex items-center justify-center rounded-full bg-slate-100">
                  {getSocialIcon(link.platform)}
                </div>
                <div className="flex-grow">
                  <h3 className="text-sm font-medium">{link.platform.charAt(0).toUpperCase() + link.platform.slice(1)}</h3>
                  <p className="text-xs text-slate-500 truncate">{extractUsername(link.url, link.platform)}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => openUrlWithCorrectDomain(link.url)}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </>
        )}
        
        {!website && (!socialLinks || socialLinks.length === 0) && (
          <div className="text-center py-6">
            <p className="text-sm text-slate-500">No social links added yet</p>
            {isCurrentUser && (
              <Button 
                size="sm" 
                variant="outline" 
                className="mt-2"
                onClick={() => navigate('/settings/social')}
              >
                Add Social Links
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserProfileSocial; 