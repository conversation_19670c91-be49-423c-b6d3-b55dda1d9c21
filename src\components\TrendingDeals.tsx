import React, { useEffect } from 'react';
import { useState } from "react";
import CouponCard from "./CouponCard";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, Filter } from "lucide-react";
import { useTrendingCoupons } from "@/hooks/useCoupons";
import CouponsSkeleton from "@/components/CouponsSkeleton";
import { simpleSearchCoupons } from "@/utils/searchUtils";

interface TrendingDealsProps {
  initialCategory?: string;
  searchFilter?: string;
  showHeader?: boolean;
}

const TrendingDeals = ({ 
  initialCategory = "All", 
  searchFilter = "", 
  showHeader = false 
}: TrendingDealsProps) => {
  const [activeCategory, setActiveCategory] = useState<string>(initialCategory);
  const { data: trendingDeals, isLoading, error } = useTrendingCoupons();
  
  // Update active category when initialCategory prop changes
  useEffect(() => {
    if (initialCategory) {
      setActiveCategory(initialCategory);
    }
  }, [initialCategory]);
  
  const filteredDeals = trendingDeals ? (() => {
    // First apply search filter using enhanced search
    let searchFiltered = searchFilter
      ? simpleSearchCoupons(trendingDeals, searchFilter)
      : trendingDeals;

    // Then apply category filter
    return searchFiltered.filter(deal => {
      const categoryMatch =
        activeCategory === "All" ||
        !deal.category?.name ||
        deal.category.name.toLowerCase() === activeCategory.toLowerCase();

      return categoryMatch;
    });
  })() : [];
  
  return (
    <section className="py-6">
      <div className="max-w-6xl mx-auto px-4">
        {/* Show filtered category name */}
        {activeCategory !== "All" && (
          <div className="mb-4 px-2">
            <h3 className="text-lg font-semibold text-gray-700">
              Category: <span className="text-brand-malachite">{activeCategory}</span>
            </h3>
          </div>
        )}
        
        {/* Deals Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {isLoading ? (
            // Loading skeletons
            <CouponsSkeleton count={8} />
          ) : error ? (
            <div className="col-span-4 bg-red-500/10 text-red-500 p-6 rounded-lg text-center">
              <p>Error loading deals. Please try again later.</p>
            </div>
          ) : filteredDeals && filteredDeals.length > 0 ? (
            filteredDeals.map(deal => (
              <div key={deal.id} className="animate__animated animate__fadeIn">
                <CouponCard 
                  id={deal.id}
                  brandName={deal.brand?.name || "Unknown Brand"}
                  brandLogo={deal.brand?.logo_url || "/placeholder.svg"}
                  influencerName={deal.influencer?.full_name || "Anonymous"}
                  influencerImage={deal.influencer?.avatar_url || undefined}
                  discountAmount={deal.discount_description || `${deal.discount_percent}% OFF`}
                  expirationTime={deal.expires_at ? new Date(deal.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={deal.code}
                  category={deal.category?.name || "Other"}
                  featured={deal.featured}
                  brandId={deal.brand_id}
                />
              </div>
            ))
          ) : (
            <div className="col-span-4 bg-gray-100 p-10 rounded-lg text-center">
              <p className="text-gray-500">
                {searchFilter 
                  ? "No deals matching your search." 
                  : activeCategory !== "All" 
                    ? `No deals found in the "${activeCategory}" category.` 
                    : "No deals available at the moment."}
              </p>
              {activeCategory !== "All" && (
                <Button 
                  variant="outline" 
                  className="mt-4 border-blue-500 text-blue-500"
                  onClick={() => setActiveCategory("All")}
                >
                  View All Deals
                </Button>
              )}
            </div>
          )}
        </div>
        
        {/* Load More Button */}
        {filteredDeals && filteredDeals.length > 8 && (
          <div className="mt-10 text-center">
            <Button 
              variant="outline" 
              className="border-blue-500 text-blue-500 hover:bg-blue-500/10 px-6"
            >
              Load More Deals
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default TrendingDeals;
