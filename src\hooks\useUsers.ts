import { useQuery } from '@tanstack/react-query';
import { supabase, retryOperation } from '@/integrations/supabase/client';

export interface User {
  id: string;
  full_name: string;
  username: string;
  avatar_url: string | null;
  bio: string | null;
  website: string | null;
  role: 'influencer' | 'user' | 'admin';
  coupons_count: number;
  followers_count: number;
  social_links: {
    platform: string;
    url: string;
  }[];
}

export const useUsers = () => {
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      // First get users
      const { data: users, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'influencer');
      
      if (error) {
        throw error;
      }
      
      // Get additional data for each user
      const usersWithDetails = await Promise.all(
        users.map(async (user) => {
          // Get social links
          const { data: socialLinks } = await supabase
            .from('social_links')
            .select('platform, url')
            .eq('profile_id', user.id);
          
          // Get coupon count
          const { count: couponsCount } = await supabase
            .from('coupons')
            .select('*', { count: 'exact', head: true })
            .eq('influencer_id', user.id)
            .eq('status', 'active');
          
          // Get followers count
          const { count: followersCount } = await supabase
            .from('follows')
            .select('*', { count: 'exact', head: true })
            .eq('influencer_id', user.id);
          
          return {
            ...user,
            social_links: socialLinks || [],
            coupons_count: couponsCount || 0,
            followers_count: followersCount || 0,
          };
        })
      );
      
      return usersWithDetails as User[];
    },
  });
};

export const useUser = (username: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['user', username],
    queryFn: async () => {
      // Skip the request if no username is provided
      if (!username) {
        console.log('No username provided to useUser hook');
        // Return a default user instead of throwing an error
        return createDefaultUser(username);
      }

      try {
        console.log(`Fetching user data for username: ${username}`);
        
        // Use retry operation utility with proper typing and much longer timeout
        const result = await retryOperation(async () => {
          console.log(`Attempt to fetch profile for ${username}`);
          try {
            // Use maybeSingle instead of single to prevent PGRST116 error
            const response = await supabase
              .from('profiles')
              .select('*')
              .eq('username', username)
              .maybeSingle();
            return response;
          } catch (e) {
            // Improve error logging
            const errorMessage = e instanceof Error ? e.message : JSON.stringify(e);
            console.error(`Error fetching profile: ${errorMessage}`);
            throw e;
          }
        }, 4, 1000); // 4 retries with 1s base delay
        
        const { data: user, error } = result;
        
        if (error) {
          // Better error logging with details
          const errorDetails = JSON.stringify(error, null, 2);
          console.error(`User fetch error: ${error.message || 'Unknown error'}`);
          console.error(`Error details: ${errorDetails}`);
          
          // Return default user instead of throwing an error
          return createDefaultUser(username);
        }
        
        if (!user) {
          console.warn(`User not found: ${username}`);
          // Return default user instead of throwing an error
          return createDefaultUser(username);
        }
        
        console.log(`Successfully fetched user: ${username}`);
        
        // Create a basic user object with the data we have
        const baseUser = {
          ...user,
          social_links: [],
          coupons_count: 0,
          followers_count: 0,
        } as User;
        
        try {
          // Try to get additional data, but don't fail if it doesn't work
          const [socialLinksResult, couponsResult, followersResult] = await Promise.allSettled([
            // Get social links
            supabase
              .from('social_links')
              .select('platform, url')
              .eq('profile_id', user.id),
            
            // Get coupon count
            supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('influencer_id', user.id)
              .eq('status', 'active'),
            
            // Get followers count
            supabase
              .from('follows')
              .select('*', { count: 'exact', head: true })
              .eq('influencer_id', user.id)
          ]);
          
          // Process results - only use successful ones
          const enhancedUser = {
            ...baseUser,
            social_links: socialLinksResult.status === 'fulfilled' ? socialLinksResult.value.data || [] : [],
            coupons_count: couponsResult.status === 'fulfilled' ? couponsResult.value.count || 0 : 0,
            followers_count: followersResult.status === 'fulfilled' ? followersResult.value.count || 0 : 0,
          };
          
          return enhancedUser;
        } catch (detailsError) {
          console.error('Error fetching user details:', detailsError);
          // Return basic user if we can't get details
          return baseUser;
        }
      } catch (error) {
        console.error('Error in useUser hook:', error);
        throw error;
      }
    },
    retry: 3, // More retries
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff with longer max
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: options?.enabled !== undefined ? options.enabled : !!username, // Use provided enabled option or default
  });
};

export const usePopularUsers = (limit = 5) => {
  return useQuery({
    queryKey: ['popular-users', limit],
    queryFn: async () => {
      // Get users with most followers
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          full_name,
          avatar_url,
          role,
          bio
        `)
        .eq('role', 'influencer')
        .limit(limit);
      
      if (error) {
        throw error;
      }
      
      // Get additional data for each user
      const usersWithDetails = await Promise.all(
        data.map(async (user) => {
          // Get coupon count
          const { count: couponsCount } = await supabase
            .from('coupons')
            .select('*', { count: 'exact', head: true })
            .eq('influencer_id', user.id)
            .eq('status', 'active');
          
          return {
            ...user,
            coupons_count: couponsCount || 0,
          };
        })
      );
      
      return usersWithDetails;
    },
  });
};

// Add this helper function at the end of the file
// Helper function to create a default user when no user is found
const createDefaultUser = (username: string): User => {
  return {
    id: `default-${Date.now()}`,
    full_name: username || 'Guest User',
    username: username || 'guest',
    avatar_url: null,
    bio: null,
    website: null,
    role: 'user',
    coupons_count: 0,
    followers_count: 0,
    social_links: []
  };
}; 