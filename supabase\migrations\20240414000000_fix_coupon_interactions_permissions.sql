-- Enable Row Level Security on coupon_interactions table
ALTER TABLE IF EXISTS public.coupon_interactions ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into coupon_interactions (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to coupon_interactions"
ON public.coupon_interactions
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to coupon_interactions"
ON public.coupon_interactions
USING (true)
WITH CHECK (true);

-- Allow users to see their own interactions and interactions with their coupons
CREATE POLICY "Allow users to see relevant coupon interactions"
ON public.coupon_interactions
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  EXISTS (
    SELECT 1 FROM coupons
    WHERE coupons.id = coupon_interactions.coupon_id
    AND coupons.influencer_id = auth.uid()
  )
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_user_id ON public.coupon_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_coupon_id ON public.coupon_interactions(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_type ON public.coupon_interactions(interaction_type); 