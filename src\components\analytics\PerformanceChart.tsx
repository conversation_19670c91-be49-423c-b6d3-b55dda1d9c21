import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useState } from "react";

// Define the chart data interface
export interface ChartDataPoint {
  label: string;
  views?: number;
  clicks?: number;
  uses?: number;
}

interface PerformanceChartProps {
  title: string;
  description?: string;
  data: ChartDataPoint[];
  height?: number;
}

const PerformanceChart = ({ 
  title, 
  description, 
  data,
  height = 200
}: PerformanceChartProps) => {
  const [hoverIndex, setHoverIndex] = useState<number | null>(null);
  
  // Find the maximum value to normalize the bars
  const maxValue = Math.max(
    ...data.map(d => Math.max(
      d.views || 0, 
      d.clicks || 0, 
      d.uses || 0
    ))
  );
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="relative" style={{ height: `${height}px` }}>
          {/* Y axis labels */}
          <div className="absolute left-0 top-0 bottom-0 w-10 flex flex-col justify-between text-xs text-gray-500">
            <span>{maxValue}</span>
            <span>{Math.round(maxValue * 0.75)}</span>
            <span>{Math.round(maxValue * 0.5)}</span>
            <span>{Math.round(maxValue * 0.25)}</span>
            <span>0</span>
          </div>
          
          {/* Grid lines */}
          <div className="absolute left-10 right-0 top-0 bottom-0">
            {[0, 0.25, 0.5, 0.75, 1].map((level) => (
              <div 
                key={level}
                className="absolute w-full border-t border-gray-100 dark:border-gray-800"
                style={{ top: `${(1 - level) * 100}%` }}
              />
            ))}
          </div>
          
          {/* Bars */}
          <div className="absolute left-12 right-0 top-0 bottom-8 flex items-end">
            {data.map((point, index) => {
              const isHovered = index === hoverIndex;
              const normalizedClicks = ((point.clicks || 0) / maxValue) * 100;
              const normalizedViews = ((point.views || 0) / maxValue) * 100;
              const normalizedUses = ((point.uses || 0) / maxValue) * 100;
              
              return (
                <div 
                  key={index}
                  className="group flex-1 flex items-end justify-center space-x-1"
                  onMouseEnter={() => setHoverIndex(index)}
                  onMouseLeave={() => setHoverIndex(null)}
                >
                  {/* Views bar */}
                  {point.views !== undefined && (
                    <div 
                      className={`w-3 bg-blue-400 rounded-t transition-all duration-200 ${isHovered ? 'opacity-100' : 'opacity-80'}`}
                      style={{ height: `${normalizedViews}%` }}
                    />
                  )}
                  
                  {/* Clicks bar */}
                  {point.clicks !== undefined && (
                    <div 
                      className={`w-3 bg-purple-400 rounded-t transition-all duration-200 ${isHovered ? 'opacity-100' : 'opacity-80'}`}
                      style={{ height: `${normalizedClicks}%` }}
                    />
                  )}
                  
                  {/* Uses bar */}
                  {point.uses !== undefined && (
                    <div 
                      className={`w-3 bg-green-400 rounded-t transition-all duration-200 ${isHovered ? 'opacity-100' : 'opacity-80'}`}
                      style={{ height: `${normalizedUses}%` }}
                    />
                  )}
                  
                  {/* Hover tooltip */}
                  {isHovered && (
                    <div className="absolute top-0 -mt-16 bg-white dark:bg-gray-800 shadow-lg rounded px-2 py-1 text-xs">
                      {point.views !== undefined && (
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-blue-400 rounded-full mr-1" />
                          <span>Views: {point.views}</span>
                        </div>
                      )}
                      {point.clicks !== undefined && (
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-purple-400 rounded-full mr-1" />
                          <span>Clicks: {point.clicks}</span>
                        </div>
                      )}
                      {point.uses !== undefined && (
                        <div className="flex items-center">
                          <span className="w-2 h-2 bg-green-400 rounded-full mr-1" />
                          <span>Uses: {point.uses}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {/* X axis labels */}
          <div className="absolute left-12 right-0 bottom-0 h-8 flex">
            {data.map((point, index) => (
              <div key={index} className="flex-1 text-center text-xs text-gray-500 truncate">
                {point.label}
              </div>
            ))}
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center space-x-4 mt-4">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-blue-400 rounded-full mr-1" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Views</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-purple-400 rounded-full mr-1" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Clicks</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-green-400 rounded-full mr-1" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Uses</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformanceChart; 