import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SearchResultSchemaProps {
  mainEntity: {
    name: string;
    description: string;
    url: string;
  };
  sections: Array<{
    name: string;
    description: string;
    url: string;
    image?: string;
  }>;
}

/**
 * Component that adds Schema.org structured data for rich search results
 * This helps search engines display multiple sections in search results
 * similar to how Perplexity shows different sections
 */
const SearchResultSchema: React.FC<SearchResultSchemaProps> = ({
  mainEntity,
  sections
}) => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": mainEntity.name,
    "description": mainEntity.description,
    "url": mainEntity.url,
    "mainEntity": {
      "@type": "Article",
      "name": mainEntity.name,
      "description": mainEntity.description,
      "url": mainEntity.url,
      "hasPart": sections.map(section => ({
        "@type": "WebPageElement",
        "name": section.name,
        "description": section.description,
        "url": section.url,
        ...(section.image && { "image": section.image })
      }))
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema)}
      </script>
    </Helmet>
  );
};

export default SearchResultSchema; 