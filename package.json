{"name": "couponlink", "version": "0.1.1", "private": true, "scripts": {"dev": "node vite-start.js", "build": "set ROLLUP_SKIP_NODEJS_NATIVE=true && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.74.11", "@types/react-helmet": "^6.1.11", "class-variance-authority": "^0.7.1", "date-fns": "^2.30.0", "framer-motion": "^12.9.2", "lucide-react": "^0.503.0", "next": "^13.5.4", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^7.5.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.17.32", "@types/react": "^18.3.20", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.50.0", "eslint-config-next": "^13.5.4", "jimp": "^1.6.0", "postcss": "^8.5.3", "rollup": "^3.29.4", "sharp": "^0.34.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^3.2.7"}}