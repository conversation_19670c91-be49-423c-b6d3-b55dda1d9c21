import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Color map for categories
const categoryColors: Record<string, string> = {
  'Electronics': 'bg-blue-500',
  'Fashion': 'bg-pink-500',
  'Food': 'bg-yellow-500',
  'Travel': 'bg-green-500',
  'Beauty': 'bg-purple-500',
  'Home': 'bg-emerald-500',
  'Sports': 'bg-red-500',
  'Health': 'bg-cyan-500',
  // Default for any other category
  'default': 'bg-gray-500'
};

export const useTopCategories = (limit = 4) => {
  return useQuery({
    queryKey: ['topCategories', limit],
    queryFn: async () => {
      try {
        // Get categories with their coupon counts
        const { data, error } = await supabase
          .from('categories')
          .select(`
            id,
            name,
            coupons:coupons(count)
          `)
          .order('coupons', { ascending: false, foreignTable: 'coupons' })
          .limit(limit);
        
        if (error) {
          console.error('Error fetching top categories:', error);
          return [];
        }
        
        // Format the data to include couponCount
        return data.map(category => ({
          id: category.id,
          name: category.name,
          couponCount: category.coupons?.length || 0
        }));
      } catch (error) {
        console.error('Error in useTopCategories hook:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 15 // 15 minutes
  });
}; 