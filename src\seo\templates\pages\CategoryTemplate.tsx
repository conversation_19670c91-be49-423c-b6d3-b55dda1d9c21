import React from 'react';
import SEO from '@/seo/components/SEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import BreadcrumbSchema from '@/seo/schemas/BreadcrumbSchema';
import SearchResultSchema from '@/seo/schemas/SearchResultSchema';
import PageContainer from '@/components/layout/PageContainer';

interface CategoryTemplateProps {
  categoryName: string;
  description: string;
  imageUrl?: string;
  coupons: Array<{
    id: string;
    name: string;
    description: string;
    brandName: string;
    discount: string;
  }>;
  subcategories?: Array<{
    name: string;
    description: string;
    url: string;
    imageUrl?: string;
  }>;
  relatedCategories?: Array<{
    name: string;
    url: string;
  }>;
}

/**
 * Category page template with proper SEO implementation
 * This shows how to use SEO components for best search visibility
 * and rich search results like the Perplexity example
 */
const CategoryTemplate: React.FC<CategoryTemplateProps> = ({
  categoryName,
  description,
  imageUrl,
  coupons,
  subcategories = [],
  relatedCategories = []
}) => {
  // Format title and descriptions for SEO
  const seoTitle = `${categoryName} Coupons & Promo Codes (${new Date().toLocaleString('default', { month: 'long' })} ${new Date().getFullYear()})`;
  const seoDescription = `Find the best ${categoryName.toLowerCase()} deals and discounts. Save with verified ${categoryName.toLowerCase()} coupon codes and promotional offers.`;
  const seoKeywords = `${categoryName} coupons, ${categoryName} promo codes, ${categoryName} discount codes, ${categoryName} deals, ${categoryName} offers`;
  
  // Breadcrumb items for structured data
  const breadcrumbItems = [
    { name: 'Home', url: 'https://www.couponlink.in/' },
    { name: 'Categories', url: 'https://www.couponlink.in/categories' },
    { name: categoryName, url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}` }
  ];

  // Search sections for rich results
  const searchSections = [
    {
      name: 'Featured Deals',
      description: `Best ${categoryName} coupon codes and promotional offers`,
      url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}#featured`,
      image: imageUrl
    },
    ...(subcategories.length > 0 ? [{
      name: 'Subcategories',
      description: `Browse ${categoryName} deals by subcategory`,
      url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}#subcategories`
    }] : []),
    {
      name: 'Popular Brands',
      description: `Top brands offering ${categoryName} coupons`,
      url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}#brands`
    },
    {
      name: 'Related Categories',
      description: 'Similar categories you might be interested in',
      url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}#related`
    }
  ];

  return (
    <>
      {/* Add enhanced SEO components */}
      <SEO 
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        image={imageUrl}
        type="article"
        modifiedTime={new Date().toISOString()}
        breadcrumbs={breadcrumbItems}
      />
      
      {/* Add website schema */}
      <WebsiteSchema />
      
      {/* Add breadcrumb schema */}
      <BreadcrumbSchema items={breadcrumbItems} />
      
      {/* Add search result schema for multiple sections */}
      <SearchResultSchema
        mainEntity={{
          name: `${categoryName} Coupons & Promo Codes`,
          description: seoDescription,
          url: `https://www.couponlink.in/categories/${categoryName.toLowerCase().replace(/\s+/g, '-')}`
        }}
        sections={searchSections}
      />
      
      <PageContainer>
        <div>
          {/* Template content goes here */}
          {/* You can add your category page layout components here */}
        </div>
      </PageContainer>
    </>
  );
};

export default CategoryTemplate; 