/* Forced Colors Mode Utilities */
@media (forced-colors: active) {
  .forced-colors-mode {
    forced-color-adjust: none;
  }
  
  /* System Colors */
  .system-color-text {
    color: CanvasText;
  }
  
  .system-color-bg {
    background-color: Canvas;
  }
  
  .system-color-border {
    border-color: CanvasText;
  }
  
  /* High Contrast Button States */
  .button-high-contrast {
    border: 1px solid transparent;
  }
  
  .button-high-contrast:hover,
  .button-high-contrast:focus {
    border-color: ButtonText;
  }
  
  /* Links in High Contrast */
  .link-high-contrast {
    color: LinkText;
  }
  
  .link-high-contrast:hover,
  .link-high-contrast:focus {
    color: ActiveText;
  }
}

/* Fallback for browsers that don't support forced-colors */
@supports not (forced-colors: active) {
  .forced-colors-mode {
    /* Default styles when forced colors are not active */
  }
} 