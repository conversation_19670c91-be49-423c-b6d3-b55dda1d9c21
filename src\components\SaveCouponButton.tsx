import { useState, useEffect } from 'react';
import { Bookmark, Plus, Save, X, Pencil, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/context/AuthContext';
import { useCollections, useSaveCoupon, useCreateCollection, useIsCouponSaved, useRemoveSavedCoupon } from '@/hooks/useSavedCoupons';
import { toast } from 'sonner';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SaveCouponButtonProps {
  couponId: string;
  className?: string;
  variant?: "default" | "outline" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  showText?: boolean;
}

const SaveCouponButton = ({ couponId, className = '', variant = 'ghost', size = 'sm', showText = true }: SaveCouponButtonProps) => {
  const { user } = useAuth();
  const [dbReady, setDbReady] = useState(true);
  const [isChecking, setIsChecking] = useState(true);
  const navigate = useNavigate();
  
  // Check database on component mount
  useEffect(() => {
    async function checkDatabase() {
      if (!user) {
        setIsChecking(false);
        return;
      }
      
      try {
        // Check if collections table exists
        const { error: collectionsError } = await supabase
          .from('collections')
          .select('id')
          .limit(1);
          
        if (collectionsError) {
          console.error("Collections table error:", collectionsError);
          setDbReady(false);
          setIsChecking(false);
          return;
        }
        
        // Check if saved_coupons has required columns
        const { error: savedCouponsError } = await supabase
          .from('saved_coupons')
          .select('collection_id, notes')
          .limit(1);
          
        if (savedCouponsError) {
          console.error("Saved coupons table error:", savedCouponsError);
          setDbReady(false);
          setIsChecking(false);
          return;
        }
        
        setDbReady(true);
        setIsChecking(false);
      } catch (err) {
        console.error("Database check error:", err);
        setDbReady(false);
        setIsChecking(false);
      }
    }
    
    checkDatabase();
  }, [user]);
  
  // Only enable queries if DB is ready
  const { data: collections, isLoading: collectionsLoading } = useCollections(user?.id, { enabled: dbReady && !isChecking });
  const { data: isSaved, isLoading: isSavedLoading } = useIsCouponSaved(user?.id, couponId, { enabled: dbReady && !isChecking });
  const saveCouponMutation = useSaveCoupon();
  const createCollectionMutation = useCreateCollection();
  const removeCouponMutation = useRemoveSavedCoupon();
  
  const [isOpen, setIsOpen] = useState(false);
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string | undefined>(
    'no-collection'
  );
  const [notes, setNotes] = useState('');
  const [isPending, setIsPending] = useState(false);
  
  const handleShowDatabaseError = () => {
    toast.error("Database setup required. Please set up the collections feature first.");
    
    // Show a more detailed toast with the SQL
    toast("Database Setup Required", {
      description: "Please run the required SQL for collections feature. Check your console for details.",
      action: {
        label: "Dismiss",
        onClick: () => console.log("Dismissed")
      },
      duration: 10000
    });
    
    // Log the SQL to console for easy copying
    console.info("Run this SQL in your Supabase dashboard:");
    console.info(`
-- Create collections table
CREATE TABLE public.collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add columns to saved_coupons
ALTER TABLE public.saved_coupons 
ADD COLUMN IF NOT EXISTS collection_id UUID REFERENCES public.collections(id),
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS saved_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Add RLS policies
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;

-- Users can only access their own collections
CREATE POLICY "Users can view their own collections" 
ON public.collections FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own collections" 
ON public.collections FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own collections" 
ON public.collections FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own collections" 
ON public.collections FOR DELETE 
USING (auth.uid() = user_id);
    `);
  };
  
  const handleCreateCollection = async () => {
    if (!user) return;
    if (!dbReady) {
      handleShowDatabaseError();
      return;
    }
    
    if (!newCollectionName.trim()) {
      toast.error('Please enter a collection name');
      return;
    }
    
    try {
      const newCollection = await createCollectionMutation.mutateAsync({ 
        userId: user.id, 
        name: newCollectionName.trim() 
      });
      
      setSelectedCollection(newCollection.id);
      setIsCreatingCollection(false);
      setNewCollectionName('');
    } catch (error) {
      console.error('Error creating collection:', error);
      toast.error('Failed to create collection');
    }
  };
  
  const handleSave = async () => {
    if (!user) {
      toast.error('Please log in to save coupons');
      setIsOpen(false);
      return;
    }
    
    if (!dbReady) {
      handleShowDatabaseError();
      setIsOpen(false);
      return;
    }
    
    try {
      if (!user) {
        toast.error('Please log in to save coupons');
        setIsOpen(false);
        return;
      }

      if (!dbReady) {
        handleShowDatabaseError();
        setIsOpen(false);
        return;
      }

      setIsPending(true);

      let collectionId = selectedCollection;

      // If no collection is selected, create a default collection
      if (selectedCollection === 'no-collection' || !selectedCollection) {
        const defaultCollection = collections?.find(
          (collection) => collection.is_default
        );

        if (defaultCollection) {
          collectionId = defaultCollection.id;
        } else {
          // Create a default collection if none exists
          const newCollection = await createCollectionMutation.mutateAsync({
            userId: user.id,
            name: "Saved Coupons",
            description: "Default collection for saved coupons",
            isDefault: true
          });
          collectionId = newCollection.id;
        }
      }
      
      await saveCouponMutation.mutateAsync({
        userId: user.id,
        couponId,
        collectionId: collectionId,
        notes
      });
      
      setIsOpen(false);
      // Reset the form
      setSelectedCollection('no-collection');
      setNotes('');
      toast.success('Coupon saved to your collection!', {
        action: {
          label: "View Saved Coupons",
          onClick: () => navigate('/saved')
        },
        duration: 5000
      });
      setIsPending(false);
    } catch (error) {
      console.error('Error saving coupon:', error);
      toast.error('Failed to save coupon');
      setIsOpen(false);
      setIsPending(false);
    }
  };
  
  const handleRemove = async () => {
    if (!user) return;
    
    if (!dbReady) {
      handleShowDatabaseError();
      return;
    }
    
    try {
      await removeCouponMutation.mutateAsync({
        userId: user.id,
        couponId
      });
      toast.success('Coupon removed from saved items');
    } catch (error) {
      console.error('Error removing coupon:', error);
      toast.error('Failed to remove coupon');
    }
  };

  // If user is not logged in, show login button
  if (!user) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant={variant} 
              size={size} 
              className={className} 
              asChild
            >
              <Link to="/auth">
                <Bookmark className="h-4 w-4 mr-2" />
                {showText && 'Save'}
              </Link>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Sign in to save this coupon</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  // If checking DB status or DB is not ready, show special buttons
  if (isChecking) {
    return (
      <Button 
        variant={variant} 
        size={size} 
        className={className} 
        disabled
      >
        <Bookmark className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />
        {showText && 'Save'}
      </Button>
    );
  }
  
  if (!dbReady) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="outline" 
              size={size} 
              className={`text-amber-600 border-amber-200 ${className}`}
              onClick={handleShowDatabaseError}
            >
              <AlertTriangle className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />
              {showText && 'Setup Required'}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Database setup required for saved coupons</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  // Standard loading state
  if (isSavedLoading) {
    return (
      <Button 
        variant={variant} 
        size={size} 
        className={className} 
        disabled
      >
        <Bookmark className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />
        {showText && 'Save'}
      </Button>
    );
  }
  
  // Show different button if already saved - filled bookmark and option to unsave
  if (isSaved) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="default" 
              size={size} 
              className={`bg-blue-500 hover:bg-blue-600 text-white ${className}`}
              onClick={size === 'icon' ? handleRemove : () => setIsOpen(true)}
            >
              <Bookmark className={`h-4 w-4 fill-white ${showText ? 'mr-2' : ''}`} />
              {showText && 'Saved'}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {size === 'icon' ? 'Remove from saved' : 'Manage saved coupon'}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Regular save button
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant={variant} 
              size={size} 
              className={className}
              onClick={() => setIsOpen(true)}
            >
              <Bookmark className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />
              {showText && 'Save'}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Save this coupon</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save Coupon</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            {isCreatingCollection ? (
              <div className="space-y-2">
                <label className="text-sm font-medium">New Collection Name</label>
                <div className="flex gap-2">
                  <Input 
                    value={newCollectionName} 
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    placeholder="Collection name"
                    className="flex-1"
                  />
                  <Button onClick={handleCreateCollection}>Create</Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsCreatingCollection(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Save to Collection</label>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setIsCreatingCollection(true)}
                    className="text-xs"
                  >
                    <Plus className="h-3.5 w-3.5 mr-1" />
                    New Collection
                  </Button>
                </div>
                
                <Select value={selectedCollection || 'no-collection'} onValueChange={setSelectedCollection}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a collection" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-collection">No Collection</SelectItem>
                    {collections?.map((collection) => (
                      <SelectItem key={collection.id} value={collection.id}>
                        {collection.name} {collection.is_default && '(Default)'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Notes (Optional)</label>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add notes about this coupon..."
                className="min-h-[80px]"
              />
            </div>
            
            <div className="flex gap-2 justify-end mt-6">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button type="button" onClick={handleSave} disabled={isPending}>
                {isPending ? 'Saving...' : 'Save Coupon'}
              </Button>
            </div>
            
            {isSaved && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <Button 
                  type="button" 
                  variant="ghost" 
                  className="w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  onClick={() => {
                    setIsOpen(false);
                    navigate('/saved');
                  }}
                >
                  <Bookmark className="h-4 w-4 mr-2" />
                  View All Saved Coupons
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SaveCouponButton; 