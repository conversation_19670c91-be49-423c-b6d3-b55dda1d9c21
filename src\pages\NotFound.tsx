import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Page Not Found"
          subtitle="The page you're looking for doesn't exist"
          icon={AlertTriangle}
        />

        <div className="max-w-md mx-auto text-center bg-white rounded-lg shadow-sm p-8 border border-gray-100">
          <h1 className="text-5xl font-bold mb-4 text-gray-800">404</h1>
          <p className="text-xl text-gray-600 mb-6">Oops! Page not found</p>
          <Button asChild>
            <Link to="/">
              Return to Home
            </Link>
          </Button>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default NotFound;
