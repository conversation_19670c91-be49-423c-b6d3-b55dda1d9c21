/* Modern Forced Colors Mode Support */
@media (forced-colors: active) {
  .forced-colors-mode {
    forced-color-adjust: none;
  }

  /* Switch Component */
  .switch-root {
    background-color: Canvas;
    border-color: ButtonText;
  }

  .switch-thumb {
    background-color: ButtonText;
    border: 1px solid ButtonText;
  }

  .switch-root[data-state="checked"] {
    background-color: Highlight;
  }

  /* Tabs Component */
  .tabs-list {
    background-color: Canvas;
    border: 1px solid ButtonText;
  }

  .tabs-trigger {
    border: 1px solid transparent;
  }

  .tabs-trigger[data-state="active"] {
    background-color: Highlight;
    color: HighlightText;
    border-color: ButtonText;
  }

  .tabs-content {
    border: 1px solid transparent;
  }

  .tabs-content:focus-visible {
    outline: 2px solid ButtonText;
    outline-offset: 2px;
  }

  /* Radix UI Components Forced Colors Support */
  [data-radix-collection-item] {
    forced-color-adjust: auto;
  }

  /* Dialog Components */
  [data-radix-dialog-overlay] {
    background-color: Canvas;
  }

  [data-radix-dialog-content] {
    background-color: Canvas;
    border: 1px solid ButtonText;
    color: CanvasText;
  }

  /* Popover Components */
  [data-radix-popover-content] {
    background-color: Canvas;
    border: 1px solid ButtonText;
    color: CanvasText;
  }

  /* Select Components */
  [data-radix-select-content] {
    background-color: Canvas;
    border: 1px solid ButtonText;
  }

  [data-radix-select-item] {
    color: CanvasText;
  }

  [data-radix-select-item][data-highlighted] {
    background-color: Highlight;
    color: HighlightText;
  }

  /* Toast Components */
  [data-radix-toast-root] {
    background-color: Canvas;
    border: 1px solid ButtonText;
    color: CanvasText;
  }

  /* Tooltip Components */
  [data-radix-tooltip-content] {
    background-color: Canvas;
    border: 1px solid ButtonText;
    color: CanvasText;
  }

  /* Accordion Components */
  [data-radix-accordion-content] {
    border: 1px solid ButtonText;
  }

  [data-radix-accordion-trigger] {
    color: CanvasText;
  }

  [data-radix-accordion-trigger]:hover {
    background-color: Highlight;
    color: HighlightText;
  }

  /* Avatar Components */
  [data-radix-avatar-root] {
    border: 1px solid ButtonText;
  }

  /* Progress Components */
  [data-radix-progress-root] {
    background-color: Canvas;
    border: 1px solid ButtonText;
  }

  [data-radix-progress-indicator] {
    background-color: Highlight;
  }
}

/* Legacy High Contrast Mode Override - Suppress Deprecation Warnings */
@media (-ms-high-contrast: active) {
  /* Override any third-party library usage with modern equivalents */
  * {
    /* Redirect to forced-colors mode styles */
    forced-color-adjust: auto;
  }
}

/* Fallback for browsers that don't support forced-colors */
@supports not (forced-colors: active) {
  .forced-colors-mode {
    /* Default styles when forced colors are not active */
  }
}