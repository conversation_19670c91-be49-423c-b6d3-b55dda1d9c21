/* Orientation-responsive styles for the onboarding modal */

/* Remove the fixed position from the original class and make it more dynamic */
.onboarding-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

/* Landscape orientation */
@media (orientation: landscape) {
  .onboarding-modal {
    top: 50% !important;
    max-height: 85vh !important;
  }
}

/* Small screens */
@media (max-width: 320px) {
  .onboarding-modal {
    width: 95% !important;
    max-width: 280px !important;
    padding: 8px !important;
  }
}

/* For tablets */
@media (min-width: 768px) {
  .onboarding-modal {
    max-width: 380px !important;
  }
}

/* For very large screens */
@media (min-width: 1200px) {
  .onboarding-modal {
    max-width: 400px !important;
  }
}

/* Ensure modal is visible for small height viewports */
@media screen and (max-height: 500px) {
  .onboarding-modal {
    top: 50% !important;
    max-height: 90vh !important;
  }
}

/* For really small screens, adjust positioning */
@media screen and (max-height: 400px) {
  .onboarding-modal {
    top: 50% !important;
    max-height: 95vh !important;
  }
} 