import React from 'react';
import SEO from '@/seo/components/SEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import CouponSchema from '@/seo/schemas/CouponSchema';
import BreadcrumbSchema from '@/seo/schemas/BreadcrumbSchema';
import FAQSchema from '@/seo/schemas/FAQSchema';
import SearchResultSchema from '@/seo/schemas/SearchResultSchema';
import PageContainer from '@/components/layout/PageContainer';

interface BrandTemplateProps {
  brandName: string;
  description: string;
  logoUrl: string;
  websiteUrl: string;
  coupons: Array<{
    id: string;
    name: string;
    description: string;
    code: string;
    discount: string;
    validFrom: string;
    validThrough: string;
    category?: string;
  }>;
  faqs?: Array<{
    question: string;
    answer: string;
  }>;
  relatedBrands?: Array<{
    name: string;
    url: string;
  }>;
}

/**
 * Brand page template with proper SEO implementation
 * This shows how to use SEO components for best search visibility
 * and rich search results like the Perplexity example
 */
const BrandTemplate: React.FC<BrandTemplateProps> = ({
  brandName,
  description,
  logoUrl,
  websiteUrl,
  coupons,
  faqs = [],
  relatedBrands = []
}) => {
  // Format title and descriptions for SEO
  const seoTitle = `${brandName} Coupons & Promo Codes (${new Date().toLocaleString('default', { month: 'long' })} ${new Date().getFullYear()})`;
  const seoDescription = `Save with the latest ${brandName} coupon codes, promo offers, and discount deals. Updated for ${new Date().toLocaleString('default', { month: 'long' })} ${new Date().getFullYear()}.`;
  const seoKeywords = `${brandName}, ${brandName} coupons, ${brandName} promo codes, ${brandName} discount codes, ${brandName} deals, ${brandName} offers`;
  
  // Breadcrumb items for structured data
  const breadcrumbItems = [
    { name: 'Home', url: 'https://www.couponlink.in/' },
    { name: 'Brands', url: 'https://www.couponlink.in/brands' },
    { name: brandName, url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}` }
  ];

  // Search sections for rich results
  const searchSections = [
    {
      name: 'Active Coupons',
      description: `Current ${brandName} coupon codes and promotional offers`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#coupons`
    },
    {
      name: 'About',
      description: `Learn about ${brandName} and their promotional offers`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#about`
    },
    {
      name: 'FAQs',
      description: `Frequently asked questions about ${brandName} coupons`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#faqs`
    },
    {
      name: 'Related Brands',
      description: 'Similar brands you might be interested in',
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#related`
    }
  ];

  return (
    <>
      {/* Add enhanced SEO components */}
      <SEO 
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        image={logoUrl}
        type="article"
        modifiedTime={new Date().toISOString()}
        breadcrumbs={breadcrumbItems}
      />
      
      {/* Add website schema */}
      <WebsiteSchema />
      
      {/* Add breadcrumb schema */}
      <BreadcrumbSchema items={breadcrumbItems} />
      
      {/* Add FAQ schema */}
      {faqs.length > 0 && <FAQSchema faqs={faqs} />}
      
      {/* Add schema for all coupons */}
      {coupons.map(coupon => (
        <CouponSchema
          key={coupon.id}
          couponName={coupon.name}
          description={coupon.description}
          brandName={brandName}
          discountAmount={coupon.discount}
          couponCode={coupon.code}
          validFrom={coupon.validFrom}
          validThrough={coupon.validThrough}
        />
      ))}

      {/* Add search result schema for multiple sections */}
      <SearchResultSchema
        mainEntity={{
          name: `${brandName} Coupons & Promo Codes`,
          description: seoDescription,
          url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}`
        }}
        sections={searchSections}
      />
      
      <PageContainer>
        <div>
          {/* Template content goes here */}
          {/* You can add your brand page layout components here */}
        </div>
      </PageContainer>
    </>
  );
};

export default BrandTemplate; 