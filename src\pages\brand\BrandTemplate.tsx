import React from 'react';
import SEO from '@/seo/components/SEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import CouponSchema from '@/seo/schemas/CouponSchema';
import BreadcrumbSchema from '@/seo/schemas/BreadcrumbSchema';
import FAQSchema from '@/seo/schemas/FAQSchema';
import SearchResultSchema from '@/seo/schemas/SearchResultSchema';
import PageContainer from '@/components/layout/PageContainer';

interface BrandTemplateProps {
  brandName: string;
  description: string;
  logoUrl: string;
  websiteUrl: string;
  coupons: Array<{
    id: string;
    name: string;
    description: string;
    code: string;
    discount: string;
    validFrom: string;
    validThrough: string;
    category?: string;
  }>;
  faqs?: Array<{
    question: string;
    answer: string;
  }>;
  relatedBrands?: Array<{
    name: string;
    url: string;
  }>;
}

/**
 * Enhanced brand page template with proper SEO implementation
 * This shows how to use SEO components for best search visibility
 * and rich search results like the Perplexity example
 */
const BrandTemplate: React.FC<BrandTemplateProps> = ({
  brandName,
  description,
  logoUrl,
  websiteUrl,
  coupons,
  faqs = [],
  relatedBrands = []
}) => {
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
  
  // Format title and descriptions for SEO
  const seoTitle = `${brandName} Coupons & Promo Codes (${formattedDate}) | Save up to ${getMaxDiscount(coupons)}%`;
  const seoDescription = `Save with ${coupons.length} verified ${brandName} coupon codes, promo offers, and discount deals. Updated for ${formattedDate}. Average success rate: ${getSuccessRate()}%. Last verified ${getLastVerifiedDate()}.`;
  const seoKeywords = `${brandName}, ${brandName} coupons, ${brandName} promo codes, ${brandName} discount codes, ${brandName} deals, ${brandName} offers, ${formattedDate}`;
  
  // Breadcrumb items for structured data
  const breadcrumbItems = [
    { name: 'Home', url: 'https://www.couponlink.in/' },
    { name: 'Brands', url: 'https://www.couponlink.in/brands' },
    { name: brandName, url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}` }
  ];

  // Group coupons by category for search results schema
  const couponsByCategory = coupons.reduce((acc, coupon) => {
    const category = coupon.category || 'General';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(coupon);
    return acc;
  }, {} as Record<string, typeof coupons>);

  // Create sections for search results schema
  const searchSections = [
    {
      name: 'Available Coupons',
      description: `${coupons.length} active coupon codes for ${brandName}`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#coupons`,
      image: logoUrl
    },
    ...Object.entries(couponsByCategory).map(([category, categoryCoupons]) => ({
      name: `${category} Coupons`,
      description: `${categoryCoupons.length} ${brandName} coupon codes for ${category.toLowerCase()}`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}/${category.toLowerCase()}`,
      image: logoUrl
    })),
    {
      name: 'Related Brands',
      description: `Similar brands to ${brandName} with active coupon codes`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#related`,
    },
    {
      name: 'FAQs',
      description: `Frequently asked questions about ${brandName} coupons and promo codes`,
      url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}#faqs`,
    }
  ];
  
  return (
    <>
      {/* Add enhanced SEO components */}
      <SEO 
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        image={logoUrl}
        type="article"
        modifiedTime={new Date().toISOString()}
        breadcrumbs={breadcrumbItems}
      />
      
      {/* Add website schema */}
      <WebsiteSchema />
      
      {/* Add breadcrumb schema */}
      <BreadcrumbSchema items={breadcrumbItems} />
      
      {/* Add FAQ schema */}
      {faqs.length > 0 && <FAQSchema faqs={faqs} />}
      
      {/* Add schema for all coupons */}
      {coupons.map(coupon => (
        <CouponSchema
          key={coupon.id}
          couponName={coupon.name}
          description={coupon.description}
          brandName={brandName}
          discountAmount={coupon.discount}
          couponCode={coupon.code}
          validFrom={coupon.validFrom}
          validThrough={coupon.validThrough}
        />
      ))}

      {/* Add search result schema for multiple sections */}
      <SearchResultSchema
        mainEntity={{
          name: `${brandName} Coupons & Promo Codes`,
          description: seoDescription,
          url: `https://www.couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}`
        }}
        sections={searchSections}
      />
      
      <PageContainer>
        <div className="max-w-6xl mx-auto px-4">
          {/* Visual Breadcrumbs for Users */}
          <nav className="flex py-3 text-sm" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <a href="/" className="text-gray-500 hover:text-primary-main">Home</a>
              </li>
              <li>
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <a href="/brands" className="text-gray-500 hover:text-primary-main">Brands</a>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <span className="text-primary-main font-medium">{brandName}</span>
                </div>
              </li>
            </ol>
          </nav>
          
          {/* Brand Header Section with H1 Tag for SEO */}
          <div className="flex flex-col md:flex-row items-center gap-6 py-8 border-b border-gray-200">
            {/* Brand Logo */}
            <div className="w-24 h-24 rounded-lg border shadow-sm flex items-center justify-center overflow-hidden bg-white">
              <img src={logoUrl} alt={`${brandName} logo`} className="max-w-full max-h-full object-contain" />
            </div>
            
            {/* Brand Info */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">{brandName} Coupon Codes</h1>
              <p className="text-gray-600">{description}</p>
              <div className="flex items-center mt-4">
                <span className="text-sm text-gray-500 mr-2">Visit:</span>
                <a 
                  href={websiteUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary-main hover:text-primary-dark hover:underline text-sm"
                >
                  {websiteUrl.replace(/^https?:\/\/(www\.)?/, '')}
                </a>
              </div>
            </div>
            
            {/* Stats for Trust Signals */}
            <div className="grid grid-cols-2 gap-4 py-4 md:py-0">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-main">{coupons.length}</div>
                <div className="text-xs text-gray-500">Active Offers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-tertiary-main">98%</div>
                <div className="text-xs text-gray-500">Success Rate</div>
              </div>
            </div>
          </div>
          
          {/* SEO-Friendly Content Section */}
          <div className="my-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">About {brandName} Deals</h2>
            <div className="prose prose-sm max-w-none text-gray-600">
              <p>
                Looking for the latest {brandName} discounts? We've compiled all active and verified coupon codes for {brandName}.
                Our team regularly checks and updates these promotional offers to ensure they work when you need them.
              </p>
              <p>
                {brandName} offers various types of promotions throughout the year, including percentage discounts,
                free shipping codes, bundle deals, and seasonal sales. Bookmark this page to stay updated on the
                newest offers as they become available.
              </p>
            </div>
          </div>
          
          {/* Coupon Cards List */}
          <div className="my-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Current {brandName} Offers</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {coupons.map(coupon => (
                <div key={coupon.id} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                  {/* Coupon Header */}
                  <div className="bg-gray-50 p-4 border-b border-gray-200">
                    <h3 className="font-medium text-gray-800">{coupon.name}</h3>
                  </div>
                  
                  {/* Coupon Body */}
                  <div className="p-4">
                    <p className="text-sm text-gray-600 mb-4">{coupon.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="bg-gray-100 rounded px-3 py-2 font-mono text-sm font-medium">{coupon.code}</div>
                      <button className="bg-primary-main text-white px-4 py-2 rounded text-sm font-medium hover:bg-primary-dark transition-colors">
                        Copy Code
                      </button>
                    </div>
                  </div>
                  
                  {/* Coupon Footer */}
                  <div className="bg-gray-50 p-3 border-t border-gray-200 text-xs text-gray-500 flex justify-between">
                    <span>Valid until: {new Date(coupon.validThrough).toLocaleDateString()}</span>
                    <span>Verified Today</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* FAQ Section for More SEO Content */}
          <div className="my-8 border-t border-gray-200 pt-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Frequently Asked Questions</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-800 mb-2">How do I use {brandName} coupon codes?</h3>
                <p className="text-sm text-gray-600">
                  To use a {brandName} coupon code, copy the code from CouponLink, visit the {brandName} website, 
                  add items to your cart, and enter the code at checkout in the promo code or coupon field.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800 mb-2">How often are {brandName} coupons updated?</h3>
                <p className="text-sm text-gray-600">
                  We update {brandName} coupons daily to ensure you always have access to the most current offers. 
                  Our team verifies each code to confirm it's working before listing it on CouponLink.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800 mb-2">Does {brandName} offer student discounts?</h3>
                <p className="text-sm text-gray-600">
                  Yes, {brandName} often provides special discounts for students. Check the offers above for any current 
                  student promotions, or visit the {brandName} website directly and look for their student discount program.
                </p>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </>
  );
};

// Helper function to get maximum discount percentage
const getMaxDiscount = (coupons: BrandTemplateProps['coupons']) => {
  const discounts = coupons.map(coupon => {
    const match = coupon.discount.match(/\d+/);
    return match ? parseInt(match[0], 10) : 0;
  });
  return Math.max(...discounts, 0);
};

// Helper function to calculate success rate (placeholder)
const getSuccessRate = () => {
  return 95; // Replace with actual calculation
};

// Helper function to get last verified date (placeholder)
const getLastVerifiedDate = () => {
  return new Date().toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
};

export default BrandTemplate; 