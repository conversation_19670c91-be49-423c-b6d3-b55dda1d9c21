import { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { resetMenuIndicator } from '@/utils/localStorageHelpers';

// Add this to help with development/testing
const DEMO_MODE = false; // Demo mode disabled

// Demo user - only used if DEMO_MODE is true
const DEMO_USER = {
  id: 'demo-user-123',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Demo User'
  },
  app_metadata: {},
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  confirmed_at: new Date().toISOString(),
  role: 'authenticated',
  updated_at: new Date().toISOString()
} as unknown as User;

const DEMO_PROFILE = {
  id: 'demo-user-123',
  full_name: 'Demo User',
  username: 'demouser',
  avatar_url: null,
  role: 'user'
};

interface Profile {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string | null;
  website: string | null;
  bio: string | null;
  payment_email: string | null;
  payment_setup_completed: boolean;
  social_links: Record<string, string>;
  onboarding_step: string;
  onboarding_completed_at: string | null;
  is_new_user: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthContextProps {
  user: User | null;
  session: Session | null;
  profile: any | null; // Profile from our profiles table
  loading: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, userData?: any) => Promise<any>;
  signOut: () => Promise<void>;
  isAdmin: boolean;
  isInfluencer: boolean;
  refreshProfile: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(DEMO_MODE ? DEMO_USER : null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<any | null>(DEMO_MODE ? DEMO_PROFILE : null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Helper function to generate a username from email
  const generateUsername = (email: string): string => {
    return email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
  };

  useEffect(() => {
    // Skip Supabase auth if in demo mode
    if (DEMO_MODE) {
      setLoading(false);
      return;
    }

    let mounted = true;

    // Initial session check - this is crucial for page refresh
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          // Handle specific auth errors
          if (error.message?.includes('refresh_token_not_found') ||
              error.message?.includes('Invalid Refresh Token') ||
              error.message?.includes('Refresh Token Not Found')) {
            console.log('Refresh token invalid, clearing session');
            try {
              await supabase.auth.signOut();
              localStorage.removeItem('supabase.auth.token');
              sessionStorage.clear();
            } catch (e) {
              console.error('Error clearing auth state:', e);
            }
          }
          if (mounted) {
            setSession(null);
            setUser(null);
            setProfile(null);
            setLoading(false);
          }
          return;
        }

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);

          if (session?.user) {
            await fetchProfile(session.user.id);
          } else {
            setProfile(null);
          }

          setLoading(false);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (mounted) {
          setSession(null);
          setUser(null);
          setProfile(null);
          setLoading(false);
        }
      }
    };

    // Set up the auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (!mounted) return;

        // Handle auth errors
        if (event === 'TOKEN_REFRESHED' && !session) {
          console.log('Token refresh failed, clearing auth state');
          try {
            localStorage.removeItem('supabase.auth.token');
            sessionStorage.clear();
          } catch (e) {
            console.error('Error clearing storage:', e);
          }
        }

        setSession(session);
        setUser(session?.user ?? null);

        // Fetch user profile when auth state changes
        if (session?.user) {
          await fetchProfile(session.user.id);

          // Handle email confirmation - when user clicks confirmation link
          if (event === 'TOKEN_REFRESHED' && session.user.email_confirmed_at) {
            console.log('Email confirmed, redirecting to home for onboarding');
            navigate('/home');
            return;
          }

          // Only navigate on explicit sign-in actions, not on token refresh or initial load
          const isSignInAction = sessionStorage.getItem('signInAction') === 'true';

          if (event === 'SIGNED_IN' && isSignInAction) {
            console.log('Navigating to home after explicit sign-in');
            navigate('/home');
            // Reset the sign-in action flag
            sessionStorage.removeItem('signInAction');
          }
        } else {
          setProfile(null);
        }
      }
    );

    // Initialize authentication
    initializeAuth();

    return () => {
      mounted = false;
      if (subscription) subscription.unsubscribe();
    };
  }, [navigate]);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      
      // Fetch social links for the profile
      const { data: socialLinks, error: socialLinksError } = await supabase
        .from('social_links')
        .select('*')
        .eq('profile_id', userId);
        
      if (socialLinksError) throw socialLinksError;
      
      // Add social links to the profile data
      setProfile({
        ...data,
        social_links: socialLinks || []
      });
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };
  
  // Add refreshProfile function
  const refreshProfile = async () => {
    if (user?.id) {
      await fetchProfile(user.id);
    }
  };

  // Real Supabase auth functions
  const signIn = async (email: string, password: string) => {
    try {
      // Mark this as an explicit user-initiated sign-in action
      sessionStorage.setItem('signInAction', 'true');

      // Reset menu indicator state to ensure it shows after login
      resetMenuIndicator();

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { user: data.user, session: data.session, error: null };
    } catch (error: any) {
      console.error('Error signing in:', error.message);
      // Clear the sign-in flag if there's an error
      sessionStorage.removeItem('signInAction');
      return { user: null, session: null, error };
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      // First check if there's an existing unverified user
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('id')
        .single();

      if (existingUser) {
        // If user exists but is unverified, delete the old profile first
        await supabase
          .from('profiles')
          .delete()
          .eq('id', existingUser.id);
      }

      // Attempt the auth signup
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: metadata?.full_name,
            username: metadata?.username,
            is_new_user: true
          }
        }
      });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error('No user data returned from signup');
      }

      // Use provided username or generate one if not provided
      // Only generate username if none was explicitly provided
      const username = metadata?.username && metadata.username.trim()
        ? metadata.username.trim()
        : generateUsername(email);

      // Using username from metadata or generated from email

      // Create profile with retry logic
      let retryCount = 0;
      const maxRetries = 3;
      let profileData = null;
      let profileError = null;

      while (retryCount < maxRetries) {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .insert([
              {
                id: authData.user.id,
                full_name: metadata?.full_name || '',
                username,
                is_new_user: true,
                role: 'user',
                onboarding_step: 'welcome',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                social_links: {}, // Initialize empty social links
                profile_complete_percent: 0,
                is_verified: false
              }
            ])
            .select()
            .single();

          if (error) {
            if (error.code === '23505') { // Unique violation
              // Wait a short moment before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
              retryCount++;
              continue;
            }
            throw error;
          }

          profileData = data;

          // Initialize user wallet after profile creation
          try {
            await supabase
              .from('user_wallets')
              .insert([{ user_id: authData.user.id }])
              .select()
              .single();
          } catch (walletError: any) {
            // Ignore if wallet already exists (conflict error)
            if (walletError.code !== '23505') {
              console.error('Error creating user wallet:', walletError);
            }
          }

          break;
        } catch (err) {
          profileError = err;
          retryCount++;
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (profileError) {
        console.error('Error creating profile:', profileError);
        
        // If profile creation failed, attempt cleanup
        try {
          await supabase.auth.signOut(); // Sign out first to clear any session
          await supabase.auth.admin.deleteUser(authData.user.id);
        } catch (cleanupError) {
          console.error('Failed to cleanup after profile creation failed:', cleanupError);
        }
        
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      // Return success with both user and profile data
      return { 
        user: authData.user, 
        profile: profileData, 
        error: null 
      };
    } catch (error: any) {
      console.error('Error in signup process:', error);
      return { 
        user: null, 
        profile: null, 
        error: error instanceof Error ? error : new Error('An unknown error occurred during signup') 
      };
    }
  };

  const signOut = async () => {
    try {
      console.log('Starting sign out process...');

      // Clear local state first to provide immediate feedback
      setUser(null);
      setSession(null);
      setProfile(null);

      // Then call Supabase signOut
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Supabase signOut error:', error);
        // Even if Supabase signOut fails, we've already cleared local state
        // so the user appears signed out in the UI
      } else {
        console.log('Successfully signed out from Supabase');
      }

      // Clear any session storage items
      sessionStorage.removeItem('signInAction');

      // Navigate to auth page after sign out
      navigate('/auth');
      console.log('Sign out process completed');
    } catch (error) {
      console.error('Error during sign out process:', error);
      // Even if there's an error, ensure user appears signed out
      setUser(null);
      setSession(null);
      setProfile(null);
      navigate('/auth');
    }
  };

  const isAdmin = profile?.role === 'admin';
  const isInfluencer = profile?.role === 'influencer';

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth?mode=reset_password`
      });
      
      if (error) throw error;
      return { error: null };
    } catch (error: any) {
      console.error('Error resetting password:', error);
      return { error };
    }
  };

  const value = {
    user,
    session,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    isAdmin,
    isInfluencer,
    refreshProfile,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
