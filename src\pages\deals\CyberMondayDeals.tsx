import React, { useState } from 'react';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import CouponCard from '@/components/CouponCard';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import { useCoupons } from '@/hooks/useCoupons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Monitor, 
  Search, 
  Filter, 
  Calendar,
  Zap,
  Smartphone,
  Percent,
  Clock
} from 'lucide-react';

const CyberMondayDeals = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  // Fetch all coupons and filter for Cyber Monday deals
  const { data: allCoupons, isLoading, error } = useCoupons();
  
  // Filter coupons for Cyber Monday deals (tech-focused)
  const cyberMondayCoupons = allCoupons?.filter(coupon => 
    coupon.discount_description?.toLowerCase().includes('cyber monday') ||
    coupon.code?.toLowerCase().includes('cyber') ||
    coupon.code?.toLowerCase().includes('monday') ||
    coupon.category?.name?.toLowerCase().includes('electronics') ||
    coupon.category?.name?.toLowerCase().includes('technology') ||
    coupon.category?.name?.toLowerCase().includes('software') ||
    (coupon.discount_percent >= 25 && coupon.featured === true) // High discount tech deals
  ) || [];
  
  // Apply search and category filters
  const filteredCoupons = cyberMondayCoupons.filter(coupon => {
    const matchesSearch = !searchQuery || 
      coupon.brand?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.discount_description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.code?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || 
      coupon.category?.name?.toLowerCase() === selectedCategory.toLowerCase();
    
    return matchesSearch && matchesCategory;
  });
  
  // Get unique categories from filtered coupons
  const categories = Array.from(new Set(
    cyberMondayCoupons.map(coupon => coupon.category?.name).filter(Boolean)
  ));

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.8}>
        <PageHeaderWithBackButton
          title="Cyber Monday Deals"
          subtitle="Tech deals and digital discounts for Cyber Monday"
          icon={Monitor}
        />

        {/* Cyber Monday Banner */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-800 text-white rounded-xl p-6 mb-8 relative overflow-hidden">
          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/20 p-2 rounded-lg">
                <Smartphone className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Cyber Monday 2024</h2>
                <p className="text-blue-100">Digital deals and tech savings</p>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-white/10 rounded-lg p-3">
                <Percent className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">70%</div>
                <div className="text-xs text-blue-100">Max Discount</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <Zap className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">{cyberMondayCoupons.length}</div>
                <div className="text-xs text-blue-100">Tech Deals</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <Clock className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">48H</div>
                <div className="text-xs text-blue-100">Online Only</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <Calendar className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">Dec 2</div>
                <div className="text-xs text-blue-100">Cyber Monday</div>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search Cyber Monday tech deals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant={selectedCategory === null ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(null)}
              >
                All Categories
              </Button>
              {categories.slice(0, 4).map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Deals Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <CouponsSkeleton count={12} />
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
            <p className="text-red-600">Failed to load Cyber Monday deals. Please try again later.</p>
          </div>
        ) : filteredCoupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCoupons.map((coupon, index) => (
              <motion.div
                key={coupon.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="relative"
              >
                <div className="absolute -top-2 -right-2 z-10">
                  <Badge className="bg-blue-600 text-white">
                    Cyber Monday
                  </Badge>
                </div>
                <CouponCard
                  id={coupon.id}
                  brandName={coupon.brand?.name || "Unknown Brand"}
                  brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                  influencerName={coupon.influencer?.full_name || "Anonymous"}
                  influencerImage={coupon.influencer?.avatar_url}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "General"}
                  featured={coupon.featured}
                  isPremium={coupon.is_premium}
                  brandId={coupon.brand?.id}
                  price={coupon.price}
                />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
            <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No Cyber Monday deals found matching your criteria.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory(null);
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default CyberMondayDeals;
