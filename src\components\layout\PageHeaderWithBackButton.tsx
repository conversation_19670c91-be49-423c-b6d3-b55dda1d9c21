import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import BackButton from '@/components/BackButton';
import { COLORS } from '@/constants/theme';
import { LucideIcon } from 'lucide-react';

interface PageHeaderWithBackButtonProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  children?: ReactNode;
}

/**
 * PageHeaderWithBackButton - A standardized page header with back button
 * Provides consistent spacing, styling, and behavior for page headers
 */
const PageHeaderWithBackButton = ({
  title,
  subtitle,
  icon: Icon,
  children
}: PageHeaderWithBackButtonProps) => {
  return (
    <div className="flex items-center mb-8">
      <BackButton />
      
      <motion.div 
        className="ml-3 flex flex-col"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <div className="flex items-center">
          {Icon && (
            <Icon className="w-6 h-6 mr-2" style={{ color: COLORS.primary.main }} />
          )}
          <h1 className="text-2xl font-bold" style={{ color: COLORS.neutral[800] }}>
            {title}
          </h1>
        </div>
        
        {subtitle && (
          <span className="block text-sm font-medium mt-1" style={{ color: COLORS.neutral[600] }}>
            {subtitle}
          </span>
        )}
      </motion.div>
      
      {children && (
        <div className="ml-auto">{children}</div>
      )}
    </div>
  );
};

export default PageHeaderWithBackButton; 