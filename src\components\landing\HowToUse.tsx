import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { UserPlus, Tag, Share2, ArrowRight } from 'lucide-react';

const HowToUse = () => {
  const navigate = useNavigate();

  const steps = [
    {
      number: 1,
      title: "Create Profile",
      description: "Sign up and personalize your account",
      icon: <UserPlus className="w-4 h-4 text-white" />,
      color: "bg-primary-main",
    },
    {
      number: 2,
      title: "Add Promotions",
      description: "Upload your codes and offers",
      icon: <Tag className="w-4 h-4 text-white" />,
      color: "bg-secondary-main",
    },
    {
      number: 3,
      title: "Share",
      description: "Distribute your link and track results",
      icon: <Share2 className="w-4 h-4 text-white" />,
      color: "bg-tertiary-main",
    }
  ];

  return (
    <section className="py-8 bg-white">
      <div className="max-w-3xl mx-auto px-4">
        {/* Simple Header */}
        <div className="text-center mb-6">
          <h2 className="text-xl font-medium mb-2">How It Works</h2>
          <p className="text-sm text-gray-500">
            Three simple steps to get started
          </p>
        </div>
        
        {/* Minimalist Steps */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          {steps.map((step) => (
            <div 
              key={step.number}
              className="flex-1 bg-white rounded border border-gray-100 p-4 hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center mb-3">
                <div className={`${step.color} w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mr-3`}>
                  {step.icon}
                </div>
                <h3 className="text-sm font-medium">{step.title}</h3>
              </div>
              <p className="text-xs text-gray-500">{step.description}</p>
            </div>
          ))}
        </div>
        
        {/* Video */}
        <div className="mb-6 rounded overflow-hidden bg-gray-50">
          <div className="relative pb-[56.25%] h-0">
            <iframe 
              className="absolute top-0 left-0 w-full h-full"
              src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
              title="Tutorial"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        </div>
        
        {/* CTA */}
        <div className="text-center">
          <Button 
            onClick={() => navigate('/auth?mode=signup')}
            className="bg-primary-main hover:bg-primary-dark text-white text-sm px-4 py-1 h-auto rounded"
          >
            <span className="flex items-center">
              Get Started
              <ArrowRight className="ml-1 h-3 w-3" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HowToUse; 