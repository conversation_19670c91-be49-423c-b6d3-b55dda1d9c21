import React from 'react';
import { Helmet } from 'react-helmet-async';

/**
 * Component that adds enhanced Schema.org website structured data
 * This helps search engines understand your website's organization
 * and display rich sitelinks in search results
 */
const WebsiteSchema: React.FC = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://www.couponlink.in/",
    "name": "CouponLink",
    "description": "Find and share verified coupon codes, promo offers, and discounts from your favorite brands.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://www.couponlink.in/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "CouponLink",
    "url": "https://www.couponlink.in",
    "logo": "https://www.couponlink.in/logos/logo.png",
    "sameAs": [
      "https://www.facebook.com/couponlink",
      "https://www.twitter.com/couponlink",
      "https://www.instagram.com/couponlink"
    ]
  };

  const navigationSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": [
      {
        "@type": "SiteNavigationElement",
        "position": 1,
        "name": "CouponLink",
        "description": "Find and share verified coupon codes from top brands",
        "url": "https://www.couponlink.in/"
      },
      {
        "@type": "SiteNavigationElement",
        "position": 2,
        "name": "Brands",
        "description": "Browse coupons by your favorite brands",
        "url": "https://www.couponlink.in/brands"
      },
      {
        "@type": "SiteNavigationElement",
        "position": 3,
        "name": "Categories",
        "description": "Find coupons by shopping categories",
        "url": "https://www.couponlink.in/categories"
      },
      {
        "@type": "SiteNavigationElement",
        "position": 4,
        "name": "Trending Deals",
        "description": "Popular and trending coupon offers",
        "url": "https://www.couponlink.in/trending"
      },
      {
        "@type": "SiteNavigationElement",
        "position": 5,
        "name": "Discover",
        "description": "Discover new brands and exclusive offers",
        "url": "https://www.couponlink.in/explore"
      }
    ]
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(organizationSchema)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(navigationSchema)}
      </script>
    </Helmet>
  );
};

export default WebsiteSchema; 