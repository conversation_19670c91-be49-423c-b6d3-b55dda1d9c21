import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import BackButton from '@/components/BackButton';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Tag, 
  Grid3x3, 
  Search, 
  Filter, 
  ArrowUpRight, 
  ShoppingBag,
  Monitor,
  Utensils,
  Plane,
  Heart,
  Home,
  Scissors,
  Briefcase,
  Gift,
  Smartphone,
  ShoppingCart,
  Car,
  BookOpen,
  Dog,
  Baby,
  Gamepad2,
  Gem,
  Inbox,
  Tent,
  Download,
  Film,
  Dumbbell,
  Music,
  Tv
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import CouponCard from '@/components/CouponCard';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useCategories } from '@/hooks/useCategories';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { useIsMobile } from '@/hooks/use-mobile';

const Category = () => {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const [searchQuery, setSearchQuery] = useState('');
  const decodedCategoryName = slug ? decodeURIComponent(slug) : '';
  const isMobile = useIsMobile();
  
  // Fetch all categories
  const { data: categories, isLoading: isCategoriesLoading } = useCategories();
  
  // Filter categories based on search query when in list view
  const filteredCategories = categories?.filter(category => 
    !searchQuery || category.name.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];
  
  // Get category details when viewing a specific category
  const selectedCategory = slug ? categories?.find(
    cat => cat.name.toLowerCase() === decodedCategoryName.toLowerCase()
  ) : null;
  
  // Fetch coupons for selected category
  const { data: coupons, isLoading: isCouponsLoading } = useQuery({
    queryKey: ['category-coupons', selectedCategory?.id],
    queryFn: async () => {
      try {
        if (!selectedCategory?.id) return [];

        // First get coupon data
        const { data: couponData, error } = await supabase
          .from('coupons')
          .select('*')
          .eq('category_id', selectedCategory.id)
          .eq('status', 'active')
          .order('created_at', { ascending: false });

        if (error) throw error;
        if (!couponData) return [];

        // Then get related data for each coupon
        const couponsWithRelations = await Promise.all(
          couponData.map(async (coupon) => {
            const getBrandData = async () => {
              if (!coupon.brand_id) return null;
              try {
                const { data } = await supabase
                  .from('brands')
                  .select('id, name, logo_url, website')
                  .eq('id', coupon.brand_id)
                  .single();
                return data;
              } catch {
                return null;
              }
            };

            const getInfluencerData = async () => {
              if (!coupon.influencer_id) return null;
              try {
                const { data } = await supabase
                  .from('profiles')
                  .select('id, full_name, username, avatar_url')
                  .eq('id', coupon.influencer_id)
                  .single();
                return data;
              } catch {
                return null;
              }
            };

            const getCategoryData = async () => {
              if (!coupon.category_id) return null;
              try {
                const { data } = await supabase
                  .from('categories')
                  .select('id, name')
                  .eq('id', coupon.category_id)
                  .single();
                return data;
              } catch {
                return null;
              }
            };

            const [brandData, influencerData, categoryData] = await Promise.all([
              getBrandData(),
              getInfluencerData(),
              getCategoryData()
            ]);

            return {
              ...coupon,
              active: coupon.status === 'active',
              brand: brandData || undefined,
              influencer: influencerData || undefined,
              category: categoryData || undefined
            } as any;
          })
        );

        return couponsWithRelations;
      } catch (error) {
        console.error('Error fetching category coupons:', error);
        return [];
      }
    },
    enabled: !!selectedCategory?.id,
  });
  
  // Get coupon counts for categories
  const { data: categoryCounts } = useQuery({
    queryKey: ['category-counts'],
    queryFn: async () => {
      try {
        if (!categories || categories.length === 0) return {};
        
        const counts: Record<string, number> = {};
        
        await Promise.all(
          categories.map(async (category) => {
            const { count, error } = await supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('status', 'active')
              .eq('category_id', category.id);
            
            if (!error) {
              counts[category.id] = count || 0;
            }
          })
        );
        
        return counts;
      } catch (error) {
        console.error('Error fetching category counts:', error);
        return {};
      }
    },
    enabled: !!categories && categories.length > 0
  });
  
  // Filter coupons based on search query when in category view
  const filteredCoupons = coupons?.filter(coupon => 
    !searchQuery || (
      (coupon.brand?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.influencer?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.discount_description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.code?.toLowerCase().includes(searchQuery.toLowerCase()))
    )
  ) || [];

  // Category icon mapping
  const getCategoryIcon = (name: string) => {
    const iconMap = {
      'Electronics': <Monitor className="h-6 w-6" />,
      'Fashion': <ShoppingBag className="h-6 w-6" />,
      'Food': <Utensils className="h-6 w-6" />,
      'Travel': <Plane className="h-6 w-6" />,
      'Beauty': <Heart className="h-6 w-6" />,
      'Home': <Home className="h-6 w-6" />,
      'Sports': <Briefcase className="h-6 w-6" />,
      'Health': <Heart className="h-6 w-6" />,
      'Technology': <Smartphone className="h-6 w-6" />,
      'Services': <Scissors className="h-6 w-6" />,
      'Gifts': <Gift className="h-6 w-6" />,
      'Automotive': <Car className="h-6 w-6" />,
      'Education': <BookOpen className="h-6 w-6" />,
      'Pets': <Dog className="h-6 w-6" />,
      'Baby and Kids': <Baby className="h-6 w-6" />,
      'Gaming': <Gamepad2 className="h-6 w-6" />,
      'Jewelry': <Gem className="h-6 w-6" />,
      'Office': <Inbox className="h-6 w-6" />,
      'Outdoors': <Tent className="h-6 w-6" />,
      'Software': <Download className="h-6 w-6" />,
      'Streaming': <Tv className="h-6 w-6" />,
      'Fitness': <Dumbbell className="h-6 w-6" />,
      'Entertainment': <Music className="h-6 w-6" />
    };
    
    return iconMap[name] || <ShoppingCart className="h-6 w-6" />;
  };
  
  // Color mapping for categories
  const getCategoryColor = (name: string) => {
    const colorMap = {
      'Electronics': 'from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600',
      'Fashion': 'from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600',
      'Food': 'from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600',
      'Travel': 'from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600',
      'Beauty': 'from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600',
      'Home': 'from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600',
      'Sports': 'from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600',
      'Health': 'from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600',
      'Technology': 'from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600',
      'Services': 'from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600',
      'Gifts': 'from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600',
      'Automotive': 'from-slate-600 to-gray-700 hover:from-slate-700 hover:to-gray-800',
      'Education': 'from-amber-500 to-yellow-400 hover:from-amber-600 hover:to-yellow-500',
      'Pets': 'from-amber-400 to-orange-300 hover:from-amber-500 hover:to-orange-400',
      'Baby and Kids': 'from-sky-400 to-blue-300 hover:from-sky-500 hover:to-blue-400',
      'Gaming': 'from-fuchsia-600 to-purple-600 hover:from-fuchsia-700 hover:to-purple-700',
      'Jewelry': 'from-amber-300 to-yellow-200 hover:from-amber-400 hover:to-yellow-300',
      'Office': 'from-slate-500 to-gray-400 hover:from-slate-600 hover:to-gray-500',
      'Outdoors': 'from-lime-600 to-green-500 hover:from-lime-700 hover:to-green-600',
      'Software': 'from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',
      'Streaming': 'from-red-600 to-rose-500 hover:from-red-700 hover:to-rose-600',
      'Fitness': 'from-emerald-500 to-green-400 hover:from-emerald-600 hover:to-green-500',
      'Entertainment': 'from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600'
    };
    
    return colorMap[name] || 'from-gray-500 to-slate-500 hover:from-gray-600 hover:to-slate-600';
  };

  const handleCategoryClick = (categoryId: string, categoryName: string) => {
    navigate(`/categories/${encodeURIComponent(categoryName)}`);
  };
  
  return (
    <MainLayout>
      <div className="w-full bg-gradient-to-b from-green-50 to-teal-50 dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
          <div className="mb-4">
            <BackButton />
          </div>
          
          {/* Header Section - Always visible */}
          <div className="bg-gradient-to-br from-green-600 to-teal-600 dark:from-green-800 dark:to-teal-900 rounded-lg p-4 md:p-6 border border-green-500/30 dark:border-green-700/30 shadow-xl mb-8">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-white flex items-center">
                  {slug ? (
                    <>
                      <Tag className="mr-2 h-7 w-7 md:h-8 md:w-8 text-white" />
                      {selectedCategory?.name || decodedCategoryName}
                    </>
                  ) : (
                    <>
                      <Grid3x3 className="mr-2 h-7 w-7 md:h-8 md:w-8 text-white" />
                      Categories
                    </>
                  )}
                </h1>
                <p className="text-green-50 mt-1 text-sm md:text-base">
                  {slug
                    ? (selectedCategory?.description || `Browse all ${decodedCategoryName} coupons and deals`)
                    : 'Browse deals by category to find the perfect discount'}
                </p>
              </div>
              
              <div className="relative max-w-md w-full">
                <div className="absolute inset-y-0 left-3 flex items-center">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  placeholder={`Search ${slug ? 'coupons' : 'categories'}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/90 dark:bg-gray-800/90 placeholder:text-gray-400 border-green-200 dark:border-green-800"
                />
              </div>
            </div>
          </div>
          
          {/* Content Section - conditional based on if we're in a category or the categories list */}
          {slug ? (
            /* Specific Category View */
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800 dark:text-white">
                  Available Deals
                </h2>
                
                <Link to="/categories">
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Grid3x3 className="h-4 w-4" />
                    <span>All Categories</span>
                  </Button>
                </Link>
              </div>
              
              {isCouponsLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {[...Array(8)].map((_, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 h-[300px] animate-pulse">
                      <div className="flex items-center justify-between mb-4">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <Skeleton className="h-4 w-16 rounded-full" />
                      </div>
                      <Skeleton className="h-6 w-3/4 mb-4" />
                      <Skeleton className="h-4 w-1/2 mb-8" />
                      <Skeleton className="h-20 w-full mb-4" />
                      <Skeleton className="h-10 w-full" />
                    </div>
                  ))}
                </div>
              ) : filteredCoupons.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredCoupons.map((coupon) => (
                    <motion.div
                      key={coupon.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="h-full"
                    >
                      <CouponCard
                        id={coupon.id}
                        brandName={coupon.brand?.name || "Unknown Brand"}
                        brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                        influencerName={coupon.influencer?.full_name || "Anonymous"}
                        influencerImage={coupon.influencer?.avatar_url}
                        discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                        expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                        couponCode={coupon.code}
                        category={coupon.category?.name || "General"}
                        featured={coupon.featured}
                        isPremium={coupon.is_premium}
                        brandId={coupon.brand?.id}
                      />
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center shadow-sm">
                  <div className="mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 mb-4">
                    <Tag className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">No coupons found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    {searchQuery 
                      ? `No ${selectedCategory?.name || 'category'} coupons matching "${searchQuery}"`
                      : `No coupons available in the ${selectedCategory?.name || decodedCategoryName} category right now.`}
                  </p>
                  {searchQuery && (
                    <Button 
                      variant="outline" 
                      onClick={() => setSearchQuery('')}
                    >
                      Clear Search
                    </Button>
                  )}
                </div>
              )}
            </div>
          ) : (
            /* Categories List View */
            <div>
              <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-6">
                Browse by Category
              </h2>
              
              {isCategoriesLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="h-32 bg-white dark:bg-gray-800 rounded-xl animate-pulse"></div>
                  ))}
                </div>
              ) : filteredCategories.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {filteredCategories.map((category, index) => (
                    <motion.div 
                      key={category.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="cursor-pointer"
                      onClick={() => handleCategoryClick(category.id, category.name)}
                    >
                      <div className={`block h-full bg-gradient-to-br ${getCategoryColor(category.name)} text-white p-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-xl`}>
                        <div className="flex flex-col h-full relative z-10">
                          <div className="flex items-center mb-3">
                            <div className="p-2 bg-white/20 rounded-lg">
                              {getCategoryIcon(category.name)}
                            </div>
                          </div>
                          <h3 className="text-lg font-semibold">{category.name}</h3>
                          <p className="mt-1 text-sm text-white/80">
                            {categoryCounts?.[category.id] || 0} {(categoryCounts?.[category.id] || 0) === 1 ? 'coupon' : 'coupons'}
                          </p>
                          <div className="mt-auto pt-4 text-sm flex items-center text-white/90 hover:text-white group">
                            View deals <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
                          </div>
                          
                          {/* Decorative circles */}
                          <div className="absolute top-0 right-0 w-24 h-24 rounded-full bg-white/10 -translate-y-1/2 translate-x-1/2"></div>
                          <div className="absolute bottom-0 left-0 w-16 h-16 rounded-full bg-white/5 translate-y-1/2 -translate-x-1/2"></div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center shadow-sm">
                  <div className="mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                    <Grid3x3 className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">No categories found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6">
                    {searchQuery 
                      ? `No categories matching "${searchQuery}"`
                      : `No categories available at the moment.`}
                  </p>
                  {searchQuery && (
                    <Button 
                      variant="outline" 
                      onClick={() => setSearchQuery('')}
                    >
                      Clear Search
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default Category; 