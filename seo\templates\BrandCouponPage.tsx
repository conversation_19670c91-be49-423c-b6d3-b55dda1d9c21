import React from 'react';
import Head from 'next/head';
import { format } from 'date-fns';
import OfferSchema from '../schemas/OfferSchema';
import FAQSchema from '../schemas/FAQSchema'; // Assume this exists

interface Coupon {
  id: string;
  code: string;
  discount: string;
  description: string;
  expirationDate: string;
  validFrom: string;
  category: string;
  successRate: number;
  usageCount: number;
  termsAndConditions?: string;
  isExclusive: boolean;
  isVerified: boolean;
  lastVerifiedDate: string;
}

interface BrandCouponPageProps {
  brandName: string;
  brandDescription: string;
  brandLogo: string;
  brandWebsite: string;
  coupons: Coupon[];
  pageUrl: string;
  faqs: Array<{question: string; answer: string}>;
  relatedBrands: Array<{name: string; url: string}>;
  popularCategories: Array<{name: string; url: string}>;
}

const BrandCouponPage: React.FC<BrandCouponPageProps> = ({
  brandName,
  brandDescription,
  brandLogo,
  brandWebsite,
  coupons,
  pageUrl,
  faqs,
  relatedBrands,
  popularCategories
}) => {
  const activeCouponsCount = coupons.length;
  const currentDate = new Date();
  const formattedDate = format(currentDate, 'MMMM yyyy');
  const lastVerified = coupons.length > 0 
    ? new Date(Math.max(...coupons.map(c => new Date(c.lastVerifiedDate).getTime())))
    : currentDate;
  const formattedLastVerified = format(lastVerified, 'MMMM d, yyyy');
  
  // Calculate average success rate
  const avgSuccessRate = coupons.length > 0 
    ? coupons.reduce((acc, curr) => acc + curr.successRate, 0) / coupons.length
    : 0;

  return (
    <>
      <Head>
        <title>{`${activeCouponsCount} ${brandName} Coupon Codes (${formattedDate}) | CouponLink.in`}</title>
        <meta name="description" content={`Save with ${activeCouponsCount} verified ${brandName} coupon codes and promo codes. Updated for ${formattedDate}. Average success rate: ${avgSuccessRate.toFixed(0)}%. Last verified on ${formattedLastVerified}.`} />
        <meta name="keywords" content={`${brandName} coupon, ${brandName} promo code, ${brandName} discount code, ${brandName} deals, ${formattedDate}`} />
        
        {/* Open Graph tags */}
        <meta property="og:title" content={`${activeCouponsCount} ${brandName} Coupon Codes (${formattedDate})`} />
        <meta property="og:description" content={`Save with verified ${brandName} coupon codes. Average success rate: ${avgSuccessRate.toFixed(0)}%.`} />
        <meta property="og:image" content={brandLogo} />
        <meta property="og:url" content={pageUrl} />
        <meta property="og:type" content="website" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${activeCouponsCount} ${brandName} Coupon Codes (${formattedDate})`} />
        <meta name="twitter:description" content={`Save with verified ${brandName} coupon codes. Updated ${formattedLastVerified}.`} />
        <meta name="twitter:image" content={brandLogo} />
        
        {/* Canonical URL */}
        <link rel="canonical" href={pageUrl} />
      </Head>

      {/* Apply schema markup for each coupon */}
      {coupons.map(coupon => (
        <OfferSchema
          key={coupon.id}
          brandName={brandName}
          couponCode={coupon.code}
          description={coupon.description}
          discount={coupon.discount}
          expirationDate={coupon.expirationDate}
          validFrom={coupon.validFrom}
          category={coupon.category}
          image={brandLogo}
          url={`${pageUrl}#${coupon.id}`}
          successRate={coupon.successRate}
          usageCount={coupon.usageCount}
          termsAndConditions={coupon.termsAndConditions}
        />
      ))}

      {/* Apply FAQ Schema */}
      {faqs.length > 0 && (
        <FAQSchema faqs={faqs} />
      )}

      <main className="brand-coupon-page">
        <div className="container mx-auto px-4 py-8">
          {/* Brand header section with rich data */}
          <section className="brand-header mb-8">
            <div className="flex items-center">
              <img src={brandLogo} alt={`${brandName} logo`} className="w-24 h-24 object-contain mr-4" />
              <div>
                <h1 className="text-3xl font-bold">{`${brandName} Coupon Codes (${formattedDate})`}</h1>
                <p className="text-gray-600">
                  <span className="font-bold text-green-600">{activeCouponsCount} active coupons</span> • 
                  <span className="text-blue-600"> {avgSuccessRate.toFixed(0)}% success rate</span> • 
                  <span className="text-gray-500"> Last verified: {formattedLastVerified}</span>
                </p>
              </div>
            </div>
            <div className="brand-description mt-4">
              <h2 className="text-xl font-semibold">About {brandName} Coupons</h2>
              <p className="mt-2">{brandDescription}</p>
              <div className="mt-4 flex items-center">
                <a href={brandWebsite} target="_blank" rel="noopener noreferrer" className="text-blue-600 font-medium hover:underline">
                  Visit {brandName} Website
                </a>
              </div>
            </div>
          </section>

          {/* Quick stats for rich snippets */}
          <section className="coupon-stats bg-gray-50 p-4 rounded-lg mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="stat-box text-center p-2">
                <span className="block text-2xl font-bold text-green-600">{activeCouponsCount}</span>
                <span className="text-sm text-gray-600">Active Coupons</span>
              </div>
              <div className="stat-box text-center p-2">
                <span className="block text-2xl font-bold text-blue-600">{avgSuccessRate.toFixed(0)}%</span>
                <span className="text-sm text-gray-600">Success Rate</span>
              </div>
              <div className="stat-box text-center p-2">
                <span className="block text-2xl font-bold text-purple-600">
                  {coupons.reduce((acc, curr) => acc + curr.usageCount, 0).toLocaleString()}
                </span>
                <span className="text-sm text-gray-600">Total Uses</span>
              </div>
              <div className="stat-box text-center p-2">
                <span className="block text-2xl font-bold text-orange-600">
                  {coupons.filter(c => c.isExclusive).length}
                </span>
                <span className="text-sm text-gray-600">Exclusive Coupons</span>
              </div>
            </div>
          </section>

          {/* Coupon list section */}
          <section className="coupons-list mb-8">
            <h2 className="text-2xl font-bold mb-4">All {brandName} Coupon Codes</h2>
            <div className="grid grid-cols-1 gap-4">
              {coupons.map(coupon => (
                <div key={coupon.id} id={coupon.id} className="coupon-card border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start">
                    <div>
                      <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mb-2">
                        {coupon.category}
                      </span>
                      {coupon.isExclusive && (
                        <span className="inline-block bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm font-medium mb-2 ml-2">
                          Exclusive
                        </span>
                      )}
                      {coupon.isVerified && (
                        <span className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mb-2 ml-2">
                          Verified
                        </span>
                      )}
                      <h3 className="text-xl font-bold">{coupon.discount}</h3>
                      <p className="text-gray-700 mt-1">{coupon.description}</p>
                      <div className="mt-2 text-sm text-gray-500">
                        <span>Success rate: {coupon.successRate}%</span> • 
                        <span> Used {coupon.usageCount.toLocaleString()} times</span> • 
                        <span> Expires: {format(new Date(coupon.expirationDate), 'MMM d, yyyy')}</span>
                      </div>
                    </div>
                    <div className="coupon-action">
                      <div className="coupon-code bg-gray-100 border border-dashed border-gray-400 p-2 rounded font-mono text-center mb-2">
                        {coupon.code}
                      </div>
                      <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                        Copy Code
                      </button>
                    </div>
                  </div>
                  {coupon.termsAndConditions && (
                    <div className="terms-conditions mt-3 text-xs text-gray-500">
                      <details>
                        <summary className="cursor-pointer">Terms & Conditions</summary>
                        <p className="mt-1">{coupon.termsAndConditions}</p>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </section>

          {/* FAQ Section for SEO and rich snippets */}
          <section className="faq-section mb-8">
            <h2 className="text-2xl font-bold mb-4">Frequently Asked Questions about {brandName} Coupons</h2>
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <details key={index} className="bg-white rounded-lg border p-4">
                  <summary className="font-medium text-lg cursor-pointer">{faq.question}</summary>
                  <p className="mt-2 text-gray-700">{faq.answer}</p>
                </details>
              ))}
            </div>
          </section>

          {/* Related Brands Section for internal linking */}
          <section className="related-brands mb-8">
            <h2 className="text-2xl font-bold mb-4">Similar Brands to {brandName}</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {relatedBrands.map((brand, index) => (
                <a 
                  key={index} 
                  href={brand.url} 
                  className="text-blue-600 hover:underline p-2 text-center"
                >
                  {brand.name} Coupons
                </a>
              ))}
            </div>
          </section>

          {/* Popular Categories for internal linking */}
          <section className="categories">
            <h2 className="text-2xl font-bold mb-4">Popular Coupon Categories</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {popularCategories.map((category, index) => (
                <a 
                  key={index} 
                  href={category.url} 
                  className="text-blue-600 hover:underline p-2 text-center"
                >
                  {category.name} Coupons
                </a>
              ))}
            </div>
          </section>
        </div>
      </main>
    </>
  );
};

export default BrandCouponPage; 