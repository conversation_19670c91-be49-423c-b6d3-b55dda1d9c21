import React from 'react';
import { Helmet } from 'react-helmet-async';

interface InfluencerSEOProps {
  title: string;
  description: string;
  brandName?: string;
  dealType?: string;
  discountAmount?: string;
  validUntil?: string;
  influencerCode?: string;
}

/**
 * Specialized SEO component for influencer marketing and link-in-bio optimization
 * Targets searches related to influencer codes, social media deals, and bio links
 */
const InfluencerSEO: React.FC<InfluencerSEOProps> = ({
  title,
  description,
  brandName,
  dealType = 'discount',
  discountAmount,
  validUntil,
  influencerCode
}) => {
  // Generate influencer-specific keywords
  const influencerKeywords = [
    'link in bio',
    'linkinbio',
    'bio link',
    'social media deals',
    'influencer codes',
    'creator codes',
    'exclusive codes',
    'follower discount',
    'social discount',
    'instagram deals',
    'tiktok coupons',
    'youtube codes',
    'twitter deals',
    'facebook offers',
    'social media coupons',
    'influencer discount',
    'creator discount',
    'exclusive offer',
    'limited time deal',
    'social media exclusive',
    'follower only deal',
    'subscriber discount',
    'community deal',
    'social savings',
    'viral deal',
    'trending offer',
    'social media promo',
    'influencer partnership',
    'brand collaboration',
    'sponsored deal'
  ];

  if (brandName) {
    influencerKeywords.push(
      `${brandName.toLowerCase()} influencer code`,
      `${brandName.toLowerCase()} creator code`,
      `${brandName.toLowerCase()} social media deal`,
      `${brandName.toLowerCase()} exclusive offer`,
      `${brandName.toLowerCase()} link in bio`
    );
  }

  const keywordsString = influencerKeywords.join(', ');

  // Generate structured data for deals
  const dealSchema = {
    "@context": "https://schema.org",
    "@type": "Offer",
    "name": title,
    "description": description,
    "url": window.location.href,
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock",
    "validFrom": new Date().toISOString(),
    ...(validUntil && { "validThrough": validUntil }),
    ...(discountAmount && { "discount": discountAmount }),
    "seller": {
      "@type": "Organization",
      "name": brandName || "CouponLink",
      "url": "https://couponlink.in"
    },
    "category": "Coupon Code",
    "additionalType": "PromotionalOffer"
  };

  return (
    <Helmet>
      {/* Influencer-specific meta tags */}
      <meta name="keywords" content={keywordsString} />
      <meta name="social-media-optimized" content="true" />
      <meta name="link-in-bio-ready" content="true" />
      <meta name="influencer-friendly" content="true" />
      <meta name="creator-codes" content="available" />
      
      {/* Deal-specific metadata */}
      {dealType && <meta name="deal-type" content={dealType} />}
      {discountAmount && <meta name="discount-amount" content={discountAmount} />}
      {validUntil && <meta name="valid-until" content={validUntil} />}
      {influencerCode && <meta name="influencer-code" content={influencerCode} />}
      
      {/* Social media platform specific */}
      <meta name="instagram-compatible" content="true" />
      <meta name="tiktok-compatible" content="true" />
      <meta name="youtube-compatible" content="true" />
      <meta name="twitter-compatible" content="true" />
      <meta name="facebook-compatible" content="true" />
      <meta name="pinterest-compatible" content="true" />
      <meta name="snapchat-compatible" content="true" />
      
      {/* Mobile and app specific */}
      <meta name="mobile-optimized" content="true" />
      <meta name="app-deep-link-ready" content="true" />
      <meta name="quick-access" content="true" />
      
      {/* Enhanced Open Graph for social sharing */}
      <meta property="og:type" content="product" />
      <meta property="product:price:currency" content="USD" />
      <meta property="product:availability" content="in stock" />
      <meta property="product:condition" content="new" />
      <meta property="product:category" content="Deals & Coupons" />
      
      {/* Instagram specific */}
      <meta property="instapp:owner_user_id" content="couponlink" />
      <meta property="instapp:url" content={window.location.href} />
      
      {/* TikTok specific */}
      <meta name="tiktok:app_id" content="couponlink" />
      <meta name="tiktok:title" content={title} />
      <meta name="tiktok:description" content={description} />
      
      {/* YouTube specific */}
      <meta name="youtube-compatible" content="true" />
      <meta name="video-description-ready" content="true" />
      
      {/* Pinterest specific */}
      <meta name="pinterest:rich_pin" content="true" />
      <meta property="og:see_also" content="https://couponlink.in" />
      
      {/* Structured data for deals */}
      <script type="application/ld+json">
        {JSON.stringify(dealSchema)}
      </script>
      
      {/* Additional structured data for social media */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "SocialMediaPosting",
          "headline": title,
          "description": description,
          "url": window.location.href,
          "datePublished": new Date().toISOString(),
          "author": {
            "@type": "Organization",
            "name": "CouponLink"
          },
          "publisher": {
            "@type": "Organization",
            "name": "CouponLink",
            "url": "https://couponlink.in"
          },
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": window.location.href
          }
        })}
      </script>
    </Helmet>
  );
};

export default InfluencerSEO;
