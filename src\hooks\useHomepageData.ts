import { useQuery, useQueries } from '@tanstack/react-query';
import { useTrendingCoupons, usePremiumCoupons } from './useCoupons';
import { useTopBrands } from './useBrands';
import { useCategories } from './useCategories';

/**
 * Optimized hook for loading all homepage data efficiently
 * Uses parallel queries and smart caching to improve performance
 */
export const useHomepageData = () => {
  // Use parallel queries for better performance
  const queries = useQueries({
    queries: [
      {
        queryKey: ['trending-coupons', 12],
        queryFn: async () => {
          const { data } = await import('./useCoupons');
          return data.useTrendingCoupons(12);
        },
        staleTime: 1000 * 60 * 10, // 10 minutes
        gcTime: 1000 * 60 * 60, // 1 hour
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ['premium-coupons', 4],
        queryFn: async () => {
          const { data } = await import('./useCoupons');
          return data.usePremiumCoupons(4);
        },
        staleTime: 1000 * 60 * 15, // 15 minutes
        gcTime: 1000 * 60 * 60, // 1 hour
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ['top-brands', 6],
        queryFn: async () => {
          const { data } = await import('./useBrands');
          return data.useTopBrands(6);
        },
        staleTime: 1000 * 60 * 30, // 30 minutes (brands change less frequently)
        gcTime: 1000 * 60 * 120, // 2 hours
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ['categories'],
        queryFn: async () => {
          const { data } = await import('./useCategories');
          return data.useCategories();
        },
        staleTime: 1000 * 60 * 60, // 1 hour (categories rarely change)
        gcTime: 1000 * 60 * 240, // 4 hours
        refetchOnWindowFocus: false,
      },
    ],
  });

  // Extract results from parallel queries
  const [trendingQuery, premiumQuery, brandsQuery, categoriesQuery] = queries;

  return {
    trendingCoupons: {
      data: trendingQuery.data,
      isLoading: trendingQuery.isLoading,
      error: trendingQuery.error,
    },
    premiumCoupons: {
      data: premiumQuery.data,
      isLoading: premiumQuery.isLoading,
      error: premiumQuery.error,
    },
    topBrands: {
      data: brandsQuery.data,
      isLoading: brandsQuery.isLoading,
      error: brandsQuery.error,
    },
    categories: {
      data: categoriesQuery.data,
      isLoading: categoriesQuery.isLoading,
      error: categoriesQuery.error,
    },
    // Overall loading state
    isLoading: queries.some(query => query.isLoading),
    // Check if any critical data failed to load
    hasError: queries.some(query => query.error),
    // Get all errors
    errors: queries.map(query => query.error).filter(Boolean),
  };
};

/**
 * Hook for prefetching homepage data
 * Use this to preload data before navigating to homepage
 */
export const usePrefetchHomepageData = () => {
  const trendingCoupons = useTrendingCoupons(12);
  const premiumCoupons = usePremiumCoupons(4);
  const topBrands = useTopBrands(6);
  const categories = useCategories();

  return {
    prefetchTrending: trendingCoupons.refetch,
    prefetchPremium: premiumCoupons.refetch,
    prefetchBrands: topBrands.refetch,
    prefetchCategories: categories.refetch,
  };
};

/**
 * Lightweight hook for essential homepage data only
 * Use when you need minimal data for initial render
 */
export const useEssentialHomepageData = () => {
  const trendingCoupons = useTrendingCoupons(6); // Reduced limit
  const topBrands = useTopBrands(4); // Reduced limit

  return {
    trendingCoupons,
    topBrands,
    isLoading: trendingCoupons.isLoading || topBrands.isLoading,
    hasError: !!trendingCoupons.error || !!topBrands.error,
  };
};
