import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Brand {
  id: string;
  name: string;
  logo_url: string | null;
  website: string | null;
  created_at: string | null;
  updated_at: string | null;
  coupons_count?: number;
}

export const useBrands = () => {
  return useQuery({
    queryKey: ['brands'],
    queryFn: async (): Promise<Brand[]> => {
      try {
        const { data, error } = await supabase
          .from('brands')
          .select('*')
          .order('name');
        
        if (error) throw error;
        
        // Get coupon count for each brand
        const brandsWithCounts = await Promise.all(
          (data || []).map(async (brand) => {
            const { count } = await supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('brand_id', brand.id)
              .eq('status', 'active');
            
            return {
              ...brand,
              coupons_count: count || 0,
            };
          })
        );
        
        return brandsWithCounts;
      } catch (error) {
        console.error('Error fetching brands:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useBrand = (brandId: string) => {
  return useQuery({
    queryKey: ['brand', brandId],
    queryFn: async (): Promise<Brand | null> => {
      if (!brandId) return null;
      
      try {
        const { data, error } = await supabase
          .from('brands')
          .select('*')
          .eq('id', brandId)
          .single();
        
        if (error) {
          if (error.code === 'PGRST116') {
            // Record not found
            return null;
          }
          throw error;
        }
        
        // Get coupon count for the brand
        const { count } = await supabase
          .from('coupons')
          .select('*', { count: 'exact', head: true })
          .eq('brand_id', brandId)
          .eq('status', 'active');
        
        return {
          ...data,
          coupons_count: count || 0,
        };
      } catch (error) {
        console.error('Error fetching brand:', error);
        return null;
      }
    },
    enabled: !!brandId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useBrandCoupons = (brandId: string) => {
  return useQuery({
    queryKey: ['brand-coupons', brandId],
    queryFn: async () => {
      if (!brandId) return [];

      try {
        // Get coupons first
        const { data: couponData, error } = await supabase
          .from('coupons')
          .select('*')
          .eq('brand_id', brandId)
          .eq('status', 'active')
          .order('created_at', { ascending: false });

        if (error) {
          if (error.code === 'PGRST116') {
            return [];
          }
          throw error;
        }

        if (!couponData) return [];

        // Get related data for each coupon
        const couponsWithRelations = await Promise.all(
          couponData.map(async (coupon) => {
            const [influencerData, categoryData] = await Promise.all([
              coupon.influencer_id ? supabase
                .from('profiles')
                .select('id, full_name, username, avatar_url')
                .eq('id', coupon.influencer_id)
                .single()
                .then(({ data }) => data)
                .catch(() => null) : null,

              coupon.category_id ? supabase
                .from('categories')
                .select('*')
                .eq('id', coupon.category_id)
                .single()
                .then(({ data }) => data)
                .catch(() => null) : null
            ]);

            return {
              ...coupon,
              influencer: influencerData || undefined,
              category: categoryData || undefined
            } as any;
          })
        );

        return couponsWithRelations;
      } catch (error) {
        console.error('Error fetching brand coupons:', error);
        return [];
      }
    },
    enabled: !!brandId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const usePopularBrands = (limit = 5) => {
  return useQuery({
    queryKey: ['popular-brands', limit],
    queryFn: async (): Promise<Brand[]> => {
      try {
        // First get all brands
        const { data: brands, error } = await supabase
          .from('brands')
          .select('*')
          .limit(limit * 3); // Fetch more than needed for filtering
        
        if (error) throw error;
        
        // Get coupon count for each brand
        const brandsWithCounts = await Promise.all(
          (brands || []).map(async (brand) => {
            const { count } = await supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('brand_id', brand.id)
              .eq('status', 'active');
            
            return {
              ...brand,
              coupons_count: count || 0,
            };
          })
        );
        
        // Sort by coupon count and take top 'limit'
        return brandsWithCounts
          .sort((a, b) => (b.coupons_count || 0) - (a.coupons_count || 0))
          .slice(0, limit);
      } catch (error) {
        console.error('Error fetching popular brands:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}; 