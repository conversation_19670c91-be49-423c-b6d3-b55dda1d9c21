import React from 'react';
import { usePara<PERSON>, useLocation } from 'react-router-dom';
import { useUser } from '@/hooks/useUsers';
import { useInfluencerCoupons } from '@/hooks/useCoupons';
import { Coupon } from '@/types/coupon';
import MainLayout from '@/components/layout/MainLayout';
import { User, Check, ChevronRight, Twitter, Facebook, Copy, Share2, Linkedin, Edit, X, Download, Phone, Mail, ExternalLink, Camera, Eye, Star, Link as LinkIcon, MessageSquare as Message } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { motion, AnimatePresence } from 'framer-motion';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAnalytics } from '@/hooks/useAnalytics';
import { QRCodeSVG } from 'qrcode.react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { COLORS } from '@/constants/theme';
import { cn } from '@/lib/utils';
import SimpleCouponList from '@/components/user/SimpleCouponList';
import SharedProfile from './SharedProfile';

const ProfilePreview = () => {
  const { username } = useParams();
  const location = useLocation();
  const { user: currentUser, profile: currentUserProfile } = useAuth();
  const [copied, setCopied] = useState(false);
  const [customMessage, setCustomMessage] = useState('');
  const [isEditingMessage, setIsEditingMessage] = useState(false);
  const [profileUrl, setProfileUrl] = useState('');
  const [qrSize, setQrSize] = useState(180);
  const [activeTab, setActiveTab] = useState('link');
  
  // If no username is provided in the URL, use the current logged-in user's profile username
  const targetUsername = username || currentUserProfile?.username || '';
  
  const { data: user, isLoading: userLoading, error } = useUser(targetUsername);
  const { data: coupons, isLoading: couponsLoading } = useInfluencerCoupons(user?.id || '');
  const { trackProfileShare } = useAnalytics();

  // Check if this is a direct profile view (accessed via /:username)
  const isDirectView = location.pathname === `/${targetUsername}`;

  useEffect(() => {
    // Ensure we have a proper absolute URL with protocol
    const url = new URL(`/${encodeURIComponent(targetUsername)}`, window.location.origin).toString();
    setProfileUrl(url);
    setCustomMessage(`Check out all the coupon codes from ${targetUsername}`);
  }, [targetUsername]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      setCopied(true);
      toast.success('Link copied to clipboard!');
      trackShare('copy');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  // Helper function to track shares in a consistent way
  const trackShare = (platform: string) => {
    try {
      trackProfileShare(targetUsername, platform);
    } catch (error) {
      console.error('Error tracking share:', error);
    }
  };

  const shareProfile = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out ${targetUsername}'s coupons`,
          text: customMessage,
          url: profileUrl,
        });
        trackShare('system');
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      copyToClipboard();
    }
  };

  const shareToTwitter = () => {
    const text = encodeURIComponent(customMessage);
    const url = encodeURIComponent(profileUrl);
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
    trackShare('twitter');
  };

  const shareToFacebook = () => {
    const url = encodeURIComponent(profileUrl);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
    trackShare('facebook');
  };

  const shareToLinkedIn = () => {
    const text = encodeURIComponent(customMessage);
    const url = encodeURIComponent(profileUrl);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&summary=${text}`, '_blank');
    trackShare('linkedin');
  };

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${customMessage} ${profileUrl}`);
    window.open(`https://wa.me/?text=${text}`, '_blank');
    trackShare('whatsapp');
  };

  const shareByEmail = () => {
    const subject = encodeURIComponent(`Check out ${targetUsername}'s profile`);
    const body = encodeURIComponent(`${customMessage}\n\n${profileUrl}`);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
    trackShare('email');
  };

  const shareByText = () => {
    if (navigator.share) {
      navigator.share({
        title: `${targetUsername}'s Profile`,
        text: `${customMessage} ${profileUrl}`,
      })
      .then(() => trackShare('text'))
      .catch(err => console.error('Error sharing via text:', err));
    } else {
      copyToClipboard();
      toast.info('No SMS capability detected. Link copied instead!');
    }
  };

  const downloadQrCode = async () => {
    try {
      toast.loading('Preparing QR code for download...');
      const url = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(profileUrl)}&size=500x500&margin=20&color=2d3748&bgcolor=FFFFFF&format=png&qzone=2`;
      
      // Fetch the image data
      const response = await fetch(url);
      const blob = await response.blob();
      
      // Create a local URL for the blob
      const blobUrl = URL.createObjectURL(blob);
      
      // Create an anchor element and trigger download
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = `${targetUsername}-qr.png`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(blobUrl);
      
      trackShare('qr-download');
      toast.dismiss();
      toast.success('QR code downloaded successfully!');
    } catch (error) {
      console.error('Error:', error);
      toast.dismiss();
      toast.error('Failed to download QR code');
    }
  };

  // Handle case when user is not logged in and trying to access without a username
  if (!targetUsername && !currentUser) {
    return (
      <MainLayout>
        <div className="min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50">
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
            className="max-w-md w-full px-6 py-8"
          >
            <div className="bg-white rounded-2xl shadow-lg p-8 border border-neutral-200">
              <div className="w-16 h-16 mx-auto mb-5 rounded-full bg-primary-light/20 flex items-center justify-center">
                <User className="h-8 w-8" style={{ color: COLORS.primary.main }} />
              </div>
              
              <h2 className="text-2xl font-bold mb-3 text-center" style={{ color: COLORS.primary.main }}>
                Login Required
              </h2>
              
              <p className="mb-6 text-center text-neutral-600">
                Please log in to view your profile and share your coupons with others.
              </p>
              
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button 
                  asChild
                  className="w-full py-2.5"
                  style={{ background: COLORS.primary.gradient, color: 'white' }}
                >
                  <Link to="/auth">
                    Login / Sign Up
                  </Link>
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="min-h-[calc(100vh-64px)] bg-gradient-to-b from-blue-50/50 to-slate-50">
        <div className="container max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
          {/* Back button with improved styling */}
          <div className="mb-8">
            <Link 
              to={`/profile`}
              className="inline-flex items-center text-sm font-medium hover:text-blue-600 transition-colors group bg-white px-3 py-1.5 rounded-full shadow-sm border border-slate-200"
            >
              <ChevronRight className="h-4 w-4 mr-1.5 rotate-180 text-slate-400 group-hover:text-blue-500 transition-colors" />
              <span>Back to Dashboard</span>
            </Link>
          </div>
          
          {/* Page title */}
          <div className="mb-8 text-center">
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 mb-2">Profile Preview & Sharing</h1>
            <p className="text-slate-500 max-w-2xl mx-auto">View your profile as others see it and share it across platforms</p>
          </div>
          
          {/* Main tabs */}
          <Tabs defaultValue="preview" className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList className="grid w-full max-w-md grid-cols-2 h-auto p-1 bg-slate-100 rounded-lg">
                <TabsTrigger 
                  value="preview" 
                  className="py-3 text-base h-auto data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Profile
                </TabsTrigger>
                <TabsTrigger 
                  value="share" 
                  className="py-3 text-base h-auto data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Profile
                </TabsTrigger>
              </TabsList>
            </div>
            
            {/* Preview Tab Content */}
            <TabsContent value="preview" className="focus-visible:outline-none focus-visible:ring-0">
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                <div className="p-6 border-b border-slate-100">
                  <h2 className="text-lg font-semibold text-slate-800">
                    Profile Preview
                  </h2>
                  <p className="text-sm text-slate-500">
                    See how your profile appears to others
                  </p>
                </div>

                {/* Single preview display */}
                <div className="p-2 sm:p-6 flex justify-center">
                  <div className="w-full max-w-6xl px-0 sm:px-4">
                    <div className="sm:transform sm:scale-100 scale-[1.05] origin-top">
                      <SharedProfile
                        user={user}
                        userLoading={userLoading}
                        coupons={coupons ? coupons.map(coupon => ({
                          ...coupon,
                          user_id: user?.id || ''
                        } as Coupon)) : []}
                        couponsLoading={couponsLoading}
                        profileUrl={profileUrl}
                        copyToClipboard={copyToClipboard}
                        shareProfile={shareProfile}
                        copied={copied}
                        shouldHighlightShare={false}
                        shareSectionRef={useRef(null)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            {/* Share Tab Content */}
            <TabsContent value="share" className="focus-visible:outline-none focus-visible:ring-0">
              <div className="bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
                <div className="p-6 border-b border-slate-100">
                  <h2 className="text-lg font-semibold text-slate-800">
                    Share Your Profile
                  </h2>
                  <p className="text-sm text-slate-500">
                    Choose how you want to share your profile with others
                  </p>
                </div>
                
                {/* Custom share message */}
                <div className="p-6 border-b border-slate-100">
                  <div className="mb-2">
                    <div className="flex items-center justify-between mb-3">
                      <label className="text-sm font-medium text-slate-700">
                        Customize Your Message
                      </label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditingMessage(!isEditingMessage)}
                        className="h-8 px-2 text-xs hover:bg-blue-50 hover:text-blue-600"
                      >
                        {isEditingMessage ? (
                          <>
                            <X className="h-3.5 w-3.5 mr-1" />
                            Cancel
                          </>
                        ) : (
                          <>
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Edit
                          </>
                        )}
                      </Button>
                    </div>
                    
                    <AnimatePresence mode="wait">
                      {isEditingMessage ? (
                        <motion.div
                          key="edit"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Textarea
                            value={customMessage}
                            onChange={(e) => setCustomMessage(e.target.value)}
                            className="resize-none h-24 focus-visible:ring-blue-500"
                            placeholder="Write a custom message to share..."
                          />
                          <div className="flex justify-end mt-3">
                            <Button
                              size="sm"
                              onClick={() => setIsEditingMessage(false)}
                              className="h-9 bg-blue-600 hover:bg-blue-700"
                            >
                              Save Message
                            </Button>
                          </div>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="view"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="bg-gradient-to-br from-blue-50 to-slate-50 p-4 rounded-xl border border-slate-200"
                        >
                          <p className="text-sm text-slate-700">
                            {customMessage}
                          </p>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
                
                {/* Sharing methods subtabs */}
                <div className="p-6">
                  <Tabs defaultValue="link" className="w-full">
                    <TabsList className="w-full mb-5 grid grid-cols-2 h-auto p-1 bg-slate-100">
                      <TabsTrigger 
                        value="link" 
                        className="py-2.5 text-sm h-auto data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm"
                      >
                        Copy Link & QR
                      </TabsTrigger>
                      <TabsTrigger 
                        value="platforms" 
                        className="py-2.5 text-sm h-auto data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm"
                      >
                        Social Media
                      </TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="link" className="mt-0 focus-visible:outline-none focus-visible:ring-0">
                      {/* Copy link functionality */}
                      <div className="mb-6">
                        <label className="text-sm font-medium block mb-2.5 text-slate-700">
                          Share Profile Link
                        </label>
                        <div className="flex">
                          <div className="flex-grow relative">
                            <Input
                              value={profileUrl}
                              readOnly
                              className="pr-10 rounded-l-lg rounded-r-none border-r-0 bg-slate-50 focus-visible:ring-blue-500"
                            />
                          </div>
                          <Button
                            onClick={copyToClipboard}
                            className={cn(
                              "rounded-l-none",
                              "transition-all duration-300",
                              copied ? "bg-green-500 hover:bg-green-600" : "bg-blue-600 hover:bg-blue-700"
                            )}
                          >
                            {copied ? <Check className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                            {copied ? "Copied" : "Copy"}
                          </Button>
                        </div>
                      </div>
                      
                      {/* QR Code with improved styling */}
                      <div>
                        <label className="text-sm font-medium block mb-2.5 text-slate-700">
                          Profile QR Code
                        </label>
                        <div className="bg-gradient-to-br from-white to-slate-50 p-6 rounded-xl border border-slate-200 mb-4 flex flex-col items-center">
                          <div className="bg-white p-4 rounded-xl shadow-sm mb-4 border border-slate-100">
                            <QRCodeSVG
                              value={profileUrl}
                              size={qrSize}
                              level="H"
                              fgColor="#2d3748"
                              includeMargin={true}
                            />
                          </div>
                          <Button 
                            variant="outline" 
                            onClick={downloadQrCode}
                            className="text-sm border-slate-300 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                          >
                            <Download className="h-4 w-4 mr-1.5" />
                            Download QR Code
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="platforms" className="mt-0 focus-visible:outline-none focus-visible:ring-0">
                      <div className="space-y-5">
                        <div>
                          <label className="text-sm font-medium block mb-2.5 text-slate-700">
                            Share on Social Media
                          </label>
                          <div className="grid grid-cols-2 gap-3">
                            <Button
                              variant="outline"
                              onClick={shareToTwitter}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Twitter className="h-4 w-4 mr-2" />
                              Twitter
                            </Button>
                            <Button
                              variant="outline"
                              onClick={shareToFacebook}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Facebook className="h-4 w-4 mr-2" />
                              Facebook
                            </Button>
                            <Button
                              variant="outline"
                              onClick={shareToLinkedIn}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Linkedin className="h-4 w-4 mr-2" />
                              LinkedIn
                            </Button>
                            <Button
                              variant="outline"
                              onClick={shareToWhatsApp}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Phone className="h-4 w-4 mr-2" />
                              WhatsApp
                            </Button>
                          </div>
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium block mb-2.5 text-slate-700">
                            Other Sharing Options
                          </label>
                          <div className="grid grid-cols-2 gap-3">
                            <Button
                              variant="outline"
                              onClick={shareByEmail}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Mail className="h-4 w-4 mr-2" />
                              Email
                            </Button>
                            <Button
                              variant="outline"
                              onClick={shareByText}
                              className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                            >
                              <Message className="h-4 w-4 mr-2" />
                              Text Message
                            </Button>
                            {navigator.share && (
                              <Button
                                variant="outline"
                                onClick={shareProfile}
                                className="justify-start h-auto py-2.5 border-slate-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200"
                              >
                                <Share2 className="h-4 w-4 mr-2" />
                                System Share
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
};

export default ProfilePreview; 