import { ReactNode } from 'react';

export interface PageContainerProps {
  children: ReactNode;
  className?: string;
  withBackButton?: boolean;
  fullWidth?: boolean;
  noBackground?: boolean;
  customBackground?: string;
  decorationType?: 'none' | 'default' | 'minimal' | 'landing';
  decorationOpacity?: number;
}

/**
 * PageContainer - A standardized container for page content
 * Provides consistent spacing and maximum width for all pages
 * Enhanced with customizable backgrounds and decorative elements
 */
const PageContainer = ({ 
  children, 
  className = "",
  withBackButton = true,
  fullWidth = false,
  noBackground = false,
  customBackground = "",
  decorationType = "default",
  decorationOpacity = 1
}: PageContainerProps) => {
  // Get background style based on props
  const getBackgroundStyle = () => {
    if (noBackground) return "bg-transparent";
    if (customBackground) return customBackground;
    return "bg-gradient-to-br from-indigo-50 via-purple-50/30 to-white dark:from-gray-900 dark:via-indigo-950/10 dark:to-gray-900";
  };
  
  // Get decoration elements based on type
  const renderDecorations = (type: PageContainerProps['decorationType'], opacity: number = 0.5) => {
    switch (type) {
      case 'none':
        return null;
      case 'minimal':
        return (
          <>
            <div 
              className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary-bgLight rounded-full blur-3xl" 
              style={{ opacity: opacity * 0.4, transform: 'translate(30%, -30%)' }}
            ></div>
          </>
        );
      case 'landing':
        return (
          <>
            <div 
              className="absolute top-0 right-0 w-1/2 h-2/5 bg-primary-bgLight rounded-full blur-3xl"
              style={{ opacity: opacity * 0.5, transform: 'translate(25%, -25%)' }}
            ></div>
            <div 
              className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-secondary-bgLight rounded-full blur-3xl"
              style={{ opacity: opacity * 0.4, transform: 'translate(-25%, 25%)' }}
            ></div>
            <div 
              className="absolute top-1/4 left-1/5 w-20 h-20 bg-accent-bgLight rounded-full blur-xl"
              style={{ opacity: opacity * 0.3 }}
            ></div>
          </>
        );
      default:
        return (
          <>
            <div 
              className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary-bgLight rounded-full blur-3xl" 
              style={{ opacity: opacity * 0.4, transform: 'translate(30%, -30%)' }}
            ></div>
            <div 
              className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-secondary-bgLight rounded-full blur-3xl" 
              style={{ opacity: opacity * 0.3, transform: 'translate(-30%, 30%)' }}
            ></div>
          </>
        );
    }
  };
  
  return (
    <div className="w-full relative overflow-hidden">
      {/* Content with background - no min-height to avoid full page height */}
      <div className={`relative rounded-xl ${getBackgroundStyle()}`}>
        {/* Decorative elements */}
        {renderDecorations(decorationType, decorationOpacity)}
        
        {/* Main content with consistent padding and extra bottom padding for taskbar */}
        <div className={`${fullWidth ? 'max-w-full' : 'max-w-6xl'} mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 pb-8 sm:pb-12 relative z-10 ${className}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageContainer; 