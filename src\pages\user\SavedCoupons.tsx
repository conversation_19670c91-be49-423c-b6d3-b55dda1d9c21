import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useSavedCoupons, useCollections, useRemoveSavedCoupon, useCreateCollection, useDeleteCollection } from '@/hooks/useSavedCoupons';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Bookmark, Plus, Folder, FolderOpen, Trash2, Archive, MoreHorizontal, Edit, FolderX, Settings, Search, X, AlertTriangle, ChevronLeft, ChevronDown, ChevronUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import CouponCard from '@/components/CouponCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import BackButton from '@/components/BackButton';
import PageContainer from '@/components/layout/PageContainer';

const SavedCouponsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeCollection, setActiveCollection] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dbChecked, setDbChecked] = useState(false);
  const [showCollectionsMobile, setShowCollectionsMobile] = useState(false);
  
  // Collection creation state
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  
  // Collection deletion state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [collectionToDelete, setCollectionToDelete] = useState<string | null>(null);
  const [shouldDeleteCoupons, setShouldDeleteCoupons] = useState(false);
  
  // Check if collections table exists
  useEffect(() => {     async function checkDatabase() {
      if (!user) return;
      
      try {
        // First check if collections table exists
        const { error: collectionsError } = await supabase
          .from('collections')
          .select('id')
          .limit(1);

        if (collectionsError) {
          console.error("Collections table error:", collectionsError);
          setError("The collections table doesn't exist. Please run the database setup SQL.");
          return;
        }

        // Check saved_coupons table structure
        const { error: savedCouponsError } = await supabase
          .from('saved_coupons')
          .select('collection_id, notes')
          .limit(1);

        if (savedCouponsError) {
          console.error("Saved coupons table error:", savedCouponsError);
          setError("The saved_coupons table is missing required columns. Please run the database migration SQL.");
          return;
        }
        
        setDbChecked(true);
      } catch (err) {
        console.error("Database check error:", err);
        setError("Error checking database. Please ensure Supabase is running and your tables are set up correctly.");
      }
    }
    
    checkDatabase();
  }, [user]);
  
  // Get saved coupons and collections only if DB check passed
  const { data: savedCoupons, isLoading: savedCouponsLoading, error: savedCouponsError } = useSavedCoupons(
    user?.id, 
    activeCollection,
    { enabled: !!user && dbChecked }
  );
  
  const { data: collections, isLoading: collectionsLoading, error: collectionsError } = useCollections(
    user?.id,
    { enabled: !!user && dbChecked }
  );
  
  const createCollectionMutation = useCreateCollection();
  const deleteCollectionMutation = useDeleteCollection();
  const removeSavedCouponMutation = useRemoveSavedCoupon();
  
  // Reset mobile collections visibility when collection changes
  useEffect(() => {
    setShowCollectionsMobile(false);
  }, [activeCollection]);

  // Check if we need to create a default collection
  useEffect(() => {
    const createDefaultCollection = async () => {
      if (!user || !dbChecked || collectionsLoading || collectionsError) return;
      
      // If there are no collections, create a default one
      if (collections && collections.length === 0) {
        try {
          console.log("Creating default collection as none exist");
          await createCollectionMutation.mutateAsync({
            userId: user.id,
            name: "Saved Coupons",
            description: "Default collection for saved coupons",
            isDefault: true
          });
        } catch (err) {
          console.error("Failed to create default collection:", err);
        }
      }
    };
    
    createDefaultCollection();
  }, [user, dbChecked, collections, collectionsLoading, collectionsError, createCollectionMutation]);
  
  // Check for query errors
  useEffect(() => {
    if (savedCouponsError) {
      console.error("Saved coupons fetch error:", savedCouponsError);
      setError("Error loading saved coupons. Please check your database setup.");
    }
    
    if (collectionsError) {
      console.error("Collections fetch error:", collectionsError);
      setError("Error loading collections. Please check your database setup.");
    }
  }, [savedCouponsError, collectionsError]);
  
  // Filter coupons by search query with enhanced search
  const filteredCoupons = savedCoupons ? (() => {
    if (!searchQuery.trim()) return savedCoupons;

    const query = searchQuery.toLowerCase();

    return savedCoupons.filter(item => {
      const coupon = item.coupon;
      if (!coupon) return false;

      // Enhanced search across multiple fields (only using fields that exist in DB)
      return (
        coupon.title?.toLowerCase().includes(query) ||
        coupon.code?.toLowerCase().includes(query) ||
        coupon.brand?.name?.toLowerCase().includes(query) ||
        coupon.influencer?.full_name?.toLowerCase().includes(query) ||
        coupon.influencer?.username?.toLowerCase().includes(query) ||
        coupon.category?.name?.toLowerCase().includes(query) ||
        coupon.discount_description?.toLowerCase().includes(query) ||
        item.notes?.toLowerCase().includes(query)
      );
    });
  })() : [];
  
  // Handle collection creation
  const handleCreateCollection = async () => {
    if (!user) return;
    if (!newCollectionName.trim()) {
      toast.error('Please enter a collection name');
      return;
    }
    
    try {
      await createCollectionMutation.mutateAsync({
        userId: user.id,
        name: newCollectionName.trim(),
        description: newCollectionDescription.trim()
      });
      
      setIsCreateDialogOpen(false);
      setNewCollectionName('');
      setNewCollectionDescription('');
    } catch (error) {
      console.error('Error creating collection:', error);
      toast.error('Failed to create collection');
    }
  };
  
  // Handle collection deletion
  const openDeleteDialog = (collectionId: string) => {
    setCollectionToDelete(collectionId);
    setIsDeleteDialogOpen(true);
    setShouldDeleteCoupons(false);
  };
  
  const handleDeleteCollection = async () => {
    if (!user || !collectionToDelete) return;
    
    try {
      await deleteCollectionMutation.mutateAsync({
        userId: user.id,
        collectionId: collectionToDelete,
        deleteCoupons: shouldDeleteCoupons
      });
      
      // If the deleted collection was active, reset to show all coupons
      if (activeCollection === collectionToDelete) {
        setActiveCollection(null);
      }
      
      setIsDeleteDialogOpen(false);
      setCollectionToDelete(null);
    } catch (error) {
      console.error('Error deleting collection:', error);
    }
  };
  
  // Handle coupon removal
  const handleRemoveCoupon = async (couponId: string) => {
    if (!user) return;
    
    try {
      await removeSavedCouponMutation.mutateAsync({
        userId: user.id,
        couponId
      });
    } catch (error) {
      console.error('Error removing coupon:', error);
    }
  };
  
  // Handle not logged in state
  if (!user) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h2 className="text-2xl font-bold mb-4">My Saved Coupons</h2>
          <p className="text-gray-600 mb-6">Please log in to view your saved coupons.</p>
          <Button asChild>
            <Link to="/auth">Sign In</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  // Display error message if there's an error
  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4 text-red-700">Database Error</h2>
            <p className="text-red-600 mb-6">{error}</p>
            <div className="bg-white p-4 rounded-lg border border-red-100 text-left mb-6">
              <p className="text-sm font-medium mb-2">Run the following SQL in your Supabase dashboard:</p>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto whitespace-pre-wrap">
                {`-- Create collections table
CREATE TABLE public.collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add columns to saved_coupons
ALTER TABLE public.saved_coupons 
ADD COLUMN IF NOT EXISTS collection_id UUID REFERENCES public.collections(id),
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS saved_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Add RLS policies
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;

-- Users can only access their own collections
CREATE POLICY "Users can view their own collections" 
ON public.collections FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own collections" 
ON public.collections FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own collections" 
ON public.collections FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own collections" 
ON public.collections FOR DELETE 
USING (auth.uid() = user_id);`}
              </pre>
            </div>
            <Button onClick={() => window.location.reload()}>Refresh Page</Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Get current collection name for display
  const currentCollectionName = activeCollection 
    ? collections?.find(c => c.id === activeCollection)?.name 
    : 'All Saved Coupons';

  return (
    <MainLayout>
      <PageContainer>
        {/* Top navigation with better spacing */}
        <div className="mb-6">
          <BackButton />
        </div>

        {/* Header section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-400 dark:to-purple-400">{currentCollectionName}</h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
              {filteredCoupons?.length || 0} {filteredCoupons?.length === 1 ? 'coupon' : 'coupons'} 
              {searchQuery ? ` matching "${searchQuery}"` : ''}
            </p>
          </div>
          
          <div className="flex gap-2 self-end sm:self-auto">
            {showSearch ? (
              <div className="relative flex items-center">
                <Input
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by brand, code, category, notes..."
                  className="w-full sm:w-64 pr-8"
                  autoFocus
                />
                <Button 
                  variant="ghost" 
                  size="icon" 
                    className="absolute right-0 h-full text-gray-400 hover:text-gray-600"
                  onClick={() => {
                    setSearchQuery('');
                    setShowSearch(false);
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowSearch(true)}
                  className="h-10"
              >
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            )}
          </div>
        </div>
          
        {/* Mobile Collections Toggle */}
        <div className="lg:hidden mb-4">
          <Button 
            variant="outline" 
            className="w-full flex justify-between items-center py-3 text-left"
            onClick={() => setShowCollectionsMobile(!showCollectionsMobile)}
          >
            <div className="flex items-center">
              <Folder className="h-4 w-4 mr-2 text-indigo-500" />
              <span>{activeCollection 
                ? collections?.find(c => c.id === activeCollection)?.name 
                : 'All Saved Coupons'}</span>
            </div>
            {showCollectionsMobile ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </Button>
        </div>
      
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar - Collections with fixed width on desktop, collapsible on mobile */}
          <div className={`w-full lg:w-64 xl:w-72 shrink-0 ${showCollectionsMobile ? 'block' : 'hidden lg:block'}`}>
            <div className="bg-white/80 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg shadow-sm p-4 border border-gray-200/60 dark:border-gray-700/60 sticky top-20">
              <div className="space-y-1">
                <Button 
                    variant={activeCollection === null ? "default" : "ghost"}
                    className={`w-full justify-start h-12 ${activeCollection === null ? 'bg-indigo-500 text-white hover:bg-indigo-600' : 'text-gray-700 hover:text-indigo-600 dark:text-gray-300'}`}
                  onClick={() => setActiveCollection(null)}
                >
                    <Bookmark className={`h-5 w-5 mr-2 ${activeCollection === null ? 'text-white' : 'text-indigo-500'}`} />
                  All Saved Coupons
                </Button>
                
                {collectionsLoading ? (
                  <div className="space-y-2 mt-2">
                    {[1, 2, 3].map(i => (
                        <Skeleton key={i} className="h-12 w-full" />
                    ))}
                  </div>
                ) : collections && collections.length > 0 ? (
                  collections.map(collection => (
                      <div key={collection.id} className="flex items-center gap-1 group">
                      <Button
                          variant={activeCollection === collection.id ? "default" : "ghost"}
                          className={`flex-1 justify-start h-12 ${activeCollection === collection.id ? 'bg-indigo-500 text-white hover:bg-indigo-600' : 'text-gray-700 hover:text-indigo-600 dark:text-gray-300'}`}
                        onClick={() => setActiveCollection(collection.id)}
                      >
                          <FolderOpen className={`h-5 w-5 mr-2 ${activeCollection === collection.id ? 'text-white' : 'text-indigo-500'}`} />
                        <span className="truncate">{collection.name}</span>
                        {collection.coupon_count ? (
                            <span className={`ml-2 text-xs py-0.5 px-1.5 rounded-full ${activeCollection === collection.id ? 'bg-white/20 text-white' : 'bg-indigo-100/80 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-300'}`}>
                            {collection.coupon_count}
                          </span>
                        ) : null}
                      </Button>
                      
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                              className="h-10 w-10 opacity-80 lg:opacity-0 lg:group-hover:opacity-100 transition-opacity"
                          >
                              <MoreHorizontal className="h-5 w-5" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48 p-2">
                          <div className="flex flex-col space-y-1">
                            <Button 
                              variant="ghost" 
                              size="sm" 
                                className="justify-start h-10"
                              onClick={() => navigate(`/collections/${collection.id}`)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                                className="justify-start h-10 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                              onClick={() => openDeleteDialog(collection.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  ))
                ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    No collections yet
                  </p>
                )}
              </div>
            </div>
          </div>
          
          {/* Main Content - Coupons with better responsive grid */}
          <div className="flex-1">
            {/* Add New Create Collection button at the top of main content */}
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {activeCollection 
                  ? collections?.find(c => c.id === activeCollection)?.name || "Collection"
                  : "Your Saved Coupons"}
              </h2>
              <Button 
                variant="outline" 
                className="text-indigo-600 dark:text-indigo-400 border-indigo-200 dark:border-indigo-800 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 h-10"
                onClick={() => setIsCreateDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Collection
              </Button>
            </div>
            
            {savedCouponsLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {[1, 2, 3, 4, 5, 6].map(i => (
                  <Skeleton key={i} className="h-64 rounded-lg" />
                ))}
              </div>
            ) : filteredCoupons && filteredCoupons.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-5">
                {filteredCoupons.map(item => (
                    <div key={item.id} className="relative group transform transition-transform hover:-translate-y-1 duration-200 hover:shadow-md">
                    {item.coupon && (
                      <CouponCard 
                        id={item.coupon.id}
                        brandName={item.coupon.brand?.name || ""}
                        brandLogo={item.coupon.brand?.logo_url || ""}
                        influencerName={item.coupon.influencer?.full_name || ""}
                        influencerImage={item.coupon.influencer?.avatar_url}
                        discountAmount={item.coupon.discount_description || `${item.coupon.discount_percent || ''}% OFF`}
                        expirationTime={item.coupon.expires_at ? new Date(item.coupon.expires_at).toLocaleDateString() : "No expiration"}
                        couponCode={item.coupon.code}
                        category={item.coupon.category?.name || ""}
                        featured={item.coupon.featured}
                        isPremium={item.coupon.is_premium}
                        isSaved={true}
                      />
                    )}
                    
                      {/* Quick actions overlay - larger touch target for mobile */}
                      <div className="absolute top-2 right-2 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity">
                      <Button 
                        variant="destructive" 
                        size="icon" 
                          className="h-10 w-10 rounded-full bg-white/90 border border-red-200 text-red-500 hover:bg-red-50 shadow-sm"
                        onClick={() => handleRemoveCoupon(item.coupon_id)}
                        title="Remove from saved"
                      >
                          <Trash2 className="h-5 w-5" />
                      </Button>
                    </div>
                    
                    {/* Notes indicator */}
                    {item.notes && (
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 via-black/50 to-transparent p-3 text-white text-sm rounded-b-lg">
                          <p className="line-clamp-2 text-shadow">{item.notes}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : searchQuery ? (
                <div className="text-center py-12 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg border border-gray-200/60 dark:border-gray-700/60">
                <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">No search results</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                  No coupons match your search for "{searchQuery}"
                </p>
                <Button onClick={() => setSearchQuery('')}>Clear search</Button>
              </div>
            ) : (
                <div className="text-center py-12 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg border border-gray-200/60 dark:border-gray-700/60">
                <Bookmark className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">No saved coupons</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {activeCollection 
                    ? "This collection is empty. Save some coupons to see them here."
                    : "You haven't saved any coupons yet."}
                </p>
                  <Button asChild className="h-12 px-6 text-base">
                  <Link to="/explore">Explore Coupons</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </PageContainer>
      
      {/* Create Collection Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md max-w-[95vw] rounded-lg">
          <DialogHeader>
            <DialogTitle>Create New Collection</DialogTitle>
            <DialogDescription>
              Create a new collection to organize your saved coupons.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Collection Name</label>
              <Input 
                value={newCollectionName}
                onChange={(e) => setNewCollectionName(e.target.value)}
                placeholder="e.g., Fashion Deals, Home Discounts, etc."
                className="h-12"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Description (optional)</label>
              <Input 
                value={newCollectionDescription}
                onChange={(e) => setNewCollectionDescription(e.target.value)}
                placeholder="What kind of coupons will be in this collection?"
                className="h-12"
              />
            </div>
          </div>
          
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)} className="h-11 w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={handleCreateCollection} className="h-11 w-full sm:w-auto">
              Create Collection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Collection Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md max-w-[95vw] rounded-lg">
          <DialogHeader>
            <DialogTitle>Delete Collection</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this collection?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="deleteCoupons"
                checked={shouldDeleteCoupons}
                onChange={(e) => setShouldDeleteCoupons(e.target.checked)}
                className="rounded border-gray-300 h-5 w-5"
              />
              <label htmlFor="deleteCoupons" className="text-sm font-medium">
                Also delete all coupons in this collection
              </label>
            </div>
            
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded border border-gray-100 dark:border-gray-700">
              {shouldDeleteCoupons 
                ? "All saved coupons in this collection will be permanently deleted."
                : "Saved coupons will be kept but removed from this collection."}
            </p>
          </div>
          
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} className="h-11 w-full sm:w-auto">
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteCollection} className="h-11 w-full sm:w-auto">
              Delete Collection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default SavedCouponsPage; 