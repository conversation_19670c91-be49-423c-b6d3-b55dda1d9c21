import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Copy, Star, Lock, ExternalLink, Bookmark } from "lucide-react";
import React, { useState, useEffect, Component, ReactNode } from "react";
import { toast } from "sonner";
import { useAnalytics } from "@/hooks/useAnalytics";
import SaveCouponButton from "@/components/SaveCouponButton";
import { useAuth } from "@/context/AuthContext";
import { useIsCouponSaved } from "@/hooks/useSavedCoupons";
import { useNavigate, Link } from "react-router-dom";
import PremiumCouponPayment from "@/components/PremiumCouponPayment";
import { fixDomainName } from "@/utils/domainFix";
import BrandLogo from '@/components/BrandLogo';

interface CouponCardProps {
  id: string;
  brandName: string;
  brandLogo: string;
  brandWebsite?: string;
  influencerName: string;
  influencerImage?: string;
  discountAmount: string;
  expirationTime: string;
  couponCode: string;
  category: string;
  featured?: boolean;
  isPremium?: boolean;
  isLocked?: boolean;
  price?: number;
  isSaved?: boolean;
  brandId?: string;
}

const CouponCard = ({
  id,
  brandName,
  brandLogo,
  brandWebsite,
  influencerName,
  influencerImage,
  discountAmount,
  expirationTime,
  couponCode,
  category,
  featured = false,
  isPremium = false,
  isLocked = false,
  price = 0,
  isSaved = false,
  brandId = ""
}: CouponCardProps) => {
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);
  const { trackCouponView, trackCouponCopy } = useAnalytics();
  const { user } = useAuth();
  const { data: isActuallySaved, isError: saveStatusError } = useIsCouponSaved(user?.id, id);
  
  // Use either the prop or the query result
  const couponIsSaved = isSaved || (isActuallySaved && !saveStatusError);
  
  useEffect(() => {
    // Track coupon view when component mounts
    try {
      // Normal tracking (not debug mode)
      trackCouponView(id);
    } catch (error) {
      console.error("Failed to track coupon view:", error);
      // Don't break the component for analytics errors
    }
  }, [id, trackCouponView]);
  
  const handleCopyCode = () => {
    if (isLocked) {
      toast.error("Premium coupon code is locked. Please purchase to unlock.");
      return;
    }
    
    try {
      navigator.clipboard.writeText(couponCode);
      setCopied(true);
      trackCouponCopy(id);
      toast.success("Coupon code copied to clipboard!");
      
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (error) {
      console.error("Failed to copy: ", error);
      toast.error("Failed to copy code. Please try again.");
    }
  };
  
  // Format the price if applicable
  const formattedPrice = price ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price) : null;

  // Error handler for save button
  const handleSaveError = (error: any) => {
    console.error("Error with save button:", error);
    toast.error("There was an issue saving this coupon. Please try again.");
  };

  // Function to visit the store/brand page within the application
  const handleVisitStore = () => {
    try {
      // Track the store visit (if analytics are available)
      try {
        trackCouponCopy(id); // Using copy tracking as a proxy for store visits
      } catch (error) {
        console.error("Failed to track store visit:", error);
      }
      
      // If we have a specific brandId, navigate directly to that brand page
      if (brandId) {
        // Ensure we're using the correct domain before navigating
        const brandPath = `/brands/${brandId}`;
        navigate(brandPath);
        toast.success(`Viewing coupons from ${brandName}`);
      } 
      // Otherwise try to find the brand by name
      else {
        // Use the search system to find the brand
        const searchPath = `/brands?name=${encodeURIComponent(brandName)}`;
        navigate(searchPath);
        // The Brands page will handle the redirect to the specific brand if found
      }
    } catch (error) {
      console.error("Failed to navigate to brand page:", error);
      toast.error("Failed to open brand page. Please try again.");
    }
  };
  
  // Function to render the coupon code with appropriate masking for premium coupons
  const renderCouponCode = () => {
    if (isLocked) {
      // Return a masked placeholder for premium locked coupons
      return "••••••••";
    }
    
    return couponCode;
  };
  
  return (
    <div className={`coupon-card relative border-[3px] rounded-xl overflow-hidden h-full bg-gray-50/50 shadow-md hover:shadow-xl transition-all duration-300 ${featured ? 'border-brand-yellow bg-yellow-50/30 shadow-yellow-200/50' : 'border-gray-400 hover:border-gray-600'}`}>
      {/* Badges */}
      <div className="absolute top-2 right-2 z-10 flex gap-1">
        {featured && (
          <Badge className="bg-brand-yellow text-black font-medium text-xs px-1.5 py-0.5">
            Featured
          </Badge>
        )}
        {isPremium && (
          <Badge className="bg-amber-500 text-white font-medium text-xs px-1.5 py-0.5">
            Premium
          </Badge>
        )}
      </div>
      
      <div className="p-4 relative h-full flex flex-col">
        {/* Brand Info - Make clickable to go to store */}
        <div className="flex items-center justify-between mb-3">
          <div 
            className="flex items-center gap-2 overflow-hidden cursor-pointer group"
            onClick={handleVisitStore}
            title={`View more from ${brandName}`}
          >
            <BrandLogo
              brandName={brandName}
              logoUrl={brandLogo}
              website={brandWebsite}
              size="md"
              className="border border-transparent group-hover:border-blue-300 transition-colors"
            />
            <div className="overflow-hidden">
              <h3 className="font-medium text-base truncate group-hover:text-blue-600 transition-colors">{brandName}</h3>
              <p className="text-xs text-gray-500 truncate">{category}</p>
            </div>
          </div>
        </div>
        
        {/* Discount Info */}
        <div className="mb-2">
          <h2 className="text-lg font-bold text-brand-blue truncate">{discountAmount}</h2>
        </div>
        
        {/* Influencer Info - Hide on small mobile */}
        <div className="flex items-center gap-2 mb-2 overflow-hidden hidden sm:flex">
          {influencerImage ? (
            <div className="w-5 h-5 rounded-full overflow-hidden flex-shrink-0">
              <img 
                src={influencerImage} 
                alt={influencerName} 
                className="w-full h-full object-cover" 
                onError={(e) => {
                  // Handle image load error
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          ) : (
            <div className="w-5 h-5 rounded-full bg-brand-pink-100 flex-shrink-0 flex items-center justify-center">
              <span className="text-xs font-medium text-brand-pink">
                {influencerName.charAt(0)}
              </span>
            </div>
          )}
          <p className="text-xs text-gray-600 truncate overflow-hidden">by <span className="font-medium">{influencerName}</span></p>
        </div>
        
        {/* Expiration Info */}
        <div className="flex items-center text-xs text-gray-500 mb-2">
          <Clock className="w-3 h-3 mr-1 text-brand-pink-400 flex-shrink-0" />
          <span className="truncate">Expires: {expirationTime}</span>
        </div>
        
        {/* Coupon Code Area - Shows code with copy button or purchase overlay */}
        <div className="flex items-center gap-2 mt-auto relative mb-1">
          <div className={`flex-1 bg-gray-50 rounded-lg px-2 py-1.5 text-gray-800 font-mono font-medium flex-grow text-center overflow-hidden ${isLocked ? 'bg-gray-100' : ''}`}>
            <span className="truncate block text-sm">
              {isLocked ? (
                <span className="select-none text-gray-400">••••••••</span>
              ) : (
                couponCode
              )}
            </span>
          </div>
          
          {/* Premium button for locked coupons */}
          {isLocked && (
            <div className="w-full absolute inset-0 z-50">
              <PremiumCouponPayment 
                couponId={id} 
                price={price} 
                title={`${discountAmount} ${brandName} Coupon`}
              />
            </div>
          )}
          
          {!isLocked && (
            <Button 
              variant="outline" 
              size="icon" 
              className={`h-8 w-8 ${copied ? 'bg-green-100 border-green-300 text-green-600' : 'border-gray-200'} flex-shrink-0`}
              onClick={handleCopyCode}
              title="Copy code"
            >
              <Copy className="w-3 h-3" />
            </Button>
          )}
        </div>
        
        {/* Price indicator for locked coupons */}
        {isLocked && formattedPrice && (
          <div className="mt-2 text-center">
            <span className="text-xs font-medium text-brand-pink">{formattedPrice} to unlock</span>
          </div>
        )}
        
        {/* Hover overlay with full actions (only for unlocked coupons) */}
        {!isLocked && (
          <div className="absolute inset-0 bg-black/70 opacity-0 hover:opacity-100 transition-opacity duration-200 flex flex-col justify-center items-center p-3 gap-3 rounded-lg">
            {/* Save button - Highlighted at the top */}
            <SaveCouponButton
              couponId={id}
              variant={couponIsSaved ? "default" : "default"}
              size="default"
              className={`w-full ${couponIsSaved ? 'bg-blue-500 hover:bg-blue-600' : 'bg-pink-500 hover:bg-pink-600'}`}
              showText={true}
            />
            
            {/* Copy button */}
            <Button 
              className="bg-green-500 hover:bg-green-600 shadow-md w-full"
              onClick={handleCopyCode}
            >
              <Copy className="mr-2 h-4 w-4" />
              {copied ? "Copied!" : "Copy Code"}
            </Button>
            
            {/* Visit store button - Changed text to make it clearer */}
            <Button 
              variant="outline" 
              className="bg-white w-full text-gray-800 hover:bg-gray-100"
              onClick={handleVisitStore}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View Brand
            </Button>
          </div>
        )}
        
        {/* Small saved indicator if coupon is saved (visible even without hover) */}
        {couponIsSaved && (
          <div className="absolute top-2 left-2 z-10">
            <div className="bg-blue-500 text-white rounded-full p-1 shadow-md">
              <Bookmark className="h-3 w-3 fill-white" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Simple error boundary to prevent crashes
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class CustomErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  state: ErrorBoundaryState = { hasError: false, error: null };
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error("SaveCouponButton error:", error, errorInfo);
  }
  
  render(): ReactNode {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    
    return this.props.children;
  }
}

export default CouponCard;