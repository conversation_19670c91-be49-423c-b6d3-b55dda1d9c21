-- Comprehensive RLS Performance Optimization
-- Replace auth.uid() with (select auth.uid()) to prevent re-evaluation for each row
-- This migration fixes performance issues across multiple tables

-- 1. INFLUENCER_ANALYTICS TABLE
-- Drop the existing policy
DROP POLICY IF EXISTS "Influencers can view their own analytics" ON public.influencer_analytics;

-- <PERSON>reate optimized policy with subquery to prevent re-evaluation
CREATE POLICY "Influencers can view their own analytics"
  ON public.influencer_analytics
  FOR SELECT
  USING ((select auth.uid())::uuid = influencer_id);

-- 2. FOLLOWS TABLE
-- Drop and recreate policies with optimization
DROP POLICY IF EXISTS "Users can manage their own follows" ON public.follows;
DROP POLICY IF EXISTS "Users can view their own follows" ON public.follows;
DROP POLICY IF EXISTS "Users can create follows" ON public.follows;
DROP POLICY IF EXISTS "Users can delete follows" ON public.follows;

-- <PERSON>reate optimized policies for follows table
CREATE POLICY "Users can manage their own follows"
  ON public.follows
  FOR ALL
  USING ((select auth.uid()) = user_id)
  WITH CHECK ((select auth.uid()) = user_id);

-- 3. BRANDS TABLE
-- Drop and recreate policies with optimization
DROP POLICY IF EXISTS "Allow authenticated users to update their own brands" ON public.brands;
DROP POLICY IF EXISTS "Users can update their own brands" ON public.brands;
DROP POLICY IF EXISTS "Users can view their own brands" ON public.brands;

-- Create optimized policies for brands table
CREATE POLICY "Allow authenticated users to update their own brands"
  ON public.brands
  FOR UPDATE
  TO authenticated
  USING ((select auth.uid()) = created_by)
  WITH CHECK ((select auth.uid()) = created_by);

-- 4. COUPON_INTERACTIONS TABLE
-- Drop and recreate the policy with optimization
DROP POLICY IF EXISTS "Allow users to see relevant coupon interactions" ON public.coupon_interactions;

CREATE POLICY "Allow users to see relevant coupon interactions"
  ON public.coupon_interactions
  FOR SELECT
  TO authenticated
  USING (
    (user_id = (select auth.uid())) OR
    EXISTS (
      SELECT 1 FROM coupons
      WHERE coupons.id = coupon_interactions.coupon_id
      AND coupons.influencer_id = (select auth.uid())
    )
  );

-- 5. NOTIFICATIONS TABLE
-- Drop and recreate policies with optimization
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update read status of their own notifications" ON public.notifications;

CREATE POLICY "Users can view their own notifications"
  ON public.notifications
  FOR SELECT
  USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can update read status of their own notifications"
  ON public.notifications
  FOR UPDATE
  USING ((select auth.uid()) = user_id)
  WITH CHECK ((select auth.uid()) = user_id);

-- 6. USER_WALLETS TABLE
-- Drop and recreate policy with optimization
DROP POLICY IF EXISTS "Users can view their own wallet" ON public.user_wallets;

CREATE POLICY "Users can view their own wallet"
  ON public.user_wallets
  FOR SELECT
  USING ((select auth.uid()) = user_id);

-- 7. WITHDRAWAL_REQUESTS TABLE
-- Drop and recreate policies with optimization
DROP POLICY IF EXISTS "Users can view their own withdrawal requests" ON public.withdrawal_requests;
DROP POLICY IF EXISTS "Users can create withdrawal requests" ON public.withdrawal_requests;

CREATE POLICY "Users can view their own withdrawal requests"
  ON public.withdrawal_requests
  FOR SELECT
  USING ((select auth.uid()) = user_id);

CREATE POLICY "Users can create withdrawal requests"
  ON public.withdrawal_requests
  FOR INSERT
  WITH CHECK ((select auth.uid()) = user_id);

-- 8. PROFILE_SHARES TABLE
-- Drop and recreate policy with optimization
DROP POLICY IF EXISTS "Allow users to see relevant profile shares" ON public.profile_shares;

CREATE POLICY "Allow users to see relevant profile shares"
  ON public.profile_shares
  FOR SELECT
  TO authenticated
  USING (
    (user_id = (select auth.uid())) OR
    (profile_id = (select auth.uid()))
  );

-- 9. PROFILE_VIEWS TABLE
-- Drop and recreate policy with optimization
DROP POLICY IF EXISTS "Allow users to see views of their profile" ON public.profile_views;

CREATE POLICY "Allow users to see views of their profile"
  ON public.profile_views
  FOR SELECT
  TO authenticated
  USING (
    (viewer_id = (select auth.uid())) OR
    (profile_id = (select auth.uid()))
  );

-- 10. COLLECTIONS TABLE (if it exists)
-- Drop and recreate policies with optimization
DROP POLICY IF EXISTS "Users can view their own collections" ON public.collections;
DROP POLICY IF EXISTS "Users can create their own collections" ON public.collections;
DROP POLICY IF EXISTS "Users can update their own collections" ON public.collections;
DROP POLICY IF EXISTS "Users can delete their own collections" ON public.collections;

-- Only create policies if the collections table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'collections') THEN
    CREATE POLICY "Users can view their own collections"
      ON public.collections FOR SELECT
      USING ((select auth.uid()) = user_id);

    CREATE POLICY "Users can create their own collections"
      ON public.collections FOR INSERT
      WITH CHECK ((select auth.uid()) = user_id);

    CREATE POLICY "Users can update their own collections"
      ON public.collections FOR UPDATE
      USING ((select auth.uid()) = user_id);

    CREATE POLICY "Users can delete their own collections"
      ON public.collections FOR DELETE
      USING ((select auth.uid()) = user_id);
  END IF;
END $$;

-- 9. PREMIUM_PURCHASES TABLE (if it exists)
-- Drop and recreate policies with optimization
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'premium_purchases') THEN
    DROP POLICY IF EXISTS "Users can view their own premium purchases" ON public.premium_purchases;
    DROP POLICY IF EXISTS "Sellers can view purchases of their coupons" ON public.premium_purchases;

    CREATE POLICY "Users can view their own premium purchases"
      ON public.premium_purchases
      FOR SELECT
      USING ((select auth.uid()) = buyer_id);

    CREATE POLICY "Sellers can view purchases of their coupons"
      ON public.premium_purchases
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM coupons
          WHERE coupons.id = premium_purchases.coupon_id
          AND coupons.influencer_id = (select auth.uid())
        )
      );
  END IF;
END $$;

-- 10. TRANSACTIONS TABLE (if it exists)
-- Drop and recreate policies with optimization
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'transactions') THEN
    DROP POLICY IF EXISTS "Users can view their own transactions" ON public.transactions;

    CREATE POLICY "Users can view their own transactions"
      ON public.transactions
      FOR SELECT
      USING (
        ((select auth.uid()) = buyer_id) OR
        ((select auth.uid()) = seller_id)
      );
  END IF;
END $$;

-- 11. SAVED_COUPONS TABLE (if it exists)
-- Drop and recreate policies with optimization
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'saved_coupons') THEN
    DROP POLICY IF EXISTS "Users can view their own saved coupons" ON public.saved_coupons;
    DROP POLICY IF EXISTS "Users can manage their own saved coupons" ON public.saved_coupons;

    CREATE POLICY "Users can view their own saved coupons"
      ON public.saved_coupons
      FOR SELECT
      USING ((select auth.uid()) = user_id);

    CREATE POLICY "Users can manage their own saved coupons"
      ON public.saved_coupons
      FOR ALL
      USING ((select auth.uid()) = user_id)
      WITH CHECK ((select auth.uid()) = user_id);
  END IF;
END $$;
