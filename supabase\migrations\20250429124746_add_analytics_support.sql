-- Create table for daily analytics aggregation
CREATE TABLE IF NOT EXISTS public.daily_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  total_views INTEGER DEFAULT 0,
  total_signups INTEGER DEFAULT 0,
  total_coupons_added INTEGER DEFAULT 0,
  total_coupons_used INTEGER DEFAULT 0,
  total_revenue NUMERIC DEFAULT 0,
  total_transactions INTEGER DEFAULT 0,
  new_premium_users INTEGER DEFAULT 0,
  coupons_by_category JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  CONSTRAINT daily_analytics_date_unique UNIQUE (date)
);

-- Create table for influencer analytics
CREATE TABLE IF NOT EXISTS public.influencer_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  influencer_id UUID NOT NULL,
  date DATE NOT NULL,
  profile_views INTEGER DEFAULT 0,
  coupon_views INTEGER DEFAULT 0,
  coupon_copies INTEGER DEFAULT 0,
  coupon_uses INTEGER DEFAULT 0,
  total_earnings NUMERIC DEFAULT 0,
  new_followers INTEGER DEFAULT 0,
  conversion_rate NUMERIC DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  CONSTRAINT influencer_analytics_influencer_date_unique UNIQUE (influencer_id, date),
  CONSTRAINT influencer_analytics_influencer_id_fkey FOREIGN KEY (influencer_id) 
    REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS influencer_analytics_influencer_id_idx ON public.influencer_analytics(influencer_id);
CREATE INDEX IF NOT EXISTS influencer_analytics_date_idx ON public.influencer_analytics(date);

-- Add RLS to influencer analytics
ALTER TABLE public.influencer_analytics ENABLE ROW LEVEL SECURITY;

-- Influencers can view their own analytics
CREATE POLICY "Influencers can view their own analytics"
  ON public.influencer_analytics
  FOR SELECT
  USING (auth.uid()::uuid = influencer_id);

-- Create function to calculate daily analytics
CREATE OR REPLACE FUNCTION public.generate_daily_analytics(target_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day')
RETURNS void AS $$
DECLARE
  category_stats JSONB;
BEGIN
  -- Calculate category stats for the day
  WITH category_counts AS (
    SELECT 
      c.category_id,
      cat.name as category_name,
      COUNT(*) as count,
      SUM(ci.interaction_type = 'view')::INTEGER as views,
      SUM(ci.interaction_type = 'copy')::INTEGER as copies,
      SUM(ci.interaction_type = 'use')::INTEGER as uses
    FROM public.coupon_interactions ci
    JOIN public.coupons c ON ci.coupon_id = c.id
    JOIN public.categories cat ON c.category_id = cat.id
    WHERE DATE(ci.occurred_at) = target_date
    GROUP BY c.category_id, cat.name
  )
  SELECT 
    jsonb_object_agg(
      category_name, 
      jsonb_build_object(
        'count', count,
        'views', views,
        'copies', copies,
        'uses', uses
      )
    ) INTO category_stats
  FROM category_counts;

  -- Insert or update daily analytics
  INSERT INTO public.daily_analytics (
    date,
    total_views,
    total_signups,
    total_coupons_added,
    total_coupons_used,
    total_revenue,
    total_transactions,
    new_premium_users,
    coupons_by_category
  )
  VALUES (
    target_date,
    (SELECT COUNT(*) FROM public.coupon_interactions WHERE interaction_type = 'view' AND DATE(occurred_at) = target_date),
    (SELECT COUNT(*) FROM auth.users WHERE DATE(created_at) = target_date),
    (SELECT COUNT(*) FROM public.coupons WHERE DATE(created_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions WHERE interaction_type = 'use' AND DATE(occurred_at) = target_date),
    (SELECT COALESCE(SUM(amount), 0) FROM public.transactions WHERE DATE(created_at) = target_date AND status = 'completed'),
    (SELECT COUNT(*) FROM public.transactions WHERE DATE(created_at) = target_date AND status = 'completed'),
    (SELECT COUNT(*) FROM public.premium_purchases WHERE DATE(purchased_at) = target_date),
    COALESCE(category_stats, '{}'::jsonb)
  )
  ON CONFLICT (date) 
  DO UPDATE SET
    total_views = EXCLUDED.total_views,
    total_signups = EXCLUDED.total_signups,
    total_coupons_added = EXCLUDED.total_coupons_added,
    total_coupons_used = EXCLUDED.total_coupons_used,
    total_revenue = EXCLUDED.total_revenue,
    total_transactions = EXCLUDED.total_transactions,
    new_premium_users = EXCLUDED.new_premium_users,
    coupons_by_category = EXCLUDED.coupons_by_category,
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update influencer analytics
CREATE OR REPLACE FUNCTION public.update_influencer_analytics(target_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day')
RETURNS void AS $$
BEGIN
  -- Get all influencers with activity on the target date
  WITH active_influencers AS (
    SELECT DISTINCT p.id as influencer_id
    FROM public.profiles p
    WHERE p.role = 'influencer'
    AND EXISTS (
      SELECT 1 FROM public.coupons c
      WHERE c.influencer_id = p.id
    )
  )
  
  -- Insert or update analytics for each active influencer
  INSERT INTO public.influencer_analytics (
    influencer_id,
    date,
    profile_views,
    coupon_views,
    coupon_copies,
    coupon_uses,
    total_earnings,
    new_followers,
    conversion_rate
  )
  SELECT
    ai.influencer_id,
    target_date,
    (SELECT COUNT(*) FROM public.profile_views pv WHERE pv.profile_id = ai.influencer_id AND DATE(pv.viewed_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'copy' AND DATE(ci.occurred_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'use' AND DATE(ci.occurred_at) = target_date),
    (SELECT COALESCE(SUM(t.seller_amount), 0) FROM public.transactions t 
     WHERE t.seller_id = ai.influencer_id AND DATE(t.created_at) = target_date AND t.status = 'completed'),
    (SELECT COUNT(*) FROM public.follows f WHERE f.influencer_id = ai.influencer_id AND DATE(f.created_at) = target_date),
    CASE 
      WHEN (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
            WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date) > 0
      THEN 
        (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
         WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'use' AND DATE(ci.occurred_at) = target_date)::NUMERIC /
        (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id 
         WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date)
      ELSE 0
    END
  FROM active_influencers ai
  ON CONFLICT (influencer_id, date) 
  DO UPDATE SET
    profile_views = EXCLUDED.profile_views,
    coupon_views = EXCLUDED.coupon_views,
    coupon_copies = EXCLUDED.coupon_copies,
    coupon_uses = EXCLUDED.coupon_uses,
    total_earnings = EXCLUDED.total_earnings,
    new_followers = EXCLUDED.new_followers,
    conversion_rate = EXCLUDED.conversion_rate,
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 