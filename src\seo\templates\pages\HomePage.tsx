import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import HeroSection from '@/components/homepage-sections/HeroSection';
import FeaturedBrands from '@/components/homepage-sections/FeaturedBrands';
import TrendingDealsSection from '@/components/homepage-sections/TrendingDealsSection';
import PremiumOffers from '@/components/homepage-sections/PremiumOffers';
import CategoriesSection from '@/components/homepage-sections/CategoriesSection';
import PageContainer from '@/components/layout/PageContainer';
import SEO from '@/seo/components/SEO';
import InfluencerSEO from '@/seo/components/InfluencerSEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import OrganizationSchema from '@/seo/schemas/OrganizationSchema';
import SearchResultSchema from '@/seo/schemas/SearchResultSchema';

/**
 * Homepage template with proper SEO implementation
 * This shows how to use SEO components for best search visibility
 * and rich search results like the Perplexity example
 */
const HomePageTemplate = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  
  // Handle search submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };
  
  // Create sections for search results schema
  const searchSections = [
    {
      name: 'Featured Brands',
      description: 'Top brands with active coupon codes and promotional offers',
      url: 'https://www.couponlink.in/#featured-brands',
      image: '/logos/featured-brands.png'
    },
    {
      name: 'Trending Deals',
      description: 'Most popular and recently verified coupon codes',
      url: 'https://www.couponlink.in/#trending-deals',
      image: '/logos/trending-deals.png'
    },
    {
      name: 'Categories',
      description: 'Browse coupons by shopping category',
      url: 'https://www.couponlink.in/#categories',
      image: '/logos/categories.png'
    },
    {
      name: 'Latest Coupons',
      description: 'Recently added and verified coupon codes',
      url: 'https://www.couponlink.in/#latest-coupons',
      image: '/logos/latest-coupons.png'
    },
    {
      name: 'Popular Stores',
      description: 'Most visited stores with active promotions',
      url: 'https://www.couponlink.in/#popular-stores',
      image: '/logos/popular-stores.png'
    }
  ];
  
  return (
    <>
      {/* Enhanced SEO components */}
      <SEO
        title="CouponLink - Find & Share Verified Coupon Codes and Promo Offers | Link in Bio Deals"
        description="Discover the best coupon codes, promo offers, and discount deals from top brands. Perfect for link in bio, influencer codes, and social media deals. Save money with verified promotional codes updated daily at CouponLink."
        pageType="homepage"
        enhancedKeywords={true}
        type="website"
        modifiedTime={new Date().toISOString()}
      />

      {/* Influencer and social media specific SEO */}
      <InfluencerSEO
        title="Best Coupon Codes for Link in Bio | Influencer Deals"
        description="Exclusive coupon codes perfect for influencers, creators, and social media link in bio. Get verified discount codes for your followers."
        dealType="exclusive"
        discountAmount="Up to 70% off"
      />

      {/* Website schema */}
      <WebsiteSchema />
      
      {/* Organization schema with logo */}
      <OrganizationSchema />
      
      {/* Search result schema for multiple sections */}
      <SearchResultSchema
        mainEntity={{
          name: 'CouponLink - Verified Coupon Codes & Promo Offers',
          description: 'Find and share verified coupon codes, promo offers, and discounts from your favorite brands.',
          url: 'https://www.couponlink.in'
        }}
        sections={searchSections}
      />
      
      <MainLayout fullWidth>
        <PageContainer fullWidth decorationType="landing" decorationOpacity={0.8}>
          {/* Hero Section with Search */}
          <HeroSection 
            searchQuery={searchQuery} 
            setSearchQuery={setSearchQuery} 
            handleSearch={handleSearch}
          />
          
          {/* Featured Brands Grid */}
          <FeaturedBrands limit={6} />
          
          {/* Trending Deals Carousel */}
          <TrendingDealsSection />
          
          {/* Popular Categories Grid */}
          <CategoriesSection />
          
          {/* Premium Offers with CTA */}
          <PremiumOffers />
        </PageContainer>
      </MainLayout>
    </>
  );
};

export default HomePageTemplate; 