import { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from '@/context/AuthContext';
import { useAnalytics } from '@/hooks/useAnalytics';
import { Share2, Copy, Check, Twitter, Facebook, Linkedin, Mail, Phone, DollarSign } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { openUrlWithCorrectDomain } from '@/utils/domainFix';
import { FaGlobe } from 'react-icons/fa';

// Import local components
import UserAvatar from './UserAvatar';
import UserProfileSocial from './UserProfileSocial';
import PaymentDialog from './PaymentDialog';

// Format website URL for display (removing http/https and www)
const formatWebsiteUrl = (url: string): string => {
  return url.replace(/(https?:\/\/)?(www\.)?/i, '').replace(/\/$/, '');
};

// Extended user type with all required properties
interface ExtendedUser {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  bio?: string;
  payment_id?: string;
  payment_type?: string;
  social_links?: Array<{ platform: string; url: string }>;
  username?: string;
  website?: string;
  [key: string]: any;
}

const UserProfileHeader = ({ user, isLoading }: { user: ExtendedUser; isLoading?: boolean }) => {
  const { user: currentUser } = useAuth();
  const { trackProfileView, trackProfileShare } = useAnalytics();
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);
  const [profileUrl, setProfileUrl] = useState('');
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  // Check if current user is viewing their own profile
  const isCurrentUser = currentUser?.id === user?.id;

  useEffect(() => {
    if (user?.id) {
      trackProfileView(user.id, document.referrer);
    }
  }, [user?.id, trackProfileView]);

  useEffect(() => {
    if (user?.username) {
      // Create the absolute URL including origin
      const url = `${window.location.origin}/${encodeURIComponent(user.username)}`;
      setProfileUrl(url);
    }
  }, [user?.username]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      setCopied(true);
      toast.success('Link copied to clipboard!');
      trackShare('copy');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  // Helper function to track shares in a consistent way
  const trackShare = (platform: string) => {
    try {
      if (user?.username) {
        trackProfileShare(user.username, platform);
      }
    } catch (error) {
      console.error('Error tracking share:', error);
    }
  };

  const shareProfile = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out ${user?.username}'s coupons`,
          text: `Check out all the coupon codes from ${user?.username}`,
          url: profileUrl,
        });
        trackShare('system');
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      copyToClipboard();
    }
  };

  const handleShareTwitter = () => {
    const text = encodeURIComponent(`Check out ${user.name || user.username}'s profile on CouponLink!`);
    const url = encodeURIComponent(profileUrl);
    openUrlWithCorrectDomain(`https://twitter.com/intent/tweet?text=${text}&url=${url}`);
  };
  
  const handleShareFacebook = () => {
    const url = encodeURIComponent(profileUrl);
    openUrlWithCorrectDomain(`https://www.facebook.com/sharer/sharer.php?u=${url}`);
  };
  
  const handleShareLinkedin = () => {
    const text = encodeURIComponent(`Check out ${user.name || user.username}'s profile on CouponLink!`);
    const url = encodeURIComponent(profileUrl);
    openUrlWithCorrectDomain(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&summary=${text}`);
  };
  
  const handleShareEmail = () => {
    const subject = encodeURIComponent(`Check out this CouponLink profile: ${user.name || user.username}`);
    const body = encodeURIComponent(`I thought you might be interested in this profile on CouponLink:\n\n${profileUrl}`);
    openUrlWithCorrectDomain(`mailto:?subject=${subject}&body=${body}`);
  };

  // Check for payment info
  const hasPaymentInfo = user && 'payment_id' in user && user.payment_id;

  const copyPaymentInfo = () => {
    if (!user?.payment_id) return;
    
    navigator.clipboard.writeText(user.payment_id);
    toast.success('Payment ID copied to clipboard!');
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        {/* Mobile loading state - Twitter style */}
        <div className="lg:hidden">
          <div className="flex gap-4 mb-6">
            <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gray-200 flex-shrink-0" />
            <div className="flex-1">
              <div className="h-6 sm:h-8 bg-gray-200 rounded w-32 sm:w-48 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-24 sm:w-32" />
            </div>
          </div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4" />
          <div className="h-4 bg-gray-200 rounded w-1/2" />
        </div>
        
        {/* Desktop loading state */}
        <div className="hidden lg:block">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-24 h-24 rounded-full bg-gray-200" />
            <div className="flex-1">
              <div className="h-8 bg-gray-200 rounded w-48 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-32" />
            </div>
          </div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4" />
          <div className="h-4 bg-gray-200 rounded w-1/2" />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
      {/* Mobile view - Twitter style layout */}
      <div className="lg:hidden">
        <div className="flex items-start gap-3 sm:gap-4 mb-4">
          <div className="flex-shrink-0">
            <div className="w-16 h-16 sm:w-20 sm:h-20 overflow-hidden rounded-full border border-gray-200">
              <UserAvatar
                avatarUrl={user?.avatar_url}
                fullName={user?.name || ''}
              />
            </div>
          </div>
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold mb-0.5">{user?.name}</h1>
            <p className="text-sm sm:text-base text-gray-600 mb-2">@{user?.username}</p>
            
            {user?.bio && (
              <p className="text-sm sm:text-base text-gray-800 mt-2 mb-3">{user.bio}</p>
            )}

            <UserProfileSocial
              socialLinks={user?.social_links}
              website={user?.website}
              variant="icons"
              isCurrentUser={isCurrentUser}
              navigate={navigate}
            />

            <div className="flex flex-wrap gap-2 mt-4">
              {user?.website && (
                <Button size="sm" variant="outline" onClick={() => openUrlWithCorrectDomain(user.website)} className="text-xs sm:text-sm h-8">
                  <FaGlobe className="mr-1.5 h-3 w-3" />
                  Website
                </Button>
              )}

              {isCurrentUser && (
                <>
                  <Button size="sm" variant="outline" onClick={() => navigate('/settings')} className="text-xs sm:text-sm h-8">
                    Edit Profile
                  </Button>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button size="sm" variant="outline" className="text-xs sm:text-sm h-8 flex items-center gap-1">
                        <Share2 className="h-3 w-3" />
                        Share Profile
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent 
                      className="w-[92vw] max-w-[330px] p-4 border shadow-lg rounded-xl" 
                      align="center" 
                      side="top"
                      sideOffset={10}
                      avoidCollisions
                    >
                      <div className="space-y-4">
                        <div className="flex space-x-2">
                          <Input 
                            value={profileUrl}
                            readOnly
                            className="flex-1"
                          />
                          <Button
                            onClick={copyToClipboard}
                            variant="outline"
                            size="icon"
                            className="shrink-0"
                          >
                            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <Button onClick={handleShareTwitter} variant="outline" size="sm" className="w-full">
                            <Twitter className="h-4 w-4 mr-2" />
                            Twitter
                          </Button>
                          <Button onClick={handleShareFacebook} variant="outline" size="sm" className="w-full">
                            <Facebook className="h-4 w-4 mr-2" />
                            Facebook
                          </Button>
                          <Button onClick={handleShareLinkedin} variant="outline" size="sm" className="w-full">
                            <Linkedin className="h-4 w-4 mr-2" />
                            LinkedIn
                          </Button>
                          <Button onClick={handleShareEmail} variant="outline" size="sm" className="w-full">
                            <Mail className="h-4 w-4 mr-2" />
                            Email
                          </Button>
                        </div>
                        
                        <Button onClick={shareProfile} className="w-full">
                          <Share2 className="h-4 w-4 mr-2" />
                          Share Profile
                        </Button>
                        
                        <Button 
                          variant="outline" 
                          className="w-full"
                          onClick={() => navigate(`/${user?.username}/share`)}
                        >
                          Advanced Sharing Options
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden lg:block">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-shrink-0">
            <UserAvatar
              avatarUrl={user?.avatar_url}
              fullName={user?.name || ''}
            />
          </div>
          <div className="flex-grow">
            <h1 className="text-3xl font-bold mb-1">{user?.name}</h1>
            <p className="text-gray-600 mb-4">@{user?.username}</p>
            
            {user?.bio && (
              <p className="text-gray-800 mb-6">{user.bio}</p>
            )}

            <UserProfileSocial
              socialLinks={user?.social_links}
              website={user?.website}
              variant="icons"
              isCurrentUser={isCurrentUser}
              navigate={navigate}
            />

            <div className="flex flex-wrap gap-3 mt-6">
              {user?.website && (
                <Button variant="outline" onClick={() => openUrlWithCorrectDomain(user.website)}>
                  <FaGlobe className="mr-2 h-4 w-4" />
                  {formatWebsiteUrl(user.website)}
                </Button>
              )}

              {isCurrentUser && (
                <>
                  <Button variant="outline" onClick={() => navigate('/settings')}>
                    Edit Profile
                  </Button>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="flex items-center gap-2">
                        <Share2 className="h-4 w-4" />
                        Share Profile
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent 
                      className="w-96 p-4 border shadow-lg rounded-xl" 
                      align="start" 
                      side="top"
                      sideOffset={10}
                      avoidCollisions
                    >
                      <div className="space-y-4">
                        <div className="flex space-x-2">
                          <Input 
                            value={profileUrl}
                            readOnly
                            className="flex-1"
                          />
                          <Button
                            onClick={copyToClipboard}
                            variant="outline"
                            size="icon"
                            className="shrink-0"
                          >
                            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <Button onClick={handleShareTwitter} variant="outline" className="w-full">
                            <Twitter className="h-4 w-4 mr-2" />
                            Twitter
                          </Button>
                          <Button onClick={handleShareFacebook} variant="outline" className="w-full">
                            <Facebook className="h-4 w-4 mr-2" />
                            Facebook
                          </Button>
                          <Button onClick={handleShareLinkedin} variant="outline" className="w-full">
                            <Linkedin className="h-4 w-4 mr-2" />
                            LinkedIn
                          </Button>
                          <Button onClick={handleShareEmail} variant="outline" className="w-full">
                            <Mail className="h-4 w-4 mr-2" />
                            Email
                          </Button>
                        </div>
                        
                        <Button onClick={shareProfile} className="w-full">
                          <Share2 className="h-4 w-4 mr-2" />
                          Share Profile
                        </Button>
                        
                        <Button 
                          variant="outline" 
                          className="w-full"
                          onClick={() => navigate(`/${user?.username}/share`)}
                        >
                          Advanced Sharing Options
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </>
              )}
            </div>

            {/* Contact options */}
            <div className="flex gap-2 justify-center md:justify-start mt-4 flex-wrap">
              {user?.email && (
                <Button 
                  size="sm" 
                  variant="outline"
                  className="flex items-center gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                  asChild
                >
                  <a href={`mailto:${user.email}`}>
                    <Mail className="h-3.5 w-3.5" />
                    <span>Email</span>
                  </a>
                </Button>
              )}
              
              {user?.phone && (
                <Button 
                  size="sm" 
                  variant="outline"
                  className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700"
                  asChild
                >
                  <a href={`tel:${user.phone}`}>
                    <Phone className="h-3.5 w-3.5" />
                    <span>Call</span>
                  </a>
                </Button>
              )}
              
              {/* Social buttons */}
              <UserProfileSocial
                socialLinks={user?.social_links || []}
                variant="buttons"
                isCurrentUser={isCurrentUser}
                navigate={navigate}
              />
              
              {/* Payment Button - Show only if payment info exists */}
              {hasPaymentInfo && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center gap-1 text-green-700 border-green-200 hover:bg-green-50 hover:text-green-800"
                  onClick={() => setShowPaymentDialog(true)}
                >
                  <DollarSign className="h-3.5 w-3.5" />
                  <span>Pay</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Dialog */}
      {showPaymentDialog && (
        <PaymentDialog
          paymentId={user?.payment_id}
          paymentType={user?.payment_type}
        />
      )}
    </div>
  );
};

export default UserProfileHeader; 