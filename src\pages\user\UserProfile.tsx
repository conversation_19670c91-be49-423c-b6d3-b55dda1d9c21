import { use<PERSON><PERSON><PERSON>, Link, useLocation, useNavigate } from 'react-router-dom';
import { useUser } from '@/hooks/useUsers';
import { useInfluencerCoupons } from '@/hooks/useCoupons';
import { useProfileAnalytics } from '@/hooks/useProfileAnalytics';
import { useAuth } from '@/context/AuthContext';
import UserProfileHeader from '@/components/user/UserProfileHeader';
import UserCouponTabs from '@/components/user/UserCouponTabs';
import SimpleCouponList from '@/components/user/SimpleCouponList';
import UserAvatar from '@/components/user/UserAvatar';
import UserProfileSocial from '@/components/user/UserProfileSocial';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import SharedProfile from './SharedProfile';
import { ExtendedCoupon } from '@/types/coupon';
import { 
  Tag, 
  DollarSign, 
  Eye, 
  MousePointer, 
  Copy, 
  BarChart4, 
  ArrowRight,
  ChevronRight,
  Share2,
  TrendingUp,
  TrendingDown,
  Users,
  Mail,
  Award,
  Link as LinkIcon,
  UserPlus,
  MessageSquare,
  Gift,
  ShoppingBag,
  Utensils,
  Plus,
  Twitter,
  Facebook,
  Linkedin,
  Percent,
  Check,
  User,
  Star,
  Bookmark,
  Calendar,
  ExternalLink,
  Download,
  RefreshCw,
  WalletCards,
  Clock,
  Trash2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import BackButton from '@/components/BackButton';
import { PerformanceChart, CouponPerformanceCard, type StatItem } from '@/components/analytics';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useEffect, useRef, useState } from 'react';
import UserProfileSkeleton from '@/components/user/UserProfileSkeleton';
import { motion } from 'framer-motion';
import { COLORS } from '@/constants/theme';
import { supabase } from '@/integrations/supabase/client';
import { useOnboarding } from '@/context/OnboardingContext';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const UserProfile = () => {
  const { username } = useParams();
  const { user: currentUser, profile: currentUserProfile } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const shareSectionRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState("coupons");
  const [copied, setCopied] = useState(false);
  const [profileUrl, setProfileUrl] = useState('');
  const [sortedCoupons, setSortedCoupons] = useState<ExtendedCoupon[]>([]);
  const [deletingCouponId, setDeletingCouponId] = useState<string | null>(null);
  const { currentStep, handleStepCompletion, updateStep } = useOnboarding();
  
  // Check if we're in onboarding mode
  const searchParams = new URLSearchParams(location.search);
  const isOnboarding = searchParams.get('onboarding') === 'true';
  const onboardingStep = searchParams.get('step');
  

  
  // If no username is provided in the URL (accessing /profile directly), 
  // use the current logged-in user's profile username
  const targetUsername = username || (currentUserProfile?.username || '');
  console.log('Target username:', targetUsername, 'Current user:', currentUser?.id);
  
  // Ensure queries are enabled only when we have a valid username
  const { 
    data: user, 
    isLoading: userLoading, 
    error,
    refetch: refetchUser 
  } = useUser(targetUsername, { enabled: !!targetUsername });

  // Only fetch coupons when we have a user ID
  const { 
    data: coupons, 
    isLoading: couponsLoading,
    refetch: refetchCoupons 
  } = useInfluencerCoupons(user?.id || '') as { 
    data: ExtendedCoupon[] | undefined;
    isLoading: boolean;
    refetch: () => void;
  };

  // Fetch more detailed analytics data
  const { analytics, isLoading: analyticsLoading } = useProfileAnalytics(user?.id);

  // Try to refetch data if needed
  useEffect(() => {
    // If we have a username but no user data and we're not loading, try to refetch
    if (targetUsername && !user && !userLoading && !error) {
      console.log('Refetching user data for:', targetUsername);
      refetchUser();
    }
    
    // If we have a user ID but no coupons data and we're not loading, try to refetch
    if (user?.id && !coupons && !couponsLoading) {
      console.log('Refetching coupons for user ID:', user.id);
      refetchCoupons();
    }
  }, [targetUsername, user, coupons, userLoading, couponsLoading, error, refetchUser, refetchCoupons]);

  // Test Supabase connection
  useEffect(() => {
    const testConnection = async () => {
      try {
        const { checkSupabaseConnection } = await import('@/integrations/supabase/client');
        const isConnected = await checkSupabaseConnection();
        console.log('Supabase connection test:', isConnected ? 'SUCCESS' : 'FAILED');
        
        if (!isConnected) {
          console.error('Cannot connect to Supabase. Check your network or API keys.');
        }
      } catch (error) {
        console.error('Error testing Supabase connection:', error);
      }
    };
    
    testConnection();
  }, []);

  // Set profile URL for sharing
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setProfileUrl(`${window.location.origin}/${targetUsername}`);
    }
  }, [targetUsername]);

  // Create fallback data for preview if real data is not available
  const fallbackUser = {
    id: 'fallback-id',
    full_name: targetUsername || 'Example User',
    username: targetUsername || 'example_user',
    avatar_url: null,
    bio: 'This is a preview of how your profile will appear when there is no data connection.',
    website: 'https://example.com',
    role: 'influencer' as const,
    coupons_count: 5,
    social_links: [
      { platform: 'twitter', url: 'https://twitter.com/example' },
      { platform: 'instagram', url: 'https://instagram.com/example' }
    ]
  };

  // Fallback coupons data
  const fallbackCoupons = [
    {
      id: 'fallback-coupon-1',
      title: 'Example Store Discount',
      code: 'EXAMPLE20',
      discount_percent: 20,
      discount_description: "20% off your purchase",
      status: 'active',
      is_premium: false,
      created_at: new Date().toISOString(),
      expires_at: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
      brand: {
        id: 'brand-1',
        name: 'Example Store',
        logo_url: null
      }
    },
    {
      id: 'fallback-coupon-2',
      title: 'Premium Deal',
      code: 'PREMIUM50',
      discount_percent: 50,
      discount_description: "Premium members get 50% off",
      status: 'active',
      is_premium: true,
      created_at: new Date().toISOString(),
      expires_at: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(),
      brand: {
        id: 'brand-2',
        name: 'Premium Brand',
        logo_url: null
      }
    }
  ] as unknown as ExtendedCoupon[];

  const displayUser = user || ((!userLoading && error) ? fallbackUser : undefined);
  // Make sure we always have coupons to display by using fallback when needed
  const displayCoupons = (coupons && coupons.length > 0) 
    ? coupons 
    : (displayUser ? fallbackCoupons : []);
    
  // Log detailed information about what's being displayed
  useEffect(() => {
    console.log('Preview data:', {
      displayUser: displayUser ? 'available' : 'missing',
      displayCoupons: displayCoupons.length > 0 ? `${displayCoupons.length} coupons` : 'no coupons',
      usingFallbackUser: displayUser === fallbackUser,
      usingFallbackCoupons: displayCoupons === fallbackCoupons
    });
  }, [displayUser, displayCoupons]);

  // Debug helper
  useEffect(() => {
    console.log('Profile data state:', {
      targetUsername,
      userLoading,
      user: user ? 'User data exists' : 'No user data',
      userId: user?.id,
      couponsLoading,
      couponsCount: coupons?.length,
      error: error ? `Error: ${error.message}` : 'No error'
    });
  }, [targetUsername, user, coupons, userLoading, couponsLoading, error]);

  // Sort coupons by view count for performance calculation
  useEffect(() => {
    if (coupons && coupons.length > 0) {
      // Sort by view_count instead of usage_count (which doesn't exist in the type)
      const sortedCoupons = [...coupons].sort((a, b) => 
        (b.view_count || 0) - (a.view_count || 0)
      );
      setSortedCoupons(sortedCoupons);
    }
  }, [coupons]);

  // Handle sharing triggers
  useEffect(() => {
    // If the URL has share=true, scroll to the share section
    const shouldHighlightShare = searchParams.get('share') === 'true';
    
    if (shouldHighlightShare && shareSectionRef.current) {
      // Scroll to share section with animation
      shareSectionRef.current.scrollIntoView({ behavior: 'smooth' });
      
      // Highlight the section by briefly adding a pulse effect
      shareSectionRef.current.classList.add('animate-pulse');
      setTimeout(() => {
        if (shareSectionRef.current) {
          shareSectionRef.current.classList.remove('animate-pulse');
        }
      }, 1500);
    }
  }, [searchParams, shareSectionRef]);

  useEffect(() => {
    if (displayUser?.username) {
      try {
        // Create the absolute URL including origin
        const url = `${window.location.origin}/${encodeURIComponent(displayUser.username)}`;
        setProfileUrl(url);
        console.log('Profile URL updated:', url);
      } catch (error) {
        console.error('Error setting profile URL:', error);
      }
    }
  }, [displayUser?.username]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      setCopied(true);
      toast.success('Link copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const shareProfile = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Check out ${displayUser?.username}'s coupons`,
          text: `Check out all the coupon codes from ${displayUser?.username}`,
          url: profileUrl,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      copyToClipboard();
    }
  };

  // Handle case when user is not logged in and trying to access /profile
  if (!targetUsername && !currentUser) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h2 className="text-2xl font-bold mb-2 text-brand-pink-500">Login Required</h2>
          <p className="text-gray-600 mb-4">Please log in to view your profile.</p>
          <Button asChild>
            <Link to="/auth">
              Login / Sign Up
            </Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  // For shared links - if we have a username in the URL, we should show the profile 
  // even if the user is not logged in
  const isSharedLink = !!username;
  
  // Check if viewing own profile - if not logged in but viewing a shared link, it's not own profile
  const isOwnProfile = currentUser && displayUser && currentUser.id === displayUser.id;

  // Add function to delete coupon
  const deleteCoupon = async (couponId: string) => {
    if (!user || !couponId) return;
    
    try {
      setDeletingCouponId(couponId);
      
      const { error } = await supabase
        .from('coupons')
        .delete()
        .eq('id', couponId)
        .eq('influencer_id', currentUser?.id); // Safety check to ensure only the owner can delete
      
      if (error) throw error;
      
      toast.success('Coupon deleted successfully');
      
      // Update the local state to remove the deleted coupon
      const updatedCoupons = displayCoupons.filter(c => c.id !== couponId);
      // We can't directly modify displayCoupons, so we'll refetch the data
      refetchCoupons();
      
    } catch (error) {
      console.error('Error deleting coupon:', error);
      toast.error('Failed to delete coupon. Please try again.');
    } finally {
      setDeletingCouponId(null);
    }
  };

  // Function to check if a coupon is expired
  const isExpired = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  const [formData, setFormData] = useState({
    displayName: currentUserProfile?.display_name || '',
    bio: currentUserProfile?.bio || '',
    website: currentUserProfile?.website || ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          display_name: formData.displayName,
          bio: formData.bio,
          website: formData.website,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentUser?.id);

      if (updateError) throw updateError;

      toast.success('Profile updated successfully');

      // If in onboarding, mark this step as complete
      if (currentStep === 'profile_setup') {
        await handleStepCompletion('profile_setup');
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      toast.error('Failed to update profile');
    }
  };

  // For regular profiles with MainLayout
  return (
    <MainLayout>
      <PageContainer decorationType="minimal" decorationOpacity={0.7}>
        {/* Top navigation with better mobile spacing */}
        <div className="mb-6 sm:mb-8 flex justify-between items-center">
          <BackButton />
          {!userLoading && displayUser && displayUser?.id === currentUser?.id && (
            <Button onClick={() => navigate('/settings')} size="sm" variant="outline" className="flex items-center gap-1 text-xs sm:text-sm">
              <User className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Edit Profile</span>
              <span className="inline sm:hidden">Edit</span>
            </Button>
          )}
        </div>

        {/* If we're still loading or don't have user data yet, show a loading skeleton */}
        {(userLoading || !displayUser) ? (
          <UserProfileSkeleton />
        ) : (
          <div className="space-y-10">
            {/* Hero section with profile info - Enhanced with card styling and gradients */}
            <div 
              className="rounded-lg bg-white overflow-hidden shadow-md border border-gray-200 p-6 mb-8"
            >
              <div className="flex flex-col sm:flex-row w-full items-center sm:items-start gap-6">
                <div className="relative">
                  <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-white shadow-md bg-gray-100 flex items-center justify-center">
                    <div className="w-full h-full overflow-hidden">
                      <UserAvatar
                        avatarUrl={displayUser?.avatar_url}
                        fullName={displayUser?.full_name || ''}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  {displayUser?.role === 'influencer' && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-xs px-2 py-0.5 rounded-full shadow-sm">
                      <Star className="h-3 w-3 inline mr-1" />
                      Influencer
                    </div>
                  )}
                </div>
                  
                {/* Profile details - Better alignment for all screen sizes */}
                <div className="flex-1 text-center sm:text-left w-full">
                  <div className="flex flex-col sm:flex-row gap-3 sm:items-center justify-between mb-3">
                    <div>
                      <h1 className="text-xl md:text-2xl font-bold text-gray-800">{displayUser.full_name}</h1>
                      <p className="text-base text-gray-600 mt-1">@{displayUser.username}</p>
                    </div>
                    
                    {/* Stats cards with consistent styling */}
                    <div className="flex items-center gap-4 justify-center sm:justify-end mt-2 sm:mt-0">
                      <div className="flex items-center gap-1 bg-gray-100 rounded-lg px-3 py-1.5">
                        <Tag className="h-3.5 w-3.5 text-indigo-500" />
                        <span className="text-sm font-medium text-gray-700">{displayUser.coupons_count || 0}</span>
                        <span className="text-xs text-gray-500">Coupons</span>
                      </div>
                      <div className="flex items-center gap-1 bg-gray-100 rounded-lg px-3 py-1.5">
                        <Eye className="h-3.5 w-3.5 text-blue-500" />
                        <span className="text-sm font-medium text-gray-700">{analytics?.totalViews || 0}</span>
                        <span className="text-xs text-gray-500">Views</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Profile Bio Section - Enhanced with better styling */}
                  {displayUser?.bio && (
                    <div className="mt-3 mb-5 px-3 sm:px-0 py-3 sm:py-0 bg-gray-50 rounded-lg sm:bg-transparent">
                      <p className="text-sm text-gray-700 max-w-3xl leading-relaxed">
                        {displayUser.bio}
                      </p>
                    </div>
                  )}
                    
                  <div className="flex flex-wrap justify-center sm:justify-start gap-4 mt-4">
                    {displayUser?.website && (
                      <a href={displayUser.website} target="_blank" rel="noopener noreferrer" className="inline-flex items-center gap-1 text-sm text-indigo-600 hover:underline">
                        <LinkIcon className="h-3 w-3" />
                        {displayUser.website.replace(/^https?:\/\//, '')}
                      </a>
                    )}
                  
                    <UserProfileSocial 
                      socialLinks={displayUser?.social_links}
                      website={displayUser?.website}
                      isCurrentUser={isOwnProfile}
                      navigate={navigate}
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* Main Content - Tab bar with improved styling */}
            <div>
              <div className="border-b border-gray-200 dark:border-gray-700 mb-6 sm:mb-8">
                <Tabs 
                  defaultValue="coupons" 
                  value={activeTab}
                  onValueChange={(value) => {
                    setActiveTab(value);
                  }}
                  className="w-full" 
                >
                  <div className="flex w-full items-center justify-between">
                    <div className="hidden sm:block text-lg font-medium" style={{ color: COLORS.neutral[700] }}>
                      Profile Content
                    </div>
                    <TabsList className="w-full overflow-x-auto flex-nowrap sm:w-auto sm:justify-end bg-transparent pb-1">
                      <TabsTrigger 
                        value="coupons"
                        className="flex-1 min-w-[80px] sm:flex-initial border-b-2 border-transparent data-[state=active]:border-primary-600 data-[state=active]:text-primary-600 rounded-none bg-transparent dark:data-[state=active]:text-primary-400 dark:data-[state=active]:border-primary-400 px-2 sm:px-4 py-2 text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          '--tw-border-opacity': '1',
                          borderColor: `${activeTab === 'coupons' ? COLORS.primary.main : 'transparent'}`,
                          color: activeTab === 'coupons' ? COLORS.primary.main : COLORS.neutral[700]
                        } as React.CSSProperties}
                      >
                        <Tag className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Coupons
                      </TabsTrigger>
                      <TabsTrigger 
                        value="analytics"
                        className="flex-1 min-w-[80px] sm:flex-initial border-b-2 border-transparent data-[state=active]:border-secondary-600 data-[state=active]:text-secondary-600 rounded-none bg-transparent dark:data-[state=active]:text-secondary-400 dark:data-[state=active]:border-secondary-400 px-2 sm:px-4 py-2 text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          '--tw-border-opacity': '1',
                          borderColor: `${activeTab === 'analytics' ? COLORS.secondary.main : 'transparent'}`,
                          color: activeTab === 'analytics' ? COLORS.secondary.main : COLORS.neutral[700]
                        } as React.CSSProperties}
                      >
                        <BarChart4 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Analytics
                      </TabsTrigger>
                      <TabsTrigger 
                        value="transactions"
                        className="flex-1 min-w-[80px] sm:flex-initial border-b-2 border-transparent data-[state=active]:border-tertiary-600 data-[state=active]:text-tertiary-600 rounded-none bg-transparent dark:data-[state=active]:text-tertiary-400 dark:data-[state=active]:border-tertiary-400 px-2 sm:px-4 py-2 text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          '--tw-border-opacity': '1',
                          borderColor: `${activeTab === 'transactions' ? COLORS.tertiary.main : 'transparent'}`,
                          color: activeTab === 'transactions' ? COLORS.tertiary.main : COLORS.neutral[700]
                        } as React.CSSProperties}
                      >
                        <WalletCards className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        <span className="hidden xs:inline">Transactions</span>
                        <span className="inline xs:hidden">Trans</span>
                      </TabsTrigger>
                      <TabsTrigger 
                        value="saved"
                        className="flex-1 min-w-[80px] sm:flex-initial border-b-2 border-transparent data-[state=active]:border-accent-600 data-[state=active]:text-accent-600 rounded-none bg-transparent dark:data-[state=active]:text-accent-400 dark:data-[state=active]:border-accent-400 px-2 sm:px-4 py-2 text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          '--tw-border-opacity': '1',
                          borderColor: `${activeTab === 'saved' ? COLORS.accent.main : 'transparent'}`,
                          color: activeTab === 'saved' ? COLORS.accent.main : COLORS.neutral[700]
                        } as React.CSSProperties}
                      >
                        <Bookmark className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        Saved
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  {/* Coupons Tab Content - Enhanced card styling */}
                  <TabsContent value="coupons" className="pt-4 sm:pt-6">
                    {couponsLoading ? (
                      <div className="grid gap-3 sm:gap-4">
                        {Array.from({ length: 3 }).map((_, i) => (
                          <div key={i} className="bg-white/50 dark:bg-gray-800/50 rounded-lg shadow-sm p-4 animate-pulse h-16 sm:h-20"></div>
                        ))}
                      </div>
                    ) : displayCoupons.length > 0 ? (
                      <div className="transition-all duration-300 p-1">
                        <h3 className="text-lg font-medium mb-4" style={{ color: COLORS.neutral[800] }}>All Coupons ({displayCoupons.length})</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {displayCoupons.map((coupon) => (
                            <div 
                              key={coupon.id}
                              className="bg-white rounded-lg shadow p-4 border border-gray-100 flex flex-col hover:shadow-md transition-shadow relative"
                            >
                              {/* Show delete button only for own coupons */}
                              {displayUser?.id === currentUser?.id && (
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="absolute top-2 right-2 h-7 w-7 p-0 rounded-full hover:bg-red-50 text-gray-400 hover:text-red-500"
                                  onClick={() => {
                                    if (window.confirm('Are you sure you want to delete this coupon?')) {
                                      deleteCoupon(coupon.id);
                                    }
                                  }}
                                  disabled={deletingCouponId === coupon.id}
                                >
                                  {deletingCouponId === coupon.id ? (
                                    <RefreshCw className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              )}
                              
                              <div className="flex justify-between items-start mb-3">
                                <div className="flex items-center">
                                  <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
                                    {coupon.brand?.logo_url ? (
                                      <img 
                                        src={coupon.brand.logo_url} 
                                        alt={coupon.brand?.name} 
                                        className="w-full h-full object-cover"
                                      />
                                    ) : (
                                      <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                        <span className="text-gray-500 font-medium">
                                          {coupon.brand?.name?.charAt(0) || 'B'}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-gray-900">{coupon.title}</h4>
                                    <p className="text-sm text-gray-500">{coupon.brand?.name}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1 mr-8">
                                  {coupon.is_premium && (
                                    <span className="bg-amber-50 text-amber-700 text-xs px-2 py-1 rounded-full font-medium flex items-center">
                                      <Star className="h-3 w-3 mr-1" />
                                      Premium
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* Coupon code section */}
                              <div className="flex items-center justify-between mb-3 bg-gray-50 p-2 rounded">
                                <div className="font-mono font-medium text-gray-800">{coupon.code}</div>
                                <Button 
                                  size="sm" 
                                  variant="ghost" 
                                  onClick={() => {
                                    navigator.clipboard.writeText(coupon.code);
                                    toast.success('Coupon code copied!');
                                  }}
                                  className="h-7 px-2 text-xs gap-1"
                                >
                                  <Copy className="h-3 w-3" />
                                  Copy
                                </Button>
                              </div>

                              {/* Discount and expiry information */}
                              <div className="mt-auto flex justify-between items-center text-xs text-gray-500">
                                <div className="flex items-center">
                                  <Percent className="h-3 w-3 mr-1" />
                                  <span>
                                    {coupon.discount_percent ? `${coupon.discount_percent}% off` : 
                                    coupon.discount_description || 'Special discount'}
                                  </span>
                                </div>
                                
                                {coupon.expires_at && (
                                  <div className="flex items-center">
                                    {isExpired(coupon.expires_at) ? (
                                      <span className="bg-red-50 text-red-600 px-2 py-0.5 rounded-full flex items-center">
                                        <Clock className="h-3 w-3 mr-1" />
                                        Expired on {new Date(coupon.expires_at).toLocaleDateString('en-US', {
                                          month: 'short',
                                          day: 'numeric',
                                          year: 'numeric'
                                        })}
                                      </span>
                                    ) : (
                                      <>
                                        <Clock className="h-3 w-3 mr-1" />
                                        <span>Expires: {new Date(coupon.expires_at).toLocaleDateString('en-US', {
                                          month: 'short',
                                          day: 'numeric',
                                          year: 'numeric'
                                        })}</span>
                                      </>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="py-8 sm:py-12 text-center rounded-xl">
                        <div className="mx-auto w-14 sm:w-16 h-14 sm:h-16 flex items-center justify-center rounded-full bg-white/50 dark:bg-gray-800/50 mb-4">
                          <Tag className="h-7 sm:h-8 w-7 sm:w-8" style={{ color: COLORS.primary.main }} />
                        </div>
                        <h3 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white mb-3">No coupons found</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-6">
                          {displayUser?.id === currentUser?.id 
                            ? "You haven't created any coupons yet. Create your first coupon to share exclusive deals with your audience."
                            : `${displayUser.full_name || displayUser.username} hasn't created any coupons yet.`}
                        </p>
                        {displayUser?.id === currentUser?.id && (
                          <Link to="/create-coupon">
                            <Button 
                              className="text-white text-sm"
                              style={{ 
                                background: COLORS.primary.gradient,
                              }}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Create Coupon
                            </Button>
                          </Link>
                        )}
                      </div>
                    )}
                  </TabsContent>

                  {/* Analytics Tab Content */}
                  <TabsContent value="analytics" className="pt-4 sm:pt-6">
                    {analyticsLoading ? (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                          {[1, 2, 3, 4].map((i) => (
                            <div key={i} className="bg-white p-4 rounded-lg shadow-sm animate-pulse">
                              <div className="h-4 w-24 bg-gray-200 rounded mb-3"></div>
                              <div className="h-6 w-12 bg-gray-300 rounded"></div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : !analytics ? (
                      <div className="text-center py-12 bg-white rounded-lg shadow-md">
                        <BarChart4 className="w-12 h-12 mx-auto text-gray-300 mb-4" />
                        <h3 className="text-xl font-medium text-gray-800 mb-2">No Analytics Data Available</h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                          Start sharing your coupons to see analytics data and insights about their performance.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                          {/* Total Coupon Clicks */}
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <div className="flex items-center gap-2 mb-3">
                              <MousePointer className="h-5 w-5 text-purple-500" />
                              <h3 className="text-sm font-medium text-gray-600">Total Coupon Clicks</h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-semibold text-gray-900">{analytics.couponClicks || 0}</span>
                              <span className="text-sm text-green-600">↑ 0% from previous period</span>
                            </div>
                          </div>

                          {/* Total Coupon Views */}
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <div className="flex items-center gap-2 mb-3">
                              <Eye className="h-5 w-5 text-blue-500" />
                              <h3 className="text-sm font-medium text-gray-600">Total Coupon Views</h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-semibold text-gray-900">{analytics.totalViews || 0}</span>
                              <span className="text-sm text-green-600">↑ 0% from previous period</span>
                            </div>
                          </div>

                          {/* Conversion Rate */}
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <div className="flex items-center gap-2 mb-3">
                              <Percent className="h-5 w-5 text-green-500" />
                              <h3 className="text-sm font-medium text-gray-600">Conversion Rate</h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-semibold text-gray-900">
                                {((analytics.couponCopies || 0) / (analytics.totalViews || 1) * 100).toFixed(1)}%
                              </span>
                              <span className="text-sm text-green-600">↑ 0% from previous period</span>
                            </div>
                          </div>

                          {/* Premium Sales */}
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <div className="flex items-center gap-2 mb-3">
                              <Award className="h-5 w-5 text-amber-500" />
                              <h3 className="text-sm font-medium text-gray-600">Premium Sales</h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-semibold text-gray-900">{analytics.premiumSales || 0}</span>
                              <span className="text-sm text-green-600">↑ 0% from previous period</span>
                            </div>
                          </div>
                        </div>

                        {/* View Complete Analytics Button */}
                        <div className="flex justify-center pt-4">
                          <Button 
                            onClick={() => navigate('/analytics')}
                            className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white gap-2"
                          >
                            <BarChart4 className="h-4 w-4" />
                            View Complete Analytics
                            <ExternalLink className="h-3.5 w-3.5 ml-1" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </TabsContent>
                  
                  {/* Transactions Tab Content */}
                  <TabsContent value="transactions" className="pt-4 sm:pt-6">
                    <div className="text-center py-12 bg-white rounded-lg shadow-md">
                      <div className="mx-auto w-16 h-16 bg-indigo-50 rounded-full flex items-center justify-center mb-4">
                        <WalletCards className="h-8 w-8 text-indigo-500" />
                      </div>
                      <h3 className="text-xl font-medium text-gray-800 mb-2">View Complete Transaction History</h3>
                      <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Track all your premium coupon purchases and sales with detailed transaction history.
                      </p>
                      <Button 
                        onClick={() => navigate('/user/transactions')}
                        className="bg-indigo-500 hover:bg-indigo-600 text-white gap-2"
                      >
                        <WalletCards className="h-4 w-4" />
                        Go to Transactions
                      </Button>
                    </div>
                  </TabsContent>

                  {/* Saved Tab Content */}
                  <TabsContent value="saved" className="pt-4 sm:pt-6">
                    <div className="text-center py-12 bg-white rounded-lg shadow-md">
                      <div className="mx-auto w-16 h-16 bg-amber-50 rounded-full flex items-center justify-center mb-4">
                        <Bookmark className="h-8 w-8 text-amber-500" />
                      </div>
                      <h3 className="text-xl font-medium text-gray-800 mb-2">View Saved Coupons</h3>
                      <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Access all your saved coupons and collections in one place.
                      </p>
                      <Button 
                        onClick={() => navigate('/user/saved-coupons')}
                        className="bg-amber-500 hover:bg-amber-600 text-white gap-2"
                      >
                        <Bookmark className="h-4 w-4" />
                        Go to Saved Coupons
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>

            {/* Share section (only visible for the current user viewing their own profile) */}
            {isOwnProfile && (
              <div ref={shareSectionRef} className="mt-6 md:mt-8">
                <div className="bg-white rounded-lg p-5 shadow-sm border border-gray-100 mb-8">
                  <div className="mb-3 flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <Share2 className="h-4 w-4 text-blue-500" /> 
                      Share Your Profile
                    </h3>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-5">
                    Share your profile with your audience so they can access all your coupon codes
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default UserProfile;
