import { Skeleton } from "@/components/ui/skeleton";

interface CouponsSkeletonProps {
  count?: number;
}

const CouponsSkeleton = ({ count = 4 }: CouponsSkeletonProps) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="coupon-card border-[3px] rounded-xl overflow-hidden bg-gray-50/50 border-gray-400 shadow-md h-full p-3 flex flex-col">
          {/* Brand Info */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2 overflow-hidden">
              <Skeleton className="w-10 h-10 rounded-full flex-shrink-0" />
              <div>
                <Skeleton className="h-4 w-28 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
            <div className="flex items-center flex-shrink-0 ml-1 hidden sm:flex">
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
          
          {/* Discount Info */}
          <div className="mb-2">
            <Skeleton className="h-6 w-32" />
          </div>
          
          {/* Influencer Info */}
          <div className="flex items-center gap-2 mb-2 overflow-hidden hidden sm:flex">
            <Skeleton className="w-5 h-5 rounded-full flex-shrink-0" />
            <Skeleton className="h-3 w-28" />
          </div>
          
          {/* Expiration Info */}
          <div className="flex items-center mb-2">
            <Skeleton className="w-3 h-3 mr-1 flex-shrink-0 rounded-full" />
            <Skeleton className="h-3 w-24" />
          </div>
          
          {/* Coupon Code */}
          <div className="flex items-center gap-2 mt-auto">
            <Skeleton className="flex-1 h-8 rounded-lg" />
            <Skeleton className="h-8 w-8 rounded-md flex-shrink-0" />
          </div>
        </div>
      ))}
    </>
  );
};

export default CouponsSkeleton;
