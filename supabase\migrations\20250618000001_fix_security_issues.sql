-- Fix Security Issues Migration
-- This migration addresses the security issues identified by Supabase linter:
-- 1. Remove SECURITY DEFINER from views
-- 2. Enable RLS on public tables

-- ============================================================================
-- 1. FIX SECURITY DEFINER VIEWS
-- ============================================================================

-- Drop and recreate popular_brands view without SECURITY DEFINER
DROP VIEW IF EXISTS public.popular_brands CASCADE;

CREATE VIEW public.popular_brands AS
SELECT
  b.id,
  b.name,
  b.logo_url,
  b.website,
  b.created_at,
  b.updated_at,
  count(c.id) AS coupon_count,
  sum(COALESCE(c.view_count, 0)) AS total_views
FROM brands b
LEFT JOIN coupons c ON b.id = c.brand_id
GROUP BY b.id
ORDER BY sum(COALESCE(c.view_count, 0)) DESC NULLS LAST;

-- Drop and recreate trending_coupons view without SECURITY DEFINER
DROP VIEW IF EXISTS public.trending_coupons CASCADE;

CREATE VIEW public.trending_coupons AS
SELECT 
  c.id,
  c.title,
  c.code,
  c.influencer_id,
  c.brand_id,
  c.category_id,
  c.discount_percent,
  c.discount_amount,
  c.discount_description,
  c.expires_at,
  c.status,
  c.featured,
  c.success_rate,
  c.total_ratings,
  c.average_rating,
  c.is_affiliate,
  c.affiliate_link,
  c.scraped,
  c.created_at,
  c.updated_at,
  c.is_premium,
  c.price,
  c.background_color_hex,
  c.copy_count,
  c.view_count,
  c.use_count,
  c.conversion_rate,
  COALESCE(c.view_count, 0) AS views,
  COALESCE(c.copy_count, 0) AS copies,
  COALESCE(c.use_count, 0) AS uses,
  COALESCE(c.conversion_rate, 0::numeric) AS conversion
FROM coupons c
WHERE c.status = 'active'::coupon_status
ORDER BY c.view_count DESC NULLS LAST, c.created_at DESC;

-- Drop and recreate popular_categories view without SECURITY DEFINER
DROP VIEW IF EXISTS public.popular_categories CASCADE;

CREATE VIEW public.popular_categories AS
SELECT 
  cat.id,
  cat.name,
  cat.slug,
  cat.created_at,
  cat.coupon_count,
  cat.icon_url,
  cat.cover_image_url,
  cat.description,
  cat.display_order,
  cat.color_hex,
  cat.is_featured,
  count(c.id) AS active_coupon_count,
  sum(COALESCE(c.view_count, 0)) AS total_views
FROM categories cat
LEFT JOIN coupons c ON cat.id = c.category_id
GROUP BY cat.id
ORDER BY sum(COALESCE(c.view_count, 0)) DESC NULLS LAST;

-- ============================================================================
-- 2. ENABLE RLS ON PUBLIC TABLES
-- ============================================================================

-- Enable RLS on daily_analytics table
ALTER TABLE public.daily_analytics ENABLE ROW LEVEL SECURITY;

-- Enable RLS on platform_settings table  
ALTER TABLE public.platform_settings ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 3. CREATE RLS POLICIES FOR DAILY_ANALYTICS
-- ============================================================================

-- Only allow service_role full access to daily_analytics
CREATE POLICY "Service role full access to daily_analytics"
  ON public.daily_analytics
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Allow authenticated admin users to read daily_analytics
CREATE POLICY "Admin users can read daily_analytics"
  ON public.daily_analytics
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = (select auth.uid()) 
      AND profiles.role = 'admin'
    )
  );

-- ============================================================================
-- 4. CREATE RLS POLICIES FOR PLATFORM_SETTINGS
-- ============================================================================

-- Only allow service_role full access to platform_settings
CREATE POLICY "Service role full access to platform_settings"
  ON public.platform_settings
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Allow authenticated admin users to read platform_settings
CREATE POLICY "Admin users can read platform_settings"
  ON public.platform_settings
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = (select auth.uid()) 
      AND profiles.role = 'admin'
    )
  );

-- Allow authenticated admin users to update platform_settings
CREATE POLICY "Admin users can update platform_settings"
  ON public.platform_settings
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = (select auth.uid()) 
      AND profiles.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = (select auth.uid()) 
      AND profiles.role = 'admin'
    )
  );

-- ============================================================================
-- 5. GRANT APPROPRIATE PERMISSIONS
-- ============================================================================

-- Grant SELECT on views to authenticated users
GRANT SELECT ON public.popular_brands TO authenticated;
GRANT SELECT ON public.trending_coupons TO authenticated;
GRANT SELECT ON public.popular_categories TO authenticated;

-- Grant SELECT on views to anonymous users (for public access)
GRANT SELECT ON public.popular_brands TO anon;
GRANT SELECT ON public.trending_coupons TO anon;
GRANT SELECT ON public.popular_categories TO anon;

-- Note: Table permissions are handled by RLS policies above

-- ============================================================================
-- 6. FIX FUNCTION SEARCH PATH SECURITY ISSUES
-- ============================================================================

-- Fix search_path for get_user_premium_coupons function
CREATE OR REPLACE FUNCTION public.get_user_premium_coupons(p_user_id UUID)
RETURNS TABLE (
  coupon_id UUID,
  coupon_title TEXT,
  brand_name TEXT,
  influencer_name TEXT,
  code TEXT,
  price NUMERIC,
  purchased_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id AS coupon_id,
    c.title AS coupon_title,
    b.name AS brand_name,
    p.full_name AS influencer_name,
    c.code,
    c.price,
    pp.purchased_at
  FROM premium_purchases pp
  JOIN coupons c ON pp.coupon_id = c.id
  LEFT JOIN brands b ON c.brand_id = b.id
  LEFT JOIN profiles p ON c.influencer_id = p.id
  WHERE pp.buyer_id = p_user_id
  AND pp.status = 'completed'
  ORDER BY pp.purchased_at DESC;
END;
$$;

-- Fix search_path for has_purchased_premium_coupon function
CREATE OR REPLACE FUNCTION public.has_purchased_premium_coupon(
  p_user_id UUID,
  p_coupon_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM premium_purchases
    WHERE buyer_id = p_user_id
    AND coupon_id = p_coupon_id
    AND status = 'completed'
  ) INTO v_has_purchased;

  RETURN v_has_purchased;
END;
$$;

-- Fix search_path for mark_all_notifications_read function
CREATE OR REPLACE FUNCTION public.mark_all_notifications_read(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE notifications
  SET is_read = true
  WHERE user_id = p_user_id AND is_read = false;
END;
$$;

-- Fix search_path for process_transaction_and_update_wallet function
CREATE OR REPLACE FUNCTION public.process_transaction_and_update_wallet()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Ensure the seller wallet exists
  INSERT INTO public.user_wallets (user_id)
  VALUES (NEW.seller_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Update the seller's wallet based on transaction status
  IF NEW.status = 'completed' THEN
    -- Add the seller amount to available balance and lifetime earnings
    UPDATE public.user_wallets
    SET
      available_balance = available_balance + NEW.seller_amount,
      lifetime_earnings = lifetime_earnings + NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;

  ELSIF NEW.status = 'pending' THEN
    -- Add the seller amount to pending balance only
    UPDATE public.user_wallets
    SET
      pending_balance = pending_balance + NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;

  END IF;

  RETURN NEW;
END;
$$;

-- Fix search_path for update_wallet_on_transaction_status_change function
CREATE OR REPLACE FUNCTION public.update_wallet_on_transaction_status_change()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- If status changed from pending to completed
  IF OLD.status = 'pending' AND NEW.status = 'completed' THEN
    UPDATE public.user_wallets
    SET
      available_balance = available_balance + NEW.seller_amount,
      pending_balance = pending_balance - NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
  -- If status changed from pending to failed/canceled
  ELSIF OLD.status = 'pending' AND (NEW.status = 'failed' OR NEW.status = 'canceled') THEN
    UPDATE public.user_wallets
    SET
      pending_balance = pending_balance - NEW.seller_amount,
      last_updated = now()
    WHERE user_id = NEW.seller_id;
  END IF;

  RETURN NEW;
END;
$$;

-- Fix search_path for generate_daily_analytics function
CREATE OR REPLACE FUNCTION public.generate_daily_analytics(target_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day')
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  category_stats JSONB;
BEGIN
  -- Calculate category stats for the day
  WITH category_counts AS (
    SELECT
      c.category_id,
      cat.name as category_name,
      COUNT(*) as count,
      SUM(CASE WHEN ci.interaction_type = 'view' THEN 1 ELSE 0 END)::INTEGER as views,
      SUM(CASE WHEN ci.interaction_type = 'copy' THEN 1 ELSE 0 END)::INTEGER as copies,
      SUM(CASE WHEN ci.interaction_type = 'use' THEN 1 ELSE 0 END)::INTEGER as uses
    FROM public.coupon_interactions ci
    JOIN public.coupons c ON ci.coupon_id = c.id
    JOIN public.categories cat ON c.category_id = cat.id
    WHERE DATE(ci.occurred_at) = target_date
    GROUP BY c.category_id, cat.name
  )
  SELECT jsonb_object_agg(category_name, jsonb_build_object(
    'views', views,
    'copies', copies,
    'uses', uses,
    'total', count
  )) INTO category_stats
  FROM category_counts;

  -- Insert or update daily analytics
  INSERT INTO public.daily_analytics (
    date,
    total_views,
    total_copies,
    total_uses,
    unique_users,
    category_stats,
    created_at
  )
  SELECT
    target_date,
    (SELECT COUNT(*) FROM public.coupon_interactions WHERE DATE(occurred_at) = target_date AND interaction_type = 'view'),
    (SELECT COUNT(*) FROM public.coupon_interactions WHERE DATE(occurred_at) = target_date AND interaction_type = 'copy'),
    (SELECT COUNT(*) FROM public.coupon_interactions WHERE DATE(occurred_at) = target_date AND interaction_type = 'use'),
    (SELECT COUNT(DISTINCT user_id) FROM public.coupon_interactions WHERE DATE(occurred_at) = target_date),
    category_stats,
    now()
  ON CONFLICT (date)
  DO UPDATE SET
    total_views = EXCLUDED.total_views,
    total_copies = EXCLUDED.total_copies,
    total_uses = EXCLUDED.total_uses,
    unique_users = EXCLUDED.unique_users,
    category_stats = EXCLUDED.category_stats,
    updated_at = now();
END;
$$;

-- Fix search_path for update_influencer_analytics function
CREATE OR REPLACE FUNCTION public.update_influencer_analytics(target_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day')
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Get all influencers with activity on the target date
  WITH active_influencers AS (
    SELECT DISTINCT p.id as influencer_id
    FROM public.profiles p
    WHERE p.role = 'influencer'
    AND EXISTS (
      SELECT 1 FROM public.coupons c
      WHERE c.influencer_id = p.id
    )
  )

  -- Insert or update analytics for each active influencer
  INSERT INTO public.influencer_analytics (
    influencer_id,
    date,
    profile_views,
    coupon_views,
    coupon_copies,
    coupon_uses,
    total_earnings,
    new_followers,
    conversion_rate
  )
  SELECT
    ai.influencer_id,
    target_date,
    (SELECT COUNT(*) FROM public.profile_views pv WHERE pv.profile_id = ai.influencer_id AND DATE(pv.viewed_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'copy' AND DATE(ci.occurred_at) = target_date),
    (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
     WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'use' AND DATE(ci.occurred_at) = target_date),
    (SELECT COALESCE(SUM(t.seller_amount), 0) FROM public.transactions t JOIN public.coupons c ON t.coupon_id = c.id
     WHERE c.influencer_id = ai.influencer_id AND DATE(t.created_at) = target_date AND t.status = 'completed'),
    0, -- new_followers placeholder
    CASE
      WHEN (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
            WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date) > 0
      THEN
        (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
         WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'use' AND DATE(ci.occurred_at) = target_date)::NUMERIC /
        (SELECT COUNT(*) FROM public.coupon_interactions ci JOIN public.coupons c ON ci.coupon_id = c.id
         WHERE c.influencer_id = ai.influencer_id AND ci.interaction_type = 'view' AND DATE(ci.occurred_at) = target_date)
      ELSE 0
    END
  FROM active_influencers ai
  ON CONFLICT (influencer_id, date)
  DO UPDATE SET
    profile_views = EXCLUDED.profile_views,
    coupon_views = EXCLUDED.coupon_views,
    coupon_copies = EXCLUDED.coupon_copies,
    coupon_uses = EXCLUDED.coupon_uses,
    total_earnings = EXCLUDED.total_earnings,
    new_followers = EXCLUDED.new_followers,
    conversion_rate = EXCLUDED.conversion_rate,
    updated_at = now();
END;
$$;

-- Fix search_path for purchase_premium_coupon function
CREATE OR REPLACE FUNCTION public.purchase_premium_coupon(
  p_buyer_id UUID,
  p_coupon_id UUID,
  p_stripe_payment_id TEXT,
  p_stripe_customer_id TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_coupon_record RECORD;
  v_platform_fee NUMERIC;
  v_min_fee NUMERIC;
  v_max_fee NUMERIC;
  v_seller_amount NUMERIC;
  v_purchase_id UUID;
  v_transaction_id UUID;
BEGIN
  -- Check if coupon exists and is premium
  SELECT * INTO v_coupon_record
  FROM public.coupons
  WHERE id = p_coupon_id AND is_premium = true;

  IF v_coupon_record.id IS NULL THEN
    RAISE EXCEPTION 'Coupon not found or is not a premium coupon';
  END IF;

  -- Check if user has already purchased this coupon
  IF EXISTS (
    SELECT 1 FROM public.premium_purchases
    WHERE buyer_id = p_buyer_id AND coupon_id = p_coupon_id AND status = 'completed'
  ) THEN
    RAISE EXCEPTION 'User has already purchased this coupon';
  END IF;

  -- Get platform fee settings (5% with min $0.50, max $5.00)
  v_platform_fee := GREATEST(LEAST(v_coupon_record.price * 0.05, 5.00), 0.50);
  v_seller_amount := v_coupon_record.price - v_platform_fee;

  -- Create premium purchase record
  INSERT INTO public.premium_purchases (
    buyer_id,
    coupon_id,
    seller_id,
    amount,
    platform_fee,
    seller_amount,
    stripe_payment_id,
    stripe_customer_id,
    status,
    purchased_at
  ) VALUES (
    p_buyer_id,
    p_coupon_id,
    v_coupon_record.influencer_id,
    v_coupon_record.price,
    v_platform_fee,
    v_seller_amount,
    p_stripe_payment_id,
    p_stripe_customer_id,
    'completed',
    now()
  ) RETURNING id INTO v_purchase_id;

  -- Create transaction record
  INSERT INTO public.transactions (
    buyer_id,
    seller_id,
    coupon_id,
    purchase_id,
    amount,
    platform_fee,
    seller_amount,
    payment_id,
    payment_method,
    status
  ) VALUES (
    p_buyer_id,
    v_coupon_record.influencer_id,
    p_coupon_id,
    v_purchase_id,
    v_coupon_record.price,
    v_platform_fee,
    v_seller_amount,
    p_stripe_payment_id,
    'stripe',
    'completed'
  ) RETURNING id INTO v_transaction_id;

  -- Track coupon interaction
  INSERT INTO public.coupon_interactions (
    coupon_id,
    user_id,
    interaction_type,
    occurred_at
  ) VALUES (
    p_coupon_id,
    p_buyer_id,
    'purchase',
    now()
  );

  -- Return the purchase ID
  RETURN v_purchase_id;
END;
$$;

-- Create handle_new_user function with proper search_path
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Initialize user wallet
  INSERT INTO public.user_wallets (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Set initial profile completeness
  UPDATE public.profiles
  SET profile_complete_percent = 10
  WHERE id = NEW.id;

  RETURN NEW;
END;
$$;

-- Create sanitize_onboarding_step function with proper search_path
CREATE OR REPLACE FUNCTION public.sanitize_onboarding_step(step_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  valid_steps TEXT[] := ARRAY[
    'welcome',
    'website_tour',
    'start_menu_intro',
    'start_menu_features',
    'explore_brands',
    'explore_categories',
    'profile_setup',
    'social_links',
    'payment_setup',
    'first_coupon',
    'completed'
  ];
BEGIN
  -- Return the step if it's valid, otherwise return 'welcome'
  IF step_input = ANY(valid_steps) THEN
    RETURN step_input;
  ELSE
    RETURN 'welcome';
  END IF;
END;
$$;

-- Create update_category_coupon_counts function with proper search_path
CREATE OR REPLACE FUNCTION public.update_category_coupon_counts()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Update coupon counts for all categories
  UPDATE public.categories
  SET coupon_count = (
    SELECT COUNT(*)
    FROM public.coupons
    WHERE category_id = categories.id
    AND status = 'active'
  );
END;
$$;

-- Create update_coupon_counters function with proper search_path
CREATE OR REPLACE FUNCTION public.update_coupon_counters(
  p_coupon_id UUID,
  p_interaction_type TEXT
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Update the appropriate counter based on interaction type
  CASE p_interaction_type
    WHEN 'view' THEN
      UPDATE public.coupons
      SET view_count = COALESCE(view_count, 0) + 1
      WHERE id = p_coupon_id;
    WHEN 'copy' THEN
      UPDATE public.coupons
      SET copy_count = COALESCE(copy_count, 0) + 1
      WHERE id = p_coupon_id;
    WHEN 'use' THEN
      UPDATE public.coupons
      SET use_count = COALESCE(use_count, 0) + 1
      WHERE id = p_coupon_id;
  END CASE;

  -- Update conversion rate
  UPDATE public.coupons
  SET conversion_rate = CASE
    WHEN COALESCE(view_count, 0) > 0 THEN
      COALESCE(use_count, 0)::NUMERIC / COALESCE(view_count, 0)::NUMERIC
    ELSE 0
  END
  WHERE id = p_coupon_id;
END;
$$;

-- Create update_profile_completeness function with proper search_path
CREATE OR REPLACE FUNCTION public.update_profile_completeness(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  completeness_score INTEGER := 0;
  profile_record RECORD;
BEGIN
  -- Get the profile record
  SELECT * INTO profile_record
  FROM public.profiles
  WHERE id = p_user_id;

  IF profile_record.id IS NULL THEN
    RETURN;
  END IF;

  -- Calculate completeness score
  -- Base score for having a profile
  completeness_score := 10;

  -- Add points for each completed field
  IF profile_record.full_name IS NOT NULL AND LENGTH(TRIM(profile_record.full_name)) > 0 THEN
    completeness_score := completeness_score + 15;
  END IF;

  IF profile_record.username IS NOT NULL AND LENGTH(TRIM(profile_record.username)) > 0 THEN
    completeness_score := completeness_score + 10;
  END IF;

  IF profile_record.bio IS NOT NULL AND LENGTH(TRIM(profile_record.bio)) > 0 THEN
    completeness_score := completeness_score + 15;
  END IF;

  IF profile_record.avatar_url IS NOT NULL AND LENGTH(TRIM(profile_record.avatar_url)) > 0 THEN
    completeness_score := completeness_score + 10;
  END IF;

  IF profile_record.website IS NOT NULL AND LENGTH(TRIM(profile_record.website)) > 0 THEN
    completeness_score := completeness_score + 10;
  END IF;

  -- Check for social links
  IF EXISTS (SELECT 1 FROM public.social_links WHERE profile_id = p_user_id) THEN
    completeness_score := completeness_score + 15;
  END IF;

  -- Check for payment info (if influencer)
  IF profile_record.role = 'influencer' THEN
    IF profile_record.payment_email IS NOT NULL AND LENGTH(TRIM(profile_record.payment_email)) > 0 THEN
      completeness_score := completeness_score + 15;
    END IF;
  ELSE
    -- Non-influencers get the payment points automatically
    completeness_score := completeness_score + 15;
  END IF;

  -- Cap at 100%
  completeness_score := LEAST(completeness_score, 100);

  -- Update the profile
  UPDATE public.profiles
  SET profile_complete_percent = completeness_score
  WHERE id = p_user_id;
END;
$$;

-- ============================================================================
-- 7. CREATE TRIGGERS FOR AUTOMATIC FUNCTION CALLS
-- ============================================================================

-- Create trigger to handle new user setup
CREATE OR REPLACE TRIGGER trigger_handle_new_user
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Create trigger to update coupon counters on interactions
CREATE OR REPLACE TRIGGER trigger_update_coupon_counters
  AFTER INSERT ON public.coupon_interactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_coupon_counters(NEW.coupon_id, NEW.interaction_type);

-- Create trigger to update profile completeness on profile changes
CREATE OR REPLACE TRIGGER trigger_update_profile_completeness
  AFTER UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_profile_completeness(NEW.id);
