@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  
  --primary: 164 86% 29%;
  --primary-foreground: 210 40% 98%;
  
  --secondary: 43 100% 46%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  
  --accent: 327 78% 37%;
  --accent-foreground: 222.2 47.4% 11.2%;
  
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  
  --radius: 0.5rem;
  
  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 164 86% 29%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 327 78% 37%;
  --sidebar-accent-foreground: 222.2 47.4% 11.2%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  
  --primary: 164 86% 29%;
  --primary-foreground: 222.2 47.4% 11.2%;
  
  --secondary: 43 100% 46%;
  --secondary-foreground: 210 40% 98%;
  
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  
  --accent: 327 78% 37%;
  --accent-foreground: 210 40% 98%;
  
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  
  --sidebar-background: 222.2 84% 4.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 164 86% 29%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 327 78% 37%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --sidebar-ring: 212.7 26.8% 83.9%;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* SEO-friendly content styles */
.content h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.content h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.content h3 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.content p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.content ul, .content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.content li {
  margin-bottom: 0.5rem;
  line-height: 1.75;
}

/* Coupon card styles */
.coupon-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(243, 244, 246, 0.7) 100%);
  backdrop-filter: blur(10px);
  position: relative;
}

.coupon-card::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.3), rgba(107, 114, 128, 0.5));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  pointer-events: none;
}

.coupon-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.2), 0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

.coupon-card:hover::before {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.4), rgba(75, 85, 99, 0.6));
}

.code-box {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.05em;
  transition: all 0.2s ease;
}

.code-box:hover {
  background-color: #f9fafb;
}

/* Brand stats highlight */
.stat-box {
  transition: all 0.3s ease;
}

.stat-box:hover {
  background-color: #f9fafb;
  transform: translateY(-2px);
}

/* SEO-enhanced FAQ styling */
details {
  transition: all 0.3s ease;
}

details[open] {
  background-color: #f9fafb;
}

details summary {
  cursor: pointer;
  position: relative;
  padding-right: 2rem;
}

details summary::after {
  content: '+';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  color: var(--primary);
  transition: all 0.3s ease;
}

details[open] summary::after {
  content: '−';
}

/* Breadcrumb navigation */
.breadcrumbs {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumbs li {
  display: inline-flex;
  align-items: center;
}

.breadcrumbs li:not(:last-child)::after {
  content: "/";
  margin: 0 0.5rem;
  color: var(--muted-foreground);
}

.breadcrumbs a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
} 