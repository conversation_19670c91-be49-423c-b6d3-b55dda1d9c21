import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import { generateKeywords } from '../data/keywords';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  type?: 'website' | 'article';
  canonicalPath?: string;
  noindex?: boolean;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  alternateUrls?: { [key: string]: string };
  breadcrumbs?: Array<{ name: string; url: string }>;
  brandName?: string;
  category?: string;
  pageType?: 'homepage' | 'brand' | 'category' | 'deals' | 'blog' | 'search';
  enhancedKeywords?: boolean;
}

/**
 * Enhanced SEO component that handles all metadata needs
 * Improved to include comprehensive coupon and influencer-related keywords
 */
const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  image = '/images/couponlink-search-preview.png',
  type = 'website',
  canonicalPath,
  noindex = false,
  publishedTime,
  modifiedTime,
  author = 'CouponLink',
  section,
  alternateUrls,
  breadcrumbs,
  brandName,
  category,
  pageType = 'homepage',
  enhancedKeywords = true
}) => {
  const location = useLocation();

  // Generate comprehensive keywords if enhanced keywords is enabled
  const finalKeywords = enhancedKeywords
    ? generateKeywords(pageType, brandName, category) + (keywords ? `, ${keywords}` : '')
    : keywords || '';

  // Ensure canonicalPath starts with a forward slash
  const formattedCanonicalPath = canonicalPath
    ? (canonicalPath.startsWith('/') ? canonicalPath : `/${canonicalPath}`)
    : location.pathname;
  
  // Construct the full canonical URL
  const canonical = `https://www.couponlink.in${formattedCanonicalPath}`;
  
  // Ensure image is a full URL
  const fullImageUrl = image.startsWith('http') 
    ? image 
    : `https://www.couponlink.in${image.startsWith('/') ? image : `/${image}`}`;

  // Generate breadcrumb schema if provided
  const breadcrumbSchema = breadcrumbs ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  } : null;
  
  return (
    <Helmet>
      {/* Basic SEO */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {finalKeywords && <meta name="keywords" content={finalKeywords} />}
      <meta name="author" content={author} />

      {/* Enhanced SEO for coupon searches */}
      <meta name="subject" content="Coupon Codes, Promo Codes, Discount Deals" />
      <meta name="topic" content="Online Shopping Savings and Deals" />
      <meta name="summary" content={description} />
      <meta name="classification" content="Shopping, Coupons, Deals, Discounts" />
      <meta name="category" content="E-commerce, Savings, Promotional Codes" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="rating" content="General" />

      {/* Influencer and social media specific */}
      <meta name="social-media-friendly" content="true" />
      <meta name="link-in-bio-compatible" content="true" />
      <meta name="influencer-friendly" content="true" />
      <meta name="creator-codes-available" content="true" />

      {/* Canonical link */}
      <link rel="canonical" href={canonical} />
      
      {/* Alternate language/region URLs */}
      {alternateUrls && Object.entries(alternateUrls).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}
      
      {/* Robots control */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <>
          <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
          <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
          <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
        </>
      )}
      
      {/* Article specific metadata */}
      {type === 'article' && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          <meta property="article:author" content={author} />
        </>
      )}
      
      {/* OpenGraph tags for social sharing */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:type" content="image/png" />
      <meta property="og:image:alt" content="CouponLink - Find & Share Verified Coupon Codes and Promo Offers" />
      <meta property="og:url" content={canonical} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="CouponLink" />
      <meta property="og:locale" content="en_US" />

      {/* Enhanced Open Graph for coupons and deals */}
      <meta property="og:price:currency" content="USD" />
      <meta property="og:availability" content="instock" />
      <meta property="product:category" content="Coupons & Deals" />
      <meta property="product:condition" content="new" />
      <meta property="business:contact_data:street_address" content="Online Platform" />
      <meta property="business:contact_data:locality" content="Global" />
      <meta property="business:contact_data:region" content="Worldwide" />
      <meta property="business:contact_data:country_name" content="International" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@CouponLink" />
      <meta name="twitter:creator" content="@CouponLink" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:image:alt" content="CouponLink - Find & Share Verified Coupon Codes and Promo Offers" />

      {/* Enhanced Twitter metadata for deals */}
      <meta name="twitter:label1" content="Category" />
      <meta name="twitter:data1" content="Coupons & Deals" />
      <meta name="twitter:label2" content="Updated" />
      <meta name="twitter:data2" content="Daily" />
      <meta name="twitter:app:name:iphone" content="CouponLink" />
      <meta name="twitter:app:name:ipad" content="CouponLink" />
      <meta name="twitter:app:name:googleplay" content="CouponLink" />
      
      {/* Viewport settings */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      
      {/* Mobile web app capability */}
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Breadcrumb schema if provided */}
      {breadcrumbSchema && (
        <script type="application/ld+json">
          {JSON.stringify(breadcrumbSchema)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO; 