-- Fix Auth Security Settings Migration
-- This migration addresses the auth security issues identified by Supabase linter:
-- 1. Reduce OTP expiry time to under 1 hour
-- 2. Enable leaked password protection

-- ============================================================================
-- AUTH CONFIGURATION FIXES
-- ============================================================================

-- Note: These settings need to be configured through the Supabase Dashboard
-- or via the Management API, not through SQL migrations.
-- This file serves as documentation of the required changes.

-- REQUIRED MANUAL CONFIGURATION STEPS:
-- 
-- 1. OTP EXPIRY CONFIGURATION:
--    - Go to Supabase Dashboard > Authentication > Settings
--    - Set "Email OTP expiry" to 3600 seconds (1 hour) or less
--    - Recommended: 1800 seconds (30 minutes)
--
-- 2. LEAKED PASSWORD PROTECTION:
--    - Go to Supabase Dashboard > Authentication > Settings
--    - Enable "Leaked password protection"
--    - This will check passwords against HaveIBeenPwned.org database

-- ============================================================================
-- HELPER FUNCTIONS FOR AUTH SETTINGS VALIDATION
-- ============================================================================

-- Create a function to validate auth settings (for monitoring purposes)
CREATE OR REPLACE FUNCTION public.validate_auth_security_settings()
RETURNS TABLE (
  setting_name TEXT,
  current_value TEXT,
  recommended_value TEXT,
  is_secure BOOLEAN,
  description TEXT
) 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    'otp_expiry'::TEXT as setting_name,
    'Check Dashboard'::TEXT as current_value,
    '≤ 3600 seconds'::TEXT as recommended_value,
    false as is_secure,
    'OTP expiry should be set to 1 hour or less for security'::TEXT as description
  UNION ALL
  SELECT 
    'leaked_password_protection'::TEXT as setting_name,
    'Check Dashboard'::TEXT as current_value,
    'Enabled'::TEXT as recommended_value,
    false as is_secure,
    'Leaked password protection should be enabled to prevent use of compromised passwords'::TEXT as description;
END;
$$;

-- Create a function to log security configuration checks
CREATE OR REPLACE FUNCTION public.log_security_check()
RETURNS void 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Log that security settings should be manually verified
  INSERT INTO public.system_logs (
    log_level,
    message,
    details,
    created_at
  ) VALUES (
    'WARNING',
    'Auth security settings require manual verification',
    jsonb_build_object(
      'required_actions', jsonb_build_array(
        'Set OTP expiry to ≤ 3600 seconds',
        'Enable leaked password protection'
      ),
      'dashboard_path', 'Authentication > Settings'
    ),
    now()
  );
EXCEPTION
  WHEN undefined_table THEN
    -- If system_logs table doesn't exist, just continue
    NULL;
END;
$$;

-- Run the security check log
SELECT public.log_security_check();

-- ============================================================================
-- COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION public.validate_auth_security_settings() IS 
'Function to validate auth security settings. Returns recommendations for secure configuration.';

COMMENT ON FUNCTION public.log_security_check() IS 
'Function to log security configuration requirements that need manual verification.';
