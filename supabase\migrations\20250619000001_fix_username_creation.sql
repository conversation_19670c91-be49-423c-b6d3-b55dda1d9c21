-- Fix username creation issue in handle_new_user function
-- This migration ensures that the username from metadata is used during signup

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Only create profile if it doesn't already exist (to avoid conflicts with manual creation)
  IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = NEW.id) THEN
    -- Create profile entry using username from metadata if available
    INSERT INTO profiles (id, username, full_name, avatar_url, onboarding_step, onboarding_completed, is_new_user, role, profile_complete_percent, is_verified)
    VALUES (
      NEW.id,
      COALESCE(NEW.raw_user_meta_data->>'username', LOW<PERSON>(SPLIT_PART(NEW.email, '@', 1))),
      COALESCE(NEW.raw_user_meta_data->>'full_name', SPLIT_PART(NEW.email, '@', 1)),
      NEW.raw_user_meta_data->>'avatar_url',
      'welcome',
      false,
      true,
      'user',
      0,
      false
    );
  END IF;
  
  -- Initialize user wallet
  INSERT INTO user_wallets (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$;

-- Recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS trigger_handle_new_user ON auth.users;
CREATE TRIGGER trigger_handle_new_user
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();
