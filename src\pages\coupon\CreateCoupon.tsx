import { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/AuthContext';
import { useOnboarding } from '@/context/OnboardingContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Plus, Tag, Clock, DollarSign, Percent, ArrowLeft, Star, ArrowRight, PlusCircle, Sparkles, Shield, Search, Check, X, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import BackButton from '@/components/BackButton';
import { COLORS } from '@/constants/theme';
import { motion, AnimatePresence } from 'framer-motion';
import PageContainer from '@/components/layout/PageContainer';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { createPortal } from 'react-dom';

// Create portal components for mobile modals
const MobileBrandModal = ({ 
  isOpen, 
  onClose, 
  brands, 
  loading, 
  brandId, 
  setBrandId, 
  search, 
  setSearch 
}) => {
  if (!isOpen) return null;
  
  return createPortal(
    <div className="fixed inset-0 bg-black/70 z-[9999] flex items-center justify-center p-4 touch-none">
      <div className="bg-white w-full max-w-md rounded-lg shadow-xl overflow-hidden" 
           onClick={e => e.stopPropagation()}>
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="font-semibold text-lg">Select Brand</h3>
          <button 
            className="h-8 w-8 rounded-full flex items-center justify-center hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="Search brands..."
              className="w-full h-12 pl-10 pr-4 rounded-md bg-gray-50 text-base"
            />
          </div>
        </div>
        
        <div className="max-h-[60vh] overflow-y-auto p-1">
          {loading ? (
            <div className="p-6 text-center">Loading brands...</div>
          ) : brands.length > 0 ? (
            <div className="p-2 grid gap-1">
              {brands.map(brand => (
                <div
                  key={brand.id}
                  className={`p-4 rounded-md cursor-pointer flex items-center justify-between ${
                    brandId === brand.id 
                      ? 'bg-primary/10 text-primary font-medium' 
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setBrandId(brand.id);
                    onClose();
                  }}
                >
                  <span className="text-base">{brand.name}</span>
                  {brandId === brand.id && <Check className="h-5 w-5" />}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <p className="text-gray-600 mb-3">No brands found</p>
              <p className="text-sm text-gray-400 mb-4">Try a different search term or create a new brand</p>
              <Link
                to={`/brands/create?name=${encodeURIComponent(search)}&returnTo=create-coupon`}
                className="inline-flex items-center gap-1 text-primary hover:text-primary/80"
              >
                <Plus className="h-4 w-4" />
                Create "{search}" as new brand
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};

const MobileCategoryModal = ({ 
  isOpen, 
  onClose, 
  categories, 
  loading, 
  categoryId, 
  setCategoryId, 
  search, 
  setSearch 
}) => {
  if (!isOpen) return null;
  
  return createPortal(
    <div className="fixed inset-0 bg-black/70 z-[9999] flex items-center justify-center p-4 touch-none">
      <div className="bg-white w-full max-w-md rounded-lg shadow-xl overflow-hidden" 
           onClick={e => e.stopPropagation()}>
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="font-semibold text-lg">Select Category</h3>
          <button 
            className="h-8 w-8 rounded-full flex items-center justify-center hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="Search categories..."
              className="w-full h-12 pl-10 pr-4 rounded-md bg-gray-50 text-base"
            />
          </div>
        </div>
        
        <div className="max-h-[60vh] overflow-y-auto p-1">
          {loading ? (
            <div className="p-6 text-center">Loading categories...</div>
          ) : categories.length > 0 ? (
            <div className="p-2 grid gap-1">
              {categories.map(category => (
                <div
                  key={category.id}
                  className={`p-4 rounded-md cursor-pointer flex items-center justify-between ${
                    categoryId === category.id 
                      ? 'bg-accent/10 text-accent font-medium' 
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => {
                    setCategoryId(category.id);
                    onClose();
                  }}
                >
                  <span className="text-base">{category.name}</span>
                  {categoryId === category.id && <Check className="h-5 w-5" />}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <p>No categories found</p>
            </div>
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};

const CreateCoupon = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // We no longer need to handle onboarding specifically in this component
  // Our OnboardingBanner will handle displaying the banner if needed
  
  const [title, setTitle] = useState('');
  const [code, setCode] = useState('');
  const [discountPercent, setDiscountPercent] = useState('');
  const [discountDescription, setDiscountDescription] = useState('');
  const [brandId, setBrandId] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [featured, setFeatured] = useState(false);
  const [isPremium, setIsPremium] = useState(false);
  const [price, setPrice] = useState('');
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(undefined);
  const [submitting, setSubmitting] = useState(false);
  const [step, setStep] = useState(1); // For multi-step form experience

  // Debug step changes
  useEffect(() => {
    console.log('Step changed to:', step);
    console.log('Rendering navigation buttons, step:', step, 'step > 1:', step > 1, 'step < 3:', step < 3);
    console.log('Steps array:', steps);
    console.log('Current step color:', steps[step-1]?.color);
  }, [step]);
  
  // Search state with debounce
  const [brandSearch, setBrandSearch] = useState('');
  const [categorySearch, setCategorySearch] = useState('');
  const [debouncedBrandSearch, setDebouncedBrandSearch] = useState('');
  const [debouncedCategorySearch, setDebouncedCategorySearch] = useState('');
  const [isBrandPopoverOpen, setIsBrandPopoverOpen] = useState(false);
  const [isCategoryPopoverOpen, setIsCategoryPopoverOpen] = useState(false);
  
  // Modal state for mobile
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  
  // Fetch brands with caching
  const { data: brands, isLoading: brandsLoading } = useQuery({
    queryKey: ['brands'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('brands')
        .select('id, name, logo_url')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Fetch categories with caching
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
  
  // Debounce function for search fields
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedBrandSearch(brandSearch);
    }, 300);
    
    return () => clearTimeout(timerId);
  }, [brandSearch]);
  
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedCategorySearch(categorySearch);
    }, 300);
    
    return () => clearTimeout(timerId);
  }, [categorySearch]);
  
  // Enhanced filtering with debouncing
  const filteredBrands = brands?.filter(brand => 
    brand.name.toLowerCase().includes(debouncedBrandSearch.toLowerCase())
  ) || [];

  const filteredCategories = categories?.filter(category => 
    category.name.toLowerCase().includes(debouncedCategorySearch.toLowerCase())
  ) || [];
  
  // Handle returning from brand creation
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const newBrandId = urlParams.get('brandId');

    if (newBrandId && brands && brands.length > 0) {
      // Check if the brand exists in our list
      const newBrand = brands.find(brand => brand.id === newBrandId);
      if (newBrand) {
        setBrandId(newBrandId);
        toast.success(`Brand "${newBrand.name}" selected successfully!`);

        // Clean up the URL by removing the brandId parameter
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('brandId');
        window.history.replaceState({}, '', newUrl.toString());
      }
    }
  }, [location.search, brands]);

  // Get selected item details for display
  const selectedBrand = brands?.find(brand => brand.id === brandId);
  const selectedCategory = categories?.find(category => category.id === categoryId);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted! Current step:', step);
    console.log('Form data:', { title, code, brandId, categoryId, discountPercent, discountDescription });
    console.log('User:', user);

    if (!user) {
      console.error('No user found!');
      toast.error('You must be logged in to create coupons');
      return;
    }

    console.log('User authenticated, user ID:', user.id);

    // Validate required fields
    console.log('Starting validation...');
    console.log('Validation data:', { title: title?.trim(), code: code?.trim(), brandId, categoryId });

    if (!title?.trim()) {
      console.error('Validation failed: No title');
      toast.error('Please enter a coupon title');
      return;
    }

    if (!code?.trim()) {
      console.error('Validation failed: No code');
      toast.error('Please enter a coupon code');
      return;
    }

    if (!brandId) {
      console.error('Validation failed: No brand selected');
      toast.error('Please select a brand');
      return;
    }

    if (!categoryId) {
      console.error('Validation failed: No category selected');
      toast.error('Please select a category');
      return;
    }

    // Validate code format
    if (code.trim().length < 3) {
      console.error('Validation failed: Code too short');
      toast.error('Coupon code must be at least 3 characters long');
      return;
    }

    console.log('All validation passed!');

    // Premium feature is disabled for now
    // if (isPremium && (!price || parseFloat(price) <= 0)) {
    //   toast.error('Premium coupons must have a valid price');
    //   return;
    // }

    console.log('Setting submitting to true...');
    setSubmitting(true);

    try {
      // Prepare the coupon data
      const couponData = {
        title: title.trim(),
        code: code.trim().toUpperCase(),
        discount_percent: discountPercent ? parseInt(discountPercent, 10) : null,
        discount_description: discountDescription?.trim() || null,
        brand_id: brandId,
        category_id: categoryId,
        influencer_id: user.id,
        featured: featured,
        is_premium: false, // Premium feature disabled for now
        price: null, // Premium feature disabled for now
        expires_at: expiryDate ? expiryDate.toISOString() : null,
        status: 'active' // Explicitly set status
      };

      console.log('Coupon data prepared:', couponData);
      console.log('About to call Supabase...');

      // Use single insert without array wrapper for better performance
      const { data, error } = await supabase
        .from('coupons')
        .insert(couponData)
        .select('id, title, code')
        .single();

      console.log('Supabase response:', { data, error });

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Coupon created successfully:', data);
      toast.success('Coupon created successfully!');

      // Navigate to profile after creating a coupon
      console.log('Navigating to profile...');
      navigate('/profile');
    } catch (error: any) {
      console.error('Error creating coupon:', error);

      // Provide more specific error messages
      if (error?.code === '23505') {
        toast.error('A coupon with this code already exists. Please use a different code.');
      } else if (error?.code === '23503') {
        toast.error('Invalid brand or category selected. Please refresh and try again.');
      } else if (error?.message?.includes('500') || error?.status === 500) {
        toast.error('Server error occurred. Please try again in a moment.');
        console.error('Server error details:', error);
      } else if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
        toast.error('Network error. Please check your connection and try again.');
      } else if (error?.message?.includes('timeout')) {
        toast.error('Request timed out. Please try again.');
      } else if (error?.message) {
        toast.error(`Failed to create coupon: ${error.message}`);
      } else {
        toast.error('Failed to create coupon. Please try again.');
      }
    } finally {
      console.log('Setting submitting to false...');
      setSubmitting(false);
    }
  };

  // Handle navigation between steps
  const handleNextStep = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('Next button clicked, current step:', step, 'will go to:', step + 1);
    if (step < 3) {
      const newStep = step + 1;
      setStep(newStep);
      console.log('Step updated to:', newStep);
      // Force a small delay to ensure state is updated
      setTimeout(() => {
        console.log('Step state after update:', newStep);
      }, 100);
    } else {
      console.log('Already at last step');
    }
  };

  const handlePreviousStep = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('Previous button clicked, current step:', step, 'will go to:', step - 1);
    if (step > 1) {
      const newStep = step - 1;
      setStep(newStep);
      console.log('Step updated to:', newStep);
      // Force a small delay to ensure state is updated
      setTimeout(() => {
        console.log('Step state after update:', newStep);
      }, 100);
    } else {
      console.log('Already at first step');
    }
  };
  
  // For progress steps
  const steps = [
    { id: 1, title: 'Basic Info', icon: <Tag className="w-4 h-4" />, color: COLORS.primary.gradient },
    { id: 2, title: 'Discount Details', icon: <Percent className="w-4 h-4" />, color: COLORS.secondary.gradient },
    { id: 3, title: 'Options', icon: <Star className="w-4 h-4" />, color: COLORS.accent.gradient }
  ];
  
  return (
    <MainLayout>
      <PageContainer>
        {/* Mobile Modals */}
        <MobileBrandModal 
          isOpen={showBrandModal}
          onClose={() => setShowBrandModal(false)}
          brands={filteredBrands}
          loading={brandsLoading}
          brandId={brandId}
          setBrandId={setBrandId}
          search={brandSearch}
          setSearch={setBrandSearch}
        />
        
        <MobileCategoryModal 
          isOpen={showCategoryModal}
          onClose={() => setShowCategoryModal(false)}
          categories={filteredCategories}
          loading={categoriesLoading}
          categoryId={categoryId}
          setCategoryId={setCategoryId}
          search={categorySearch}
          setSearch={setCategorySearch}
        />
        
        {/* Top navigation with better mobile spacing */}
        <div className="flex flex-wrap items-center justify-between mb-8 gap-4">
          <div className="flex items-center gap-2">
            <BackButton
              label="Back to Coupons"
            />
          </div>

          <div className="flex items-center space-x-1 text-sm font-medium px-4 py-1.5 rounded-full shadow-sm"
            style={{
              background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
              color: COLORS.neutral[600]
            }}
          >
            {steps.map((s, idx) => (
              <div key={s.id} className="flex items-center">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center cursor-pointer ${step >= s.id ? 'text-white' : 'text-gray-400'}`}
                  style={{
                    background: step >= s.id ? s.color : COLORS.neutral[200]
                  }}
                  onClick={() => s.id <= Math.max(step, 1) && setStep(s.id)}
                >
                  {s.icon}
                </div>
                {idx < steps.length - 1 && (
                  <div className="w-8 h-0.5 mx-1" style={{ background: step > s.id ? COLORS.neutral[400] : COLORS.neutral[200] }}></div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* Page title with gradient */}
        <div className="text-center mb-10">
          <motion.h1
            className="text-3xl sm:text-4xl font-bold mb-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            style={{ color: COLORS.neutral[800] }}
          >
            Create New Coupon
          </motion.h1>

        </div>
            
        {/* Form content */}
        <motion.form
          onSubmit={(e) => {
            console.log('Form onSubmit triggered!');
            handleSubmit(e);
          }}
          className="max-w-5xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Title and Code Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                    style={{ 
                      background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`,
                      border: `1px solid rgba(255, 255, 255, 0.2)`
                    }}
                  >
                    <Label htmlFor="title" className="text-base font-medium mb-2 block" style={{ color: COLORS.neutral[800] }}>Coupon Title*</Label>
                    <Input 
                      id="title" 
                      value={title} 
                      onChange={(e) => setTitle(e.target.value)} 
                      placeholder="e.g., Summer Sale 20% Off"
                      required
                      className="bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base"
                      style={{ 
                        borderColor: COLORS.primary.light,
                        color: COLORS.neutral[800]
                      }}
                    />
                    <p className="mt-2 text-xs" style={{ color: COLORS.neutral[500] }}>
                      Provide a descriptive title for your coupon
                    </p>
                  </div>
                  
                  <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                    style={{ 
                      background: `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`,
                      border: `1px solid rgba(255, 255, 255, 0.2)`
                    }}
                  >
                    <Label htmlFor="code" className="text-base font-medium mb-2 block" style={{ color: COLORS.neutral[800] }}>Coupon Code*</Label>
                    <Input 
                      id="code" 
                      value={code} 
                      onChange={(e) => setCode(e.target.value)} 
                      placeholder="e.g., SUMMER20"
                      required
                      className="bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm text-base uppercase"
                      style={{ 
                        borderColor: COLORS.tertiary.light,
                        color: COLORS.neutral[800]
                      }}
                    />
                    <p className="mt-2 text-xs" style={{ color: COLORS.neutral[500] }}>
                      Enter a unique, memorable code for your coupon
                    </p>
                  </div>
                </div>
                
                {/* Brand & Category Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                    style={{ 
                      background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                      border: `1px solid rgba(255, 255, 255, 0.2)`
                    }}
                  >
                    <div className="flex justify-between items-center mb-3">
                      <Label htmlFor="brandId" className="text-base font-medium" style={{ color: COLORS.neutral[800] }}>Brand*</Label>
                      <Link
                        to="/brands/create?returnTo=create-coupon"
                        className="text-sm flex items-center hover:underline group"
                        style={{ color: COLORS.secondary.main }}
                      >
                        <div className="w-5 h-5 rounded-full flex items-center justify-center mr-1 group-hover:scale-110 transition-transform"
                          style={{ background: COLORS.secondary.bgLight }}
                        >
                          <Plus className="h-3 w-3" style={{ color: COLORS.secondary.main }} />
                        </div>
                        <span>Add new brand</span>
                      </Link>
                    </div>
                    
                    <div className="relative">
                      <div
                        onClick={() => setShowBrandModal(true)}
                        className="w-full flex justify-between items-center bg-white/90 border shadow-sm md:hidden h-14 text-base px-4 py-3 rounded-md cursor-pointer active:bg-gray-50"
                        style={{ borderColor: COLORS.secondary.light }}
                      >
                        <span>{selectedBrand ? selectedBrand.name : "Select a brand"}</span>
                        <Search className="ml-2 h-5 w-5 shrink-0 opacity-50" />
                      </div>
                      
                      <div className="hidden md:block">
                        <Popover open={isBrandPopoverOpen} onOpenChange={setIsBrandPopoverOpen}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isBrandPopoverOpen}
                              className="w-full justify-between bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm"
                              style={{ borderColor: COLORS.secondary.light }}
                            >
                              {selectedBrand ? selectedBrand.name : "Select a brand"}
                              <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0 bg-white/95 backdrop-blur-sm border-0 shadow-lg">
                            <div className="flex flex-col">
                              <div className="flex items-center border-b px-3 py-2">
                                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                                <input
                                  placeholder="Search brands..."
                                  value={brandSearch}
                                  onChange={(e) => setBrandSearch(e.target.value)}
                                  className="flex h-10 w-full bg-transparent py-2 text-sm outline-none placeholder:text-gray-400"
                                />
                                {brandSearch && (
                                  <button 
                                    onClick={() => setBrandSearch('')}
                                    className="rounded-full p-1 hover:bg-gray-100"
                                  >
                                    <X className="h-3 w-3 opacity-50" />
                                  </button>
                                )}
                              </div>
                              
                              <div className="max-h-[300px] overflow-y-auto p-1">
                                {brandsLoading ? (
                                  <div className="p-4 text-center text-sm text-gray-500">Loading brands...</div>
                                ) : filteredBrands.length > 0 ? (
                                  filteredBrands.map((brand) => (
                                    <div
                                      key={brand.id}
                                      className={`flex items-center justify-between w-full py-2 px-3 my-0.5 cursor-pointer rounded-md active:bg-gray-200 ${
                                        brandId === brand.id ? 'bg-primary/10 text-primary font-medium' : 'hover:bg-gray-100'
                                      }`}
                                      onClick={() => {
                                        console.log('Selected brand:', brand.id);
                                        setBrandId(brand.id);
                                        setIsBrandPopoverOpen(false);
                                      }}
                                    >
                                      <span>{brand.name}</span>
                                      {brandId === brand.id && (
                                        <Check className="h-4 w-4 text-primary" />
                                      )}
                                    </div>
                                  ))
                                ) : (
                                  <div className="flex flex-col items-center justify-center text-center py-6">
                                    <p className="text-sm text-gray-500">No brands found</p>
                                    <p className="text-xs text-gray-400 mt-1 mb-3">Try a different search term or create a new brand</p>
                                    <Link
                                      to={`/brands/create?name=${encodeURIComponent(brandSearch)}&returnTo=create-coupon`}
                                      className="text-sm text-primary hover:text-primary/80 flex items-center gap-1"
                                    >
                                      <Plus className="h-4 w-4" />
                                      Create "{brandSearch}" as new brand
                                    </Link>
                                  </div>
                                )}
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>
                  
                  <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                    style={{ 
                      background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                      border: `1px solid rgba(255, 255, 255, 0.2)`
                    }}
                  >
                    <Label htmlFor="categoryId" className="text-base font-medium mb-3 block" style={{ color: COLORS.neutral[800] }}>Category*</Label>
                    
                    <div className="relative">
                      <div
                        onClick={() => setShowCategoryModal(true)}
                        className="w-full flex justify-between items-center bg-white/90 border shadow-sm md:hidden h-14 text-base px-4 py-3 rounded-md cursor-pointer active:bg-gray-50"
                        style={{ borderColor: COLORS.accent.light }}
                      >
                        <span>{selectedCategory ? selectedCategory.name : "Select a category"}</span>
                        <Search className="ml-2 h-5 w-5 shrink-0 opacity-50" />
                      </div>
                      
                      <div className="hidden md:block">
                        <Popover open={isCategoryPopoverOpen} onOpenChange={setIsCategoryPopoverOpen}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isCategoryPopoverOpen}
                              className="w-full justify-between bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm"
                              style={{ borderColor: COLORS.accent.light }}
                            >
                              {selectedCategory ? selectedCategory.name : "Select a category"}
                              <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0 bg-white/95 backdrop-blur-sm border-0 shadow-lg">
                            <div className="flex flex-col">
                              <div className="flex items-center border-b px-3 py-2">
                                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                                <input
                                  placeholder="Search categories..."
                                  value={categorySearch}
                                  onChange={(e) => setCategorySearch(e.target.value)}
                                  className="flex h-10 w-full bg-transparent py-2 text-sm outline-none placeholder:text-gray-400"
                                />
                                {categorySearch && (
                                  <button 
                                    onClick={() => setCategorySearch('')}
                                    className="rounded-full p-1 hover:bg-gray-100"
                                  >
                                    <X className="h-3 w-3 opacity-50" />
                                  </button>
                                )}
                              </div>
                              
                              <div className="max-h-[300px] overflow-y-auto p-1">
                                {categoriesLoading ? (
                                  <div className="p-4 text-center text-sm text-gray-500">Loading categories...</div>
                                ) : filteredCategories.length > 0 ? (
                                  filteredCategories.map((category) => (
                                    <div
                                      key={category.id}
                                      className={`flex items-center justify-between w-full py-2 px-3 my-0.5 cursor-pointer rounded-md active:bg-gray-200 ${
                                        categoryId === category.id ? 'bg-accent/10 text-accent font-medium' : 'hover:bg-gray-100'
                                      }`}
                                      onClick={() => {
                                        console.log('Selected category:', category.id);
                                        setCategoryId(category.id);
                                        setIsCategoryPopoverOpen(false);
                                      }}
                                    >
                                      <span>{category.name}</span>
                                      {categoryId === category.id && (
                                        <Check className="h-4 w-4 text-accent" />
                                      )}
                                    </div>
                                  ))
                                ) : (
                                  <div className="flex flex-col items-center justify-center text-center py-6">
                                    <p className="text-sm text-gray-500">No categories found</p>
                                    <p className="text-xs text-gray-400 mt-1">Try a different search term</p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
            
            {step === 2 && (
              <motion.div 
                key="step2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Discount Section */}
                <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                  style={{ 
                    background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.tertiary.bgLight})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="discountPercent" className="text-base font-medium mb-2 block" style={{ color: COLORS.neutral[800] }}>Discount Percentage</Label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                          <span style={{ color: COLORS.neutral[500] }}>%</span>
                        </div>
                        <Input 
                          id="discountPercent" 
                          type="number" 
                          value={discountPercent} 
                          onChange={(e) => setDiscountPercent(e.target.value)}
                          placeholder="e.g., 20"
                          className="bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm pl-7 text-base"
                          style={{ 
                            borderColor: COLORS.primary.light,
                            color: COLORS.neutral[800]
                          }}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="expiryDate" className="text-base font-medium mb-2 block" style={{ color: COLORS.neutral[800] }}>Expiration Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal border-0 bg-white/90 focus:ring-2 focus:ring-offset-0 shadow-sm"
                            style={{ 
                              borderColor: COLORS.tertiary.light,
                              color: expiryDate ? COLORS.neutral[800] : COLORS.neutral[500]
                            }}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" style={{ color: COLORS.tertiary.main }} />
                            {expiryDate ? format(expiryDate, "PPP") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 border-0 bg-white/95 backdrop-blur-sm shadow-lg">
                          <Calendar
                            mode="single"
                            selected={expiryDate}
                            onSelect={setExpiryDate}
                            initialFocus
                            className="rounded-md"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>
                
                {/* Description Section */}
                <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                  style={{ 
                    background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.accent.bgLight})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <Label htmlFor="discountDescription" className="text-base font-medium mb-2 block" style={{ color: COLORS.neutral[800] }}>Discount Description</Label>
                  <Textarea 
                    id="discountDescription" 
                    value={discountDescription} 
                    onChange={(e) => setDiscountDescription(e.target.value)}
                    placeholder="e.g., 20% off on all summer items, valid for all products except electronics"
                    className="bg-white/90 border-0 focus:ring-2 focus:ring-offset-0 shadow-sm min-h-[120px] text-base"
                    style={{ 
                      borderColor: COLORS.secondary.light,
                      color: COLORS.neutral[800]
                    }}
                  />
                  <p className="mt-2 text-xs flex items-center" style={{ color: COLORS.neutral[500] }}>
                    <Sparkles className="h-3 w-3 mr-1" style={{ color: COLORS.secondary.main }} />
                    Provide clear terms and conditions for your discount
                  </p>
                </div>
              </motion.div>
            )}
            
            {step === 3 && (
              <motion.div 
                key="step3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Feature toggle */}
                <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                  style={{ 
                    background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.tertiary.bgLight})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className="relative mt-1">
                      <Switch 
                        id="featured" 
                        checked={featured} 
                        onCheckedChange={setFeatured}
                        className="bg-white data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-yellow-400 data-[state=checked]:to-amber-500"
                      />
                    </div>
                    <div>
                      <Label htmlFor="featured" className="text-base font-medium block" style={{ color: COLORS.neutral[800] }}>Feature this coupon</Label>
                      <p className="text-xs" style={{ color: COLORS.neutral[500] }}>
                        Featured coupons appear in highlighted sections across the platform and get more visibility
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Premium toggle */}
                <div className="backdrop-blur-sm rounded-xl p-6 shadow-md opacity-60"
                  style={{
                    background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.primary.bgLight})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className="relative mt-1">
                    <Switch
                      id="premium"
                      checked={false}
                      disabled={true}
                      onCheckedChange={() => {
                        toast.info('Premium coupons feature is coming soon! 🚀');
                      }}
                      className="bg-gray-300 cursor-not-allowed"
                    />
                    </div>
                    <div>
                      <Label htmlFor="premium" className="text-base font-medium block cursor-not-allowed" style={{ color: COLORS.neutral[600] }}>
                        Premium coupon (paid access) - Coming Soon
                      </Label>
                      <p className="text-xs" style={{ color: COLORS.neutral[400] }}>
                        Premium coupons will require payment for access and generate revenue for creators
                      </p>
                    </div>
                  </div>

                </div>

                {/* Final Summary */}
                <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                  style={{ 
                    background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <Shield className="w-5 h-5" style={{ color: COLORS.primary.main }} />
                    <h3 className="text-base font-medium" style={{ color: COLORS.neutral[800] }}>Coupon Summary</h3>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-x-6 gap-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm" style={{ color: COLORS.neutral[500] }}>Title:</span>
                      <span className="text-sm font-medium truncate max-w-[160px]" style={{ color: COLORS.neutral[800] }}>{title || "-"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm" style={{ color: COLORS.neutral[500] }}>Code:</span>
                      <span className="text-sm font-medium" style={{ color: COLORS.neutral[800] }}>{code || "-"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm" style={{ color: COLORS.neutral[500] }}>Discount:</span>
                      <span className="text-sm font-medium" style={{ color: COLORS.neutral[800] }}>{discountPercent ? `${discountPercent}%` : "-"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm" style={{ color: COLORS.neutral[500] }}>Expires:</span>
                      <span className="text-sm font-medium" style={{ color: COLORS.neutral[800] }}>{expiryDate ? format(expiryDate, "PP") : "Never"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm" style={{ color: COLORS.neutral[500] }}>Features:</span>
                      <div className="flex gap-1">
                        {featured && <span className="text-xs px-2 py-0.5 rounded-full" style={{ background: COLORS.secondary.bgLight, color: COLORS.secondary.main }}>Featured</span>}
                        {/* Premium feature disabled */}
                        {!featured && <span className="text-sm font-medium" style={{ color: COLORS.neutral[800] }}>-</span>}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation buttons - moved inside form */}
          <div
            key={`nav-buttons-${step}`}
            className="mt-8 mb-40 flex justify-between max-w-5xl mx-auto px-4"
            style={{
              zIndex: 99999,
              pointerEvents: 'auto',
              position: 'relative'
            }}
          >
          {step > 1 ? (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Previous button clicked, current step:', step);
                handlePreviousStep(e);
              }}
              onMouseDown={(e) => {
                console.log('Previous button mouse down');
                e.preventDefault();
              }}
              onMouseUp={(e) => {
                console.log('Previous button mouse up');
                e.preventDefault();
                handlePreviousStep(e);
              }}
              className="flex items-center px-6 py-2.5 rounded-full shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 active:scale-95 md:pointer-events-auto"
              style={{
                background: COLORS.surface.light,
                color: COLORS.neutral[700],
                zIndex: 10000,
                position: 'relative',
                pointerEvents: 'auto',
                cursor: 'pointer'
              }}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </button>
          ) : (
            <div></div>
          )}

          {step < 3 ? (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Next button clicked, current step:', step);
                handleNextStep(e);
              }}
              onMouseDown={(e) => {
                console.log('Next button mouse down');
                e.preventDefault();
              }}
              onMouseUp={(e) => {
                console.log('Next button mouse up');
                e.preventDefault();
                handleNextStep(e);
              }}
              className="flex items-center px-6 py-2.5 rounded-full text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 active:scale-95 md:pointer-events-auto"
              style={{
                background: steps[step-1]?.color || COLORS.primary.gradient,
                zIndex: 10000,
                position: 'relative',
                pointerEvents: 'auto',
                cursor: 'pointer'
              }}
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          ) : (
            <div className="flex gap-3">
              <button
                type="submit"
                onClick={(e) => {
                  console.log('Submit button clicked! Current step:', step);
                  console.log('Form element:', e.target.closest('form'));
                  // Don't prevent default - let form submission happen
                }}
                data-onboarding="create-coupon-btn"
                className="flex items-center px-8 py-2.5 rounded-full text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 active:scale-95 md:pointer-events-auto"
                style={{
                  background: COLORS.primary.gradient,
                  zIndex: 10000,
                  position: 'relative',
                  pointerEvents: 'auto',
                  cursor: 'pointer'
                }}
                disabled={submitting}
              >
                {submitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Coupon...
                  </span>
                ) : (
                  <span className="flex items-center justify-center">
                    Create Coupon
                  <PlusCircle className="w-4 h-4 ml-2" />
                  </span>
                )}
              </button>
            </div>
          )}
          </div>
        </motion.form>
      </PageContainer>
    </MainLayout>
  );
};

export default CreateCoupon;
