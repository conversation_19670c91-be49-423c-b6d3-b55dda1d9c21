
import { Button } from '@/components/ui/button';
import { Lock, Check } from 'lucide-react';
import { usePremiumCoupon } from '@/hooks/usePremiumCoupon';
import { Coupon } from '@/hooks/useCoupons';

interface PremiumCouponButtonProps {
  coupon: Coupon;
}

const PremiumCouponButton = ({ coupon }: PremiumCouponButtonProps) => {
  const { purchaseCoupon, hasPurchased, isLoading } = usePremiumCoupon(coupon.id);
  
  // If it's not a premium coupon, don't render the button
  if (!coupon.is_premium) {
    return null;
  }
  
  // If the user has already purchased the coupon
  if (hasPurchased) {
    return (
      <Button 
        className="w-full flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600"
        disabled
      >
        <Check className="w-4 h-4" />
        Unlocked Premium Coupon
      </Button>
    );
  }
  
  // Format the price 
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(coupon.price || 0);
  
  return (
    <Button 
      onClick={purchaseCoupon}
      className="w-full flex items-center justify-center gap-2 bg-brand-blue hover:bg-brand-blue/90"
      disabled={isLoading}
    >
      <Lock className="w-4 h-4" />
      {isLoading ? 'Processing...' : `Unlock Premium Coupon (${formattedPrice})`}
    </Button>
  );
};

export default PremiumCouponButton;
