/**
 * Utility function to fix domain names in links
 * Ensures all links use the correct domain (couponlink.in) instead of couponlink.com
 */

/**
 * Fixes domain in URLs to ensure couponlink.in is used instead of couponlink.com
 * @param link The URL or path to be processed
 * @returns The link with corrected domain
 */
export const fixDomainName = (link: string): string => {
  if (!link) return link;
  
  // Replace couponlink.com with couponlink.in
  return link.replace(/couponlink\.com/g, 'couponlink.in');
};

/**
 * Opens a URL in a new tab with the correct domain name
 * @param url The URL to open
 */
export const openUrlWithCorrectDomain = (url: string): void => {
  if (!url) return;
  
  const correctedUrl = fixDomainName(url);
  window.open(correctedUrl, '_blank');
};

export default {
  fixDomainName,
  openUrlWithCorrectDomain
}; 