import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
		"!./src/components/onboarding/MenuIndicatorPopup.tsx",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				brand: {
					malachite: {
						DEFAULT: '#5cb474', // Softer green
						100: '#f0f8f1',
						200: '#d8ecdc',
						300: '#b1d9b8',
						400: '#8ac694',
						500: '#5cb474', // Base color
						600: '#49935e',
						700: '#377249',
						800: '#244c31',
						900: '#122618'
					},
					yellow: {
						DEFAULT: '#e6c15c', // Softer yellow
						100: '#fdf8e8',
						200: '#f9ecc6',
						300: '#f2db94',
						400: '#ecd073',
						500: '#e6c15c', // Base color
						600: '#d1a937',
						700: '#a78429',
						800: '#7d621f',
						900: '#543f15'
					},
					pink: {
						DEFAULT: '#c75b7f', // Softer pink
						100: '#f9eaf0',
						200: '#f0cad9',
						300: '#e3a7be',
						400: '#d783a1',
						500: '#c75b7f', // Base color
						600: '#b14367',
						700: '#8b3350',
						800: '#652339',
						900: '#3e1623'
					},
					blue: {
						DEFAULT: '#5c94c7', // Softer blue
						100: '#eaf3fa',
						200: '#cbe0f1',
						300: '#a3cae5',
						400: '#7bb2d9',
						500: '#5c94c7', // Base color
						600: '#4478ad',
						700: '#355d87',
						800: '#254161',
						900: '#15273b'
					},
					chartreuse: {
						DEFAULT: '#a6c75c', // Softer chartreuse
						100: '#f6f9ea',
						200: '#e7efca',
						300: '#d2e2a2',
						400: '#bdd479',
						500: '#a6c75c', // Base color
						600: '#8ba542',
						700: '#6d8234',
						800: '#4e5c25',
						900: '#2f3717'
					}
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'float': {
					'0%, 100%': {
						transform: 'translateY(0)'
					},
					'50%': {
						transform: 'translateY(-10px)'
					}
				},
				'scale-in': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'scale(1)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out forwards',
				'float': 'float 3s ease-in-out infinite',
				'scale-in': 'scale-in 0.3s ease-out forwards'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
