// Avatar utility functions for managing default avatars

// List of available default avatars
export const DEFAULT_AVATARS = [
  '/images/default-avatar.svg', // Cute girl with pink background
  '/images/avatar-1.svg',       // Cool boy with blue background
  '/images/avatar-2.svg',       // Girl with long hair and orange background
  '/images/avatar-3.svg',       // Cool guy with sunglasses
  '/images/avatar-4.svg',       // Sweet girl with curly blonde hair
  '/images/avatar-5.svg',       // Professional man with mustache
  '/images/avatar-6.svg',       // Trendy girl with glasses
  '/images/avatar-7.svg',       // Young boy with freckles
  '/images/avatar-8.svg',       // Elegant woman with earrings
];

/**
 * Get a random default avatar based on user ID or email
 * This ensures the same user always gets the same avatar
 */
export const getDefaultAvatar = (userId?: string, email?: string): string => {
  // Use user ID or email to create a consistent hash
  const identifier = userId || email || 'default';
  
  // Simple hash function to convert string to number
  let hash = 0;
  for (let i = 0; i < identifier.length; i++) {
    const char = identifier.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Get absolute value and use modulo to get index
  const index = Math.abs(hash) % DEFAULT_AVATARS.length;
  
  return DEFAULT_AVATARS[index];
};

/**
 * Get avatar URL with fallback to default
 */
export const getAvatarUrl = (avatarUrl?: string | null, userId?: string, email?: string): string => {
  if (avatarUrl && avatarUrl.trim() !== '') {
    return avatarUrl;
  }
  
  return getDefaultAvatar(userId, email);
};
