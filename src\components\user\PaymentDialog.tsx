import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DollarSign } from 'lucide-react';
import { toast } from 'sonner';

interface PaymentDialogProps {
  paymentId?: string;
  paymentType?: string;
}

const PaymentDialog = ({ paymentId, paymentType }: PaymentDialogProps) => {
  const copyPaymentInfo = () => {
    if (paymentId) {
      navigator.clipboard.writeText(paymentId);
      toast.success('Payment information copied to clipboard');
    }
  };

  if (!paymentId || !paymentType) return null;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <DollarSign className="w-4 h-4 mr-2" />
          Payment Info
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Payment Information</DialogTitle>
          <DialogDescription>
            View and copy payment details for {paymentType}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2">
            <p className="text-sm font-medium">Payment Type</p>
            <p className="text-sm text-muted-foreground">{paymentType}</p>
          </div>
          <div className="flex flex-col space-y-2">
            <p className="text-sm font-medium">Payment ID</p>
            <p className="text-sm text-muted-foreground">{paymentId}</p>
          </div>
          <Button onClick={copyPaymentInfo} className="w-full">
            Copy Payment Information
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentDialog; 