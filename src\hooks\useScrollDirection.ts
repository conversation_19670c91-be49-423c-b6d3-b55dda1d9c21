import { useState, useEffect } from 'react';

interface ScrollState {
  isScrollingUp: boolean;
  isScrollingDown: boolean;
  isPageScrollable: boolean;
  shouldShowTaskbar: boolean;
}

/**
 * Custom hook to detect scroll direction and page scrollability
 * Returns whether taskbar should be visible based on scroll behavior
 */
export const useScrollDirection = (threshold: number = 10): ScrollState => {
  const [scrollState, setScrollState] = useState<ScrollState>(() => ({
    isScrollingUp: false,
    isScrollingDown: false,
    isPageScrollable: typeof window !== 'undefined',
    shouldShowTaskbar: true,
  }));

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }

    let lastScrollY = window.scrollY;
    let ticking = false;

    const checkPageScrollable = (): boolean => {
      // Check if the page content is taller than the viewport
      const documentHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      );
      const windowHeight = window.innerHeight;

      // Page is scrollable if content height > window height + small buffer
      return documentHeight > windowHeight + 50;
    };

    const updateScrollDirection = () => {
      const scrollY = window.scrollY;
      const isPageScrollable = checkPageScrollable();
      
      // If page is not scrollable, always show taskbar
      if (!isPageScrollable) {
        setScrollState({
          isScrollingUp: false,
          isScrollingDown: false,
          isPageScrollable: false,
          shouldShowTaskbar: true,
        });
        ticking = false;
        return;
      }

      // Only update if scroll difference is greater than threshold
      if (Math.abs(scrollY - lastScrollY) < threshold) {
        ticking = false;
        return;
      }

      const isScrollingUp = scrollY < lastScrollY;
      const isScrollingDown = scrollY > lastScrollY;

      // Special case: always show taskbar when at the very top
      const shouldShowTaskbar = scrollY <= 10 || isScrollingDown;

      setScrollState({
        isScrollingUp,
        isScrollingDown,
        isPageScrollable: true,
        shouldShowTaskbar,
      });

      lastScrollY = scrollY;
      ticking = false;
    };

    const requestTick = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollDirection);
        ticking = true;
      }
    };

    const handleScroll = () => {
      requestTick();
    };

    const handleResize = () => {
      // Recheck scrollability on window resize
      requestTick();
    };

    // Initial check
    updateScrollDirection();

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [threshold]);

  return scrollState;
};
