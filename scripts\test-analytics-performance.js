#!/usr/bin/env node

/**
 * Analytics Performance Testing Script
 * 
 * This script tests the performance improvements made to the analytics system.
 * It measures query execution times and provides recommendations.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('   Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Performance testing functions
async function measureQueryTime(queryName, queryFunction) {
  console.log(`📊 Testing ${queryName}...`);
  const startTime = Date.now();
  
  try {
    const result = await queryFunction();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ ${queryName}: ${duration}ms`);
    return { success: true, duration, result };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`❌ ${queryName}: ${duration}ms (ERROR: ${error.message})`);
    return { success: false, duration, error: error.message };
  }
}

// Test functions
async function testOptimizedQueries(userId) {
  const fromDate = new Date();
  fromDate.setDate(fromDate.getDate() - 30);
  const toDate = new Date();
  
  const prevFromDate = new Date(fromDate);
  prevFromDate.setDate(prevFromDate.getDate() - 30);
  const prevToDate = new Date(fromDate);
  prevToDate.setDate(prevToDate.getDate() - 1);

  console.log('\n🚀 Testing Optimized RPC Functions\n');

  // Test optimized interaction stats
  const interactionStatsResult = await measureQueryTime(
    'Optimized Interaction Stats',
    () => supabase.rpc('get_interaction_stats', {
      p_user_id: userId,
      p_from_date: fromDate.toISOString(),
      p_to_date: toDate.toISOString(),
      p_prev_from_date: prevFromDate.toISOString(),
      p_prev_to_date: prevToDate.toISOString()
    })
  );

  // Test optimized revenue stats
  const revenueStatsResult = await measureQueryTime(
    'Optimized Revenue Stats',
    () => supabase.rpc('get_revenue_stats', {
      p_user_id: userId,
      p_from_date: fromDate.toISOString(),
      p_to_date: toDate.toISOString(),
      p_prev_from_date: prevFromDate.toISOString(),
      p_prev_to_date: prevToDate.toISOString()
    })
  );

  // Test daily analytics summary
  const dailyAnalyticsResult = await measureQueryTime(
    'Daily Analytics Summary',
    () => supabase.rpc('get_daily_analytics_summary', {
      p_user_id: userId,
      p_from_date: fromDate.toISOString(),
      p_to_date: toDate.toISOString()
    })
  );

  return {
    interactionStats: interactionStatsResult,
    revenueStats: revenueStatsResult,
    dailyAnalytics: dailyAnalyticsResult
  };
}

async function testLegacyQueries(userId) {
  const fromDate = new Date();
  fromDate.setDate(fromDate.getDate() - 30);
  const toDate = new Date();

  console.log('\n📈 Testing Legacy Query Approach\n');

  // Test multiple separate queries (old approach)
  const legacyResults = await Promise.all([
    measureQueryTime(
      'Legacy Interaction Count',
      () => supabase
        .from('coupon_interactions')
        .select('interaction_type')
        .eq('user_id', userId)
        .gte('occurred_at', fromDate.toISOString())
        .lte('occurred_at', toDate.toISOString())
    ),
    
    measureQueryTime(
      'Legacy Revenue Query',
      () => supabase
        .from('premium_purchases')
        .select('amount, purchased_at')
        .eq('buyer_id', userId)
        .gte('purchased_at', fromDate.toISOString())
        .lte('purchased_at', toDate.toISOString())
    ),
    
    measureQueryTime(
      'Legacy Top Coupons',
      () => supabase
        .from('coupons')
        .select('id, title, code, view_count, copy_count')
        .eq('influencer_id', userId)
        .order('view_count', { ascending: false })
        .limit(5)
    )
  ]);

  return legacyResults;
}

async function testAnalyticsSummaryView() {
  console.log('\n📋 Testing Analytics Summary View\n');

  const summaryResult = await measureQueryTime(
    'Analytics Summary View',
    () => supabase
      .from('analytics_summary')
      .select('*')
      .limit(10)
  );

  return summaryResult;
}

async function generatePerformanceReport(optimizedResults, legacyResults, summaryResult) {
  console.log('\n📊 Performance Report\n');
  console.log('=' * 50);

  // Calculate total times
  const optimizedTotal = optimizedResults.interactionStats.duration + 
                        optimizedResults.revenueStats.duration + 
                        optimizedResults.dailyAnalytics.duration;

  const legacyTotal = legacyResults.reduce((sum, result) => sum + result.duration, 0);

  console.log(`🚀 Optimized Approach Total: ${optimizedTotal}ms`);
  console.log(`📈 Legacy Approach Total: ${legacyTotal}ms`);
  
  if (legacyTotal > 0) {
    const improvement = ((legacyTotal - optimizedTotal) / legacyTotal * 100).toFixed(1);
    console.log(`⚡ Performance Improvement: ${improvement}%`);
  }

  console.log(`📋 Summary View: ${summaryResult.duration}ms`);

  // Recommendations
  console.log('\n💡 Recommendations\n');
  
  if (optimizedTotal < legacyTotal) {
    console.log('✅ Optimized queries are performing better!');
  } else {
    console.log('⚠️  Legacy queries are still faster. Consider:');
    console.log('   - Checking if indexes are properly created');
    console.log('   - Verifying RPC functions are working correctly');
    console.log('   - Running ANALYZE on tables to update statistics');
  }

  if (summaryResult.duration > 1000) {
    console.log('⚠️  Summary view is slow. Consider refreshing it:');
    console.log('   SELECT public.refresh_analytics_summary();');
  }

  // Performance thresholds
  console.log('\n🎯 Performance Targets\n');
  console.log('✅ Excellent: < 500ms');
  console.log('🟡 Good: 500ms - 1000ms');
  console.log('🟠 Acceptable: 1000ms - 2000ms');
  console.log('🔴 Needs Optimization: > 2000ms');
}

async function main() {
  console.log('🔍 Analytics Performance Testing\n');

  try {
    // Get a test user ID
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.error('❌ No users found for testing');
      return;
    }

    const testUserId = users[0].id;
    console.log(`🧪 Testing with user ID: ${testUserId}`);

    // Run performance tests
    const optimizedResults = await testOptimizedQueries(testUserId);
    const legacyResults = await testLegacyQueries(testUserId);
    const summaryResult = await testAnalyticsSummaryView();

    // Generate report
    await generatePerformanceReport(optimizedResults, legacyResults, summaryResult);

    console.log('\n🎉 Performance testing completed!');

  } catch (error) {
    console.error('❌ Error during performance testing:', error.message);
  }
}

// Run the tests
main().catch(error => {
  console.error('❌ Unexpected error:', error.message);
  process.exit(1);
});
