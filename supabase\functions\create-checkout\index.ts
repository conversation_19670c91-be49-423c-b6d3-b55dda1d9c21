import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@12.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const supabaseUrl = "https://oewgwxxajssonxavydbx.supabase.co";
const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY") || "";
const stripeKey = Deno.env.get("STRIPE_SECRET_KEY") || "";

if (!stripeKey) {
  console.error("Missing STRIPE_SECRET_KEY");
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Missing authorization header");
    }

    const supabase = createClient(supabaseUrl, supabaseKey, {
      global: { headers: { Authorization: authHeader } },
    });

    // Get the authenticated user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error("User not authenticated");
    }

    // Get request data
    const { couponId } = await req.json();
    if (!couponId) {
      throw new Error("Missing couponId");
    }

    // Get coupon details
    const { data: coupon, error: couponError } = await supabase
      .from("coupons")
      .select("*, brand:brands(*), influencer:profiles(*)")
      .eq("id", couponId)
      .single();

    if (couponError || !coupon) {
      throw new Error("Coupon not found");
    }

    if (!coupon.is_premium) {
      throw new Error("This is not a premium coupon");
    }

    if (coupon.price <= 0) {
      throw new Error("Invalid coupon price");
    }

    // Check if user already purchased this coupon
    const { data: existingPurchase } = await supabase
      .from("premium_purchases")
      .select("id")
      .eq("buyer_id", user.id)
      .eq("coupon_id", couponId)
      .maybeSingle();

    if (existingPurchase) {
      // User already has access
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: "You already have access to this coupon",
          alreadyPurchased: true
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Get the platform fee settings
    const { data: platformSettings, error: settingsError } = await supabase
      .from("platform_settings")
      .select("*")
      .order("updated_at", { ascending: false })
      .limit(1)
      .single();

    // Default settings if there was an error or no settings found
    const platformFeePercent = platformSettings?.platform_fee_percent || 5.0; // 5% default
    const minFeeAmount = platformSettings?.min_fee_amount || 0.5; // $0.50 min
    const maxFeeAmount = platformSettings?.max_fee_amount || null; // No max by default

    // Calculate platform fee
    let platformFee = (coupon.price * platformFeePercent) / 100;
    
    // Apply minimum fee
    platformFee = Math.max(platformFee, minFeeAmount);
    
    // Apply maximum fee if set
    if (maxFeeAmount !== null) {
      platformFee = Math.min(platformFee, maxFeeAmount);
    }
    
    // Round to 2 decimal places
    platformFee = Math.round(platformFee * 100) / 100;
    
    // Calculate seller amount (price minus platform fee)
    const sellerAmount = Math.round((coupon.price - platformFee) * 100) / 100;

    // Initialize Stripe
    const stripe = new Stripe(stripeKey);

    // Get user profile
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `Coupon: ${coupon.title}`,
              description: `${coupon.discount_description || `${coupon.discount_percent}% off`} at ${coupon.brand?.name}`,
              images: coupon.brand?.logo_url ? [coupon.brand.logo_url] : undefined,
            },
            unit_amount: Math.round(coupon.price * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${req.headers.get("origin")}/coupons?success=true&coupon=${couponId}`,
      cancel_url: `${req.headers.get("origin")}/influencer/${coupon.influencer?.username}?canceled=true`,
      client_reference_id: couponId,
      customer_email: user.email,
      metadata: {
        couponId: couponId,
        buyerId: user.id,
        sellerId: coupon.influencer_id,
        platformFee: platformFee.toString(),
        sellerAmount: sellerAmount.toString()
      },
    });

    return new Response(
      JSON.stringify({ 
        success: true, 
        sessionId: session.id, 
        sessionUrl: session.url 
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error:", error.message);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400
      }
    );
  }
});
