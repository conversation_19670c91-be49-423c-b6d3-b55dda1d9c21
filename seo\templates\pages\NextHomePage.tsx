import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { NextPage } from 'next';

const Home: NextPage = () => {
  return (
    <>
      <Head>
        <title>CouponLink.in | Find Verified Coupon Codes & Promo Codes</title>
        <meta
          name="description"
          content="Find verified coupon codes and promo codes for your favorite brands. CouponLink.in helps you save money with working discount codes updated daily."
        />
        <meta name="keywords" content="coupon codes, promo codes, discount codes, coupon link, deals, savings" />
        
        {/* Open Graph tags */}
        <meta property="og:title" content="CouponLink.in | Find Verified Coupon Codes" />
        <meta property="og:description" content="Find verified coupon codes for your favorite brands. Save money with working discount codes." />
        <meta property="og:image" content="https://couponlink.in/images/couponlink-social.jpg" />
        <meta property="og:url" content="https://couponlink.in" />
        <meta property="og:type" content="website" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="CouponLink.in | Find Verified Coupon Codes" />
        <meta name="twitter:description" content="Find verified coupon codes for your favorite brands. Save money with working discount codes." />
        <meta name="twitter:image" content="https://couponlink.in/images/couponlink-social.jpg" />
        
        {/* Canonical URL */}
        <link rel="canonical" href="https://couponlink.in" />
        
        {/* Schema.org markup for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "CouponLink.in",
              "url": "https://couponlink.in",
              "logo": "https://couponlink.in/images/logo.png",
              "sameAs": [
                "https://facebook.com/couponlink",
                "https://twitter.com/couponlink",
                "https://instagram.com/couponlink"
              ],
              "potentialAction": {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": "https://couponlink.in/search?q={search_term_string}"
                },
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
        
        {/* Schema.org markup for Website */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "url": "https://couponlink.in",
              "name": "CouponLink.in",
              "description": "Find verified coupon codes and promo codes for your favorite brands.",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://couponlink.in/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </Head>

      <main>
        <div className="hero bg-gradient-to-r from-blue-500 to-indigo-600 text-white">
          <div className="container mx-auto px-4 py-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Find Verified Coupon Codes for Your Favorite Brands
            </h1>
            <p className="text-xl mb-8">
              Save money with CouponLink.in's verified promotional offers
            </p>
            
            <div className="search-bar mb-8">
              <div className="relative max-w-xl mx-auto">
                <input
                  type="text"
                  placeholder="Search for a store or brand..."
                  className="w-full py-3 px-4 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300"
                />
                <button className="absolute right-2 top-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2">
                  Search
                </button>
              </div>
            </div>
            
            <div className="stats flex flex-wrap justify-center gap-8 text-center">
              <div className="stat">
                <span className="block text-4xl font-bold">1,500+</span>
                <span className="text-blue-100">Brands</span>
              </div>
              <div className="stat">
                <span className="block text-4xl font-bold">10,000+</span>
                <span className="text-blue-100">Coupon Codes</span>
              </div>
              <div className="stat">
                <span className="block text-4xl font-bold">95%</span>
                <span className="text-blue-100">Success Rate</span>
              </div>
            </div>
          </div>
        </div>

        {/* Popular Brands Section */}
        <section className="popular-brands py-12">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Popular Brands</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['amazon', 'walmart', 'target', 'best-buy', 'macys', 'nike', 'apple', 'samsung'].map((brand) => (
                <Link href={`/brands/${brand}`} key={brand}>
                  <div className="brand-card border rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <img
                      src={`/images/brands/${brand}.png`}
                      alt={`${brand} logo`}
                      className="w-24 h-24 object-contain mx-auto mb-2"
                    />
                    <h3 className="font-medium capitalize">{brand.replace('-', ' ')}</h3>
                    <p className="text-blue-600 text-sm">View Coupons</p>
                  </div>
                </Link>
              ))}
            </div>
            <div className="text-center mt-8">
              <Link href="/brands" className="bg-blue-600 text-white px-6 py-2 rounded-full inline-block hover:bg-blue-700">
                View All Brands
              </Link>
            </div>
          </div>
        </section>

        {/* Categories Section */}
        <section className="categories py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Shop by Category</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['electronics', 'fashion', 'travel', 'food', 'beauty', 'home-garden', 'health', 'toys'].map((category) => (
                <Link href={`/categories/${category}`} key={category}>
                  <div className="category-card rounded-lg p-4 text-center hover:bg-white hover:shadow-md transition-all">
                    <h3 className="font-medium text-lg capitalize mb-2">{category.replace('-', ' & ')}</h3>
                    <p className="text-gray-600 text-sm mb-2">Find the best deals</p>
                    <span className="text-blue-600 text-xs">View Coupons →</span>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Deals */}
        <section className="featured-deals py-12">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Today's Featured Deals</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="deal-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                <div className="bg-yellow-100 p-3 text-center">
                  <span className="inline-block bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">EXCLUSIVE</span>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-xl mb-1">Amazon</h3>
                  <p className="font-medium text-green-600 mb-2">20% OFF</p>
                  <p className="text-gray-600 mb-3">Get 20% off your entire purchase</p>
                  <div className="code-box border border-dashed border-gray-400 p-2 text-center mb-3 bg-gray-50">
                    <code className="font-mono">SAVE20NOW</code>
                  </div>
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded">
                    Copy Code
                  </button>
                </div>
              </div>
              
              <div className="deal-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                <div className="bg-blue-100 p-3 text-center">
                  <span className="inline-block bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">TRENDING</span>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-xl mb-1">Walmart</h3>
                  <p className="font-medium text-green-600 mb-2">FREE SHIPPING</p>
                  <p className="text-gray-600 mb-3">Free shipping on orders over $35</p>
                  <div className="code-box border border-dashed border-gray-400 p-2 text-center mb-3 bg-gray-50">
                    <code className="font-mono">FREESHIP35</code>
                  </div>
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded">
                    Copy Code
                  </button>
                </div>
              </div>
              
              <div className="deal-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                <div className="bg-purple-100 p-3 text-center">
                  <span className="inline-block bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold">VERIFIED</span>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-xl mb-1">Best Buy</h3>
                  <p className="font-medium text-green-600 mb-2">$50 OFF</p>
                  <p className="text-gray-600 mb-3">$50 off purchases of $500 or more</p>
                  <div className="code-box border border-dashed border-gray-400 p-2 text-center mb-3 bg-gray-50">
                    <code className="font-mono">SAVE50NOW</code>
                  </div>
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded">
                    Copy Code
                  </button>
                </div>
              </div>
            </div>
            <div className="text-center mt-8">
              <Link href="/deals" className="bg-blue-600 text-white px-6 py-2 rounded-full inline-block hover:bg-blue-700">
                View All Deals
              </Link>
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="how-it-works py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">How CouponLink Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="step text-center">
                <div className="step-number bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">1</div>
                <h3 className="text-xl font-bold mb-2">Find a Coupon</h3>
                <p className="text-gray-600">Browse our extensive collection of verified coupon codes from top brands</p>
              </div>
              <div className="step text-center">
                <div className="step-number bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">2</div>
                <h3 className="text-xl font-bold mb-2">Copy the Code</h3>
                <p className="text-gray-600">Click the button to instantly copy the coupon code to your clipboard</p>
              </div>
              <div className="step text-center">
                <div className="step-number bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold">3</div>
                <h3 className="text-xl font-bold mb-2">Save Money</h3>
                <p className="text-gray-600">Paste the code at checkout on the retailer's website and enjoy your savings</p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section for SEO */}
        <section className="faq py-12">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>
            <div className="max-w-3xl mx-auto">
              <div className="space-y-4">
                <details className="bg-white rounded-lg border p-4">
                  <summary className="font-medium text-lg cursor-pointer">How do I use a coupon code?</summary>
                  <p className="mt-2 text-gray-700">
                    To use a coupon code, click the "Copy Code" button next to the offer you want to use. 
                    The code will be copied to your clipboard. Then, visit the retailer's website, add 
                    your items to the cart, and paste the code in the promo code field at checkout.
                  </p>
                </details>
                <details className="bg-white rounded-lg border p-4">
                  <summary className="font-medium text-lg cursor-pointer">Are all coupon codes verified?</summary>
                  <p className="mt-2 text-gray-700">
                    Yes, we verify all coupon codes multiple times per week to ensure they're working. 
                    However, some codes may expire or have restrictions imposed by retailers after our 
                    verification. We display the success rate to help you choose the most reliable coupons.
                  </p>
                </details>
                <details className="bg-white rounded-lg border p-4">
                  <summary className="font-medium text-lg cursor-pointer">Why isn't my coupon code working?</summary>
                  <p className="mt-2 text-gray-700">
                    There could be several reasons: the code might have expired, you may not meet the 
                    minimum purchase requirements, or the code might not be applicable to the items in 
                    your cart. Always check the terms and conditions for each offer.
                  </p>
                </details>
                <details className="bg-white rounded-lg border p-4">
                  <summary className="font-medium text-lg cursor-pointer">How often are new coupons added?</summary>
                  <p className="mt-2 text-gray-700">
                    We add new coupons daily. Our team constantly monitors retailers for new promotions,
                    special offers, and exclusive deals to ensure you always have access to the latest
                    savings opportunities.
                  </p>
                </details>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
};

export default Home; 