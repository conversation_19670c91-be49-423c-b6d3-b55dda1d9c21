-- Create the profile_views table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profile_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address TEXT,
  user_agent TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_views_viewer_id ON public.profile_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_profile_id ON public.profile_views(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_created_at ON public.profile_views(created_at);

-- Enable Row Level Security
ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;

-- Allow anonymous inserts to profile_views (anonymous tracking)
CREATE POLICY IF NOT EXISTS "Allow anonymous inserts to profile_views"
ON public.profile_views
FOR INSERT
TO anon, authenticated
<PERSON><PERSON><PERSON> CHECK (true);

-- Allow service_role to do everything
CREATE POLICY IF NOT EXISTS "Allow service role full access to profile_views"
ON public.profile_views
USING (true)
WITH CHECK (true);

-- Allow users to see views of their profile
CREATE POLICY IF NOT EXISTS "Allow users to see views of their profile"
ON public.profile_views
FOR SELECT
TO authenticated
USING (
  (viewer_id = auth.uid()) OR
  (profile_id = auth.uid())
); 