import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Category {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  slug: string;
  coupon_count?: number;
  icon_url?: string | null;
  cover_image_url?: string | null;
  display_order?: number;
  color_hex?: string | null;
  is_featured?: boolean;
}

/**
 * Hook to fetch categories from Supabase
 */
export function useCategories() {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async (): Promise<Category[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('display_order', { ascending: true })
        .order('name');
      
      if (error) throw error;
      return data || [];
    },
  });
} 