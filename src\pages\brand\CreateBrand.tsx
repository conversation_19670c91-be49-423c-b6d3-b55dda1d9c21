import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase, safeCreateBrand } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import BackButton from '@/components/BackButton';
import { Building2, RefreshCw, Wand2, Globe, Sparkles } from 'lucide-react';

// Logo.dev API key (public)
const LOGO_DEV_API_KEY = 'pk_AMGOWyXrRPG8NF1-fQg70Q';

const CreateBrand = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [name, setName] = useState('');
  const [website, setWebsite] = useState('');
  const [logoUrl, setLogoUrl] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [generatingLogo, setGeneratingLogo] = useState(false);
  const [isCheckingName, setIsCheckingName] = useState(false);
  const [nameExists, setNameExists] = useState(false);
  
  // Define checkBrandName function outside useEffect
  const checkBrandName = async (name: string) => {
    if (name.trim().length < 2) {
      setNameExists(false);
      return;
    }
    
    setIsCheckingName(true);
    try {
      const { count, error } = await supabase
        .from('brands')
        .select('*', { count: 'exact', head: true })
        .ilike('name', name.trim());
      
      if (error) {
        console.error('Error checking brand name:', error);
        return;
      }
      
      setNameExists(count !== null && count > 0);
    } catch (error) {
      console.error('Error checking brand name:', error);
    } finally {
      setIsCheckingName(false);
    }
  };
  
  // Get name from URL parameters
  useEffect(() => {
    const nameFromUrl = searchParams.get('name');
    if (nameFromUrl) {
      setName(nameFromUrl);
      // Trigger name check when coming from search
      checkBrandName(nameFromUrl);
    }
  }, [searchParams]);
  
  // Check if brand name already exists when name changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkBrandName(name);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [name]);
  
  // Extract domain from website URL for Clearbit logo
  const extractDomain = (url: string) => {
    try {
      // Add protocol if missing
      if (!url.startsWith('http')) {
        url = 'https://' + url;
      }
      const domain = new URL(url).hostname;
      return domain.startsWith('www.') ? domain.substring(4) : domain;
    } catch (error) {
      console.error('Invalid URL:', error);
      return null;
    }
  };
  
  // Generate website URL from brand name
  const generateWebsiteUrl = () => {
    if (!name.trim()) {
      toast.error('Please enter a brand name first');
      return;
    }
    
    // Convert name to lowercase, remove spaces and special characters
    const websiteName = name.trim()
      .toLowerCase()
      .replace(/\s+/g, '')
      .replace(/[^a-z0-9]/g, '');
    
    if (websiteName) {
      setWebsite(`https://www.${websiteName}.com`);
      toast.success('Website URL generated');
    } else {
      toast.error('Could not generate a valid website URL from this brand name');
    }
  };
  
  // Use Clearbit API to get logo based on domain
  const useClearbitLogo = () => {
    if (!website) {
      toast.error('Please enter a website URL first');
      return;
    }
    
    const domain = extractDomain(website);
    if (!domain) {
      toast.error('Could not extract domain from website URL');
      return;
    }
    
    const clearbitUrl = `https://logo.clearbit.com/${domain}`;
    setLogoUrl(clearbitUrl);
    toast.success('Logo fetched from Clearbit');
  };
  
  // Generate logo using Logo.dev API
  const generateLogo = async () => {
    if (!name.trim()) {
      toast.error('Please enter a brand name first');
      return;
    }
    
    setGeneratingLogo(true);
    
    try {
      // Call Logo.dev API to generate a logo
      const response = await fetch('https://api.logo.dev/v1/logo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${LOGO_DEV_API_KEY}`
        },
        body: JSON.stringify({
          name: name.trim(),
          industry: 'general',
          format: 'png'
        })
      });
      
      // Handle HTTP error responses
      if (!response.ok) {
        // Attempt to get the error details from the response
        let errorMessage = 'Unknown error occurred';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || `API error (${response.status}): ${response.statusText}`;
        } catch (parseError) {
          // If we can't parse the JSON, just use the status text
          errorMessage = `API error (${response.status}): ${response.statusText}`;
        }
        
        console.error('Logo API error:', response.status, errorMessage);
        
        // Handle specific status codes
        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        } else if (response.status === 403) {
          throw new Error('API key invalid or unauthorized access.');
        } else if (response.status === 400) {
          throw new Error(`Invalid request: ${errorMessage}`);
        } else if (response.status >= 500) {
          throw new Error('Logo.dev service is currently unavailable. Please try again later.');
        } else {
          throw new Error(errorMessage);
        }
      }
      
      // Parse response JSON with error handling
      let data;
      try {
        data = await response.json();
      } catch (err) {
        console.error('Error parsing response:', err);
        throw new Error('Failed to parse API response');
      }
      
      // Logo.dev returns a URL that we can use directly
      if (data && data.url) {
        setLogoUrl(data.url);
        toast.success('Logo generated successfully!');
      } else {
        console.error('Unexpected API response:', data);
        throw new Error('Invalid response from logo service - missing URL');
      }
    } catch (error) {
      console.error('Error generating logo:', error);
      
      // Show the specific error message to the user
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Logo generation failed: ${errorMessage}`);
      
      // Provide fallback to Clearbit if a website is available
      if (website) {
        const domain = extractDomain(website);
        if (domain) {
          const clearbitUrl = `https://logo.clearbit.com/${domain}`;
          setLogoUrl(clearbitUrl);
          toast.info('Using Clearbit logo instead.');
          return;
        }
      }
      
      // Generate a placeholder logo with the brand initial if everything else fails
      const placeholderUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff&size=256&bold=true`;
      setLogoUrl(placeholderUrl);
      toast.info('Using a placeholder logo instead.');
    } finally {
      setGeneratingLogo(false);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Debugging message
    toast.info('Attempting to create brand...');
    console.log('Form submitted, current user:', user?.id);
    
    if (!user) {
      toast.error('You must be logged in to create brands');
      console.error('Brand creation failed: User not authenticated');
      return;
    }
    
    if (!name) {
      toast.error('Please enter a brand name');
      return;
    }
    
    if (nameExists) {
      toast.error('A brand with this name already exists. Please use a different name.');
      return;
    }
    
    // Validate website if provided
    if (website && !website.match(/^https?:\/\/.+\..+/)) {
      toast.error('Please enter a valid website URL (including http:// or https://)');
      return;
    }
    
    setSubmitting(true);
    
    // Set a fallback timeout to ensure the user can continue even if there's an issue
    const returnTo = searchParams.get('returnTo');
    const navigationTimeoutId = setTimeout(() => {
      if (submitting) {
        console.log('Navigation timeout triggered - ensuring user can continue');
        setSubmitting(false);
        toast.info('You can continue browsing while we complete the process');
        if (returnTo === 'create-coupon') {
          navigate('/coupons/create');
        } else {
          navigate('/brands');
        }
      }
    }, 5000); // 5 seconds fallback timeout
    
    try {
      // Do a final check for duplicate brand names before inserting
      const { count: duplicateCount, error: checkError } = await supabase
        .from('brands')
        .select('*', { count: 'exact', head: true })
        .ilike('name', name.trim());
      
      if (checkError) {
        console.error('Error checking for duplicates:', checkError);
        throw new Error('Failed to check for duplicate brands');
      }
      
      if (duplicateCount && duplicateCount > 0) {
        toast.error('A brand with this name already exists. Please use a different name.');
        setSubmitting(false);
        clearTimeout(navigationTimeoutId);
        return;
      }
      
      console.log('Calling safeCreateBrand with:', { name, website: website || null, logo_url: logoUrl || null });
      
      // Use the new safeCreateBrand helper function
      const { data, error, success } = await safeCreateBrand({
        name,
        website: website || null,
        logo_url: logoUrl || null
      });
      
      if (error) {
        console.error('Brand creation failed:', error);
        toast.error(error.message || 'Failed to create brand. Please try again.');
        setSubmitting(false);
        clearTimeout(navigationTimeoutId);
        return;
      }
      
      if (!data || data.length === 0) {
        throw new Error('No data returned after brand creation');
      }
      
      // Clear the navigation timeout since we're handling it normally
      clearTimeout(navigationTimeoutId);
      
      toast.success('Brand created successfully!');

      // Check if we should return to create coupon page
      const returnTo = searchParams.get('returnTo');

      // Add a short delay before navigation to ensure toast is seen
      setTimeout(() => {
        try {
          if (returnTo === 'create-coupon') {
            // Navigate back to create coupon page with the new brand ID
            navigate(`/coupons/create?brandId=${data[0].id}`, { replace: true });
            console.log('Navigation to create coupon page completed with brand ID:', data[0].id);
          } else {
            // Default navigation to brands page
            navigate('/brands', { replace: true });
            console.log('Navigation to /brands completed');
          }
        } catch (navError) {
          console.error('Navigation error:', navError);
          // Force a hard navigation if the React Router navigation fails
          if (returnTo === 'create-coupon') {
            window.location.href = `/coupons/create?brandId=${data[0].id}`;
          } else {
            window.location.href = '/brands';
          }
        }
      }, 500);
    } catch (error) {
      console.error('Error in brand creation process:', error);
      
      // Clear the navigation timeout
      clearTimeout(navigationTimeoutId);
      
      // Simplified error handling
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast.error('Network error. Please check your internet connection and try again.');
      } else if (error instanceof Error) {
        toast.error(error.message || 'Failed to create brand. Please try again.');
      }
      
      // Give the user a way to continue even if there's an error
      toast.info('You can try again or continue browsing');
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <BackButton />
        </div>
        
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 flex items-center">
            <Building2 className="h-8 w-8 mr-2 text-brand-blue-500" />
            Create New Brand
          </h1>
          
          <form 
            onSubmit={handleSubmit} 
            className="space-y-6 bg-white rounded-xl shadow-sm p-6"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && submitting) {
                e.preventDefault();
              }
            }}
          >
            <div>
              <Label htmlFor="name">Brand Name*</Label>
              <div className="relative">
                <Input 
                  id="name" 
                  value={name} 
                  onChange={(e) => setName(e.target.value)} 
                  placeholder="e.g., Nike, Amazon, etc."
                  required
                  minLength={2}
                  disabled={submitting}
                  className={nameExists ? "border-red-500 pr-10" : ""}
                />
                {isCheckingName && (
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
                  </span>
                )}
                {nameExists && (
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </span>
                )}
              </div>
              {nameExists ? (
                <p className="text-sm text-red-500 mt-1">
                  This brand name already exists. Please choose a different name.
                </p>
              ) : (
                <p className="text-sm text-gray-500 mt-1">
                  The name of the brand as it will appear to users
                </p>
              )}
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="website">Website</Label>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={generateWebsiteUrl}
                  disabled={!name.trim() || submitting}
                  className="flex items-center gap-1"
                >
                  <Wand2 className="h-3 w-3 mr-1" />
                  Generate URL
                </Button>
              </div>
              <Input 
                id="website" 
                type="url"
                value={website} 
                onChange={(e) => setWebsite(e.target.value)} 
                placeholder="e.g., https://www.nike.com"
                disabled={submitting}
              />
              <p className="text-sm text-gray-500 mt-1">
                The official website of the brand
              </p>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="logoUrl">Logo URL</Label>
                <div className="flex space-x-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={useClearbitLogo}
                    disabled={!website || submitting}
                    className="flex items-center gap-1"
                  >
                    <Globe className="h-3 w-3 mr-1" />
                    Use Clearbit
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={generateLogo}
                    disabled={generatingLogo || !name.trim() || submitting}
                    className="flex items-center gap-1"
                  >
                    {generatingLogo ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-3 w-3 mr-1" />
                        Use Logo.dev
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <Input 
                id="logoUrl" 
                value={logoUrl} 
                onChange={(e) => setLogoUrl(e.target.value)}
                placeholder="e.g., https://logo.clearbit.com/nike.com"
                disabled={submitting}
              />
              <p className="text-sm text-gray-500 mt-1">
                URL to the brand's logo image. Use Clearbit to fetch a real logo by domain name, or use Logo.dev to generate an AI logo.
              </p>

              {/* Logo Preview */}
              {logoUrl && (
                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Logo Preview:</p>
                  <div className="h-24 w-24 bg-gray-100 rounded-lg flex items-center justify-center p-2">
                    <img 
                      src={logoUrl} 
                      alt={`${name} logo`} 
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = 'https://placehold.co/100x100?text=Invalid+Image';
                        toast.error('Invalid logo URL. Please check the URL or generate a new logo.');
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
            
            <div className="pt-4">
              <Button 
                type="submit" 
                className="w-full bg-brand-blue hover:bg-brand-blue/90"
                disabled={submitting}
              >
                {submitting ? (
                  <span className="flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creating Brand...
                  </span>
                ) : 'Create Brand'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  );
};

export default CreateBrand; 