import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { format } from "date-fns";

export interface CouponPerformanceData {
  id: string;
  name: string;
  code?: string;
  views: number;
  clicks: number;
  uses?: number;
  conversionRate: number;
  expiry?: string;
}

interface CouponPerformanceCardProps {
  coupon: CouponPerformanceData;
  onViewDetails?: (id: string) => void;
}

const CouponPerformanceCard = ({ coupon, onViewDetails }: CouponPerformanceCardProps) => {
  return (
    <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-4">
        <div className="flex flex-wrap sm:flex-nowrap items-center gap-3 mb-3">
          <div className="h-12 w-12 flex-shrink-0 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center overflow-hidden">
            {/* Brand logo placeholder */}
            <div className="font-bold text-gray-500 dark:text-gray-400 text-lg">
              {coupon.name.charAt(0)}
            </div>
          </div>
          <div className="flex-grow min-w-0 w-full sm:w-auto">
            <h3 className="font-medium text-base truncate">{coupon.name}</h3>
            {coupon.code && (
              <div className="flex items-center">
                <p className="text-xs text-muted-foreground mr-1">Code:</p>
                <span className="font-mono bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded text-xs">
                  {coupon.code}
                </span>
              </div>
            )}
          </div>
          {onViewDetails && (
            <div className="w-full sm:w-auto sm:ml-auto mt-2 sm:mt-0">
              <Button 
                size="sm" 
                variant="outline" 
                className="text-xs h-8 flex items-center"
                onClick={() => onViewDetails(coupon.id)}
              >
                See Insights
                <ChevronRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
          <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Clicks</p>
            <p className="font-semibold text-sm">{coupon.clicks}</p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Views</p>
            <p className="font-semibold text-sm">{coupon.views}</p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Engagement</p>
            <p className="font-semibold text-sm">{coupon.conversionRate}%</p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-lg text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Expiry</p>
            <p className="font-semibold text-xs">
              {coupon.expiry 
                ? format(new Date(coupon.expiry), "MMM d, yyyy")
                : "No expiry"}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CouponPerformanceCard; 