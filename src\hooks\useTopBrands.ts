import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Brand } from './useBrands';

export const useTopBrands = (limit = 4) => {
  return useQuery({
    queryKey: ['topBrands', limit],
    queryFn: async () => {
      try {
        // First get all brands
        const { data: brands, error } = await supabase
          .from('brands')
          .select('*')
          .limit(limit * 2); // Fetch more than needed for filtering
        
        if (error) {
          console.error('Error fetching top brands:', error);
          return [];
        }

        // Get coupon count for each brand
        const brandsWithCounts = await Promise.all(
          (brands || []).map(async (brand) => {
            const { count } = await supabase
              .from('coupons')
              .select('*', { count: 'exact', head: true })
              .eq('brand_id', brand.id)
              .eq('status', 'active');
            
            return {
              ...brand,
              couponCount: count || 0
            };
          })
        );
        
        // Sort by coupon count and take top brands
        return brandsWithCounts
          .sort((a, b) => (b.couponCount || 0) - (a.couponCount || 0))
          .slice(0, limit);
      } catch (error) {
        console.error('Error in useTopBrands hook:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 15 // 15 minutes
  });
}; 