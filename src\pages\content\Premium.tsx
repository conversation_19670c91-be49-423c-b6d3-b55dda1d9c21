import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import CouponCard from '@/components/CouponCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Crown, Filter, BadgePercent, ShieldCheck, TagIcon, Bookmark, Users, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useCategories } from '@/hooks/useCategories';
import BackButton from '@/components/BackButton';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';

const Premium = () => {
  const { user } = useAuth();
  const [sortBy, setSortBy] = useState('newest');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [hasPurchasedMap, setHasPurchasedMap] = useState<Record<string, boolean>>({});
  
  // Use the centralized hook for fetching categories
  const { data: categories } = useCategories();
  
  // Fetch premium coupons with optimized query
  const { data: premiumCoupons, isLoading, error } = useQuery({
    queryKey: ['premium-coupons', sortBy, categoryFilter],
    queryFn: async () => {
      let query = supabase
        .from('coupons')
        .select(`
          id, title, code, discount_percent, discount_description,
          expires_at, status, featured, is_premium, view_count, created_at, price,
          brand:brands(id, name, logo_url, website),
          influencer:profiles(id, full_name, username, avatar_url),
          category:categories(id, name)
        `)
        .eq('status', 'active')
        .eq('is_premium', true);

      // Apply category filter if selected
      if (categoryFilter !== 'all') {
        query = query.eq('category_id', categoryFilter);
      }

      // Apply sorting
      if (sortBy === 'newest') {
        query = query.order('created_at', { ascending: false });
      } else if (sortBy === 'price-low') {
        query = query.order('price', { ascending: true });
      } else if (sortBy === 'price-high') {
        query = query.order('price', { ascending: false });
      } else if (sortBy === 'discount') {
        query = query.order('discount_percent', { ascending: false });
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    },
    staleTime: 1000 * 60 * 15, // 15 minutes for premium
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
  });
  
  // Check which premium coupons the user has purchased
  useEffect(() => {
    const checkPurchasedCoupons = async () => {
      if (!user || !premiumCoupons?.length) return;
      
      try {
        const { data, error } = await supabase
          .from('premium_purchases')
          .select('coupon_id')
          .eq('buyer_id', user.id);
          
        if (error) throw error;
        
        // Create a map of coupon_id -> true for purchased coupons
        const purchasedMap: Record<string, boolean> = {};
        data?.forEach(purchase => {
          purchasedMap[purchase.coupon_id] = true;
        });
        
        setHasPurchasedMap(purchasedMap);
      } catch (error) {
        console.error('Error checking purchased coupons:', error);
      }
    };
    
    checkPurchasedCoupons();
  }, [user, premiumCoupons]);
  
  return (
    <MainLayout>
      <PageContainer decorationType="default">
        <PageHeaderWithBackButton
          title="Premium Deals"
          subtitle="Exclusive premium coupons with higher discounts"
          icon={Crown}
        />
        
        <div className="flex flex-wrap gap-3 mb-8 mt-4">
          <div className="flex items-center">
            <Badge variant="outline" className="mr-2 border-amber-500/50">
              <Filter className="w-3 h-3 mr-1 text-amber-500" />
              Filter:
            </Badge>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center">
            <Badge variant="outline" className="mr-2 border-amber-500/50">
              <BadgePercent className="w-3 h-3 mr-1 text-amber-500" />
              Sort:
            </Badge>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="discount">Discount %</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Premium benefits banner with animation */}
        <motion.div
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="relative overflow-hidden bg-gradient-to-r from-amber-500 to-amber-600 rounded-xl mb-8 shadow-lg"
        >
          {/* Decorative elements */}
          <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
          
          <div className="relative p-6 md:p-8 text-white">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div className="max-w-xl">
                <h2 className="text-xl md:text-2xl font-bold mb-2 flex items-center">
                  <ShieldCheck className="w-6 h-6 mr-2" /> 
                  Premium Benefits
                </h2>
                <p className="mb-4 text-white/90 text-sm md:text-base">
                  Unlock exclusive deals with higher discounts and special offers from top brands. Premium users save an average of 35% more than regular users.
                </p>
                
                {/* Benefits list */}
                <div className="flex flex-wrap gap-3 mt-4">
                  <div className="flex items-center bg-white/20 backdrop-blur-sm py-1.5 px-3 rounded-full text-sm">
                    <BadgePercent className="w-3.5 h-3.5 mr-1.5" />
                    Higher Discounts
                  </div>
                  <div className="flex items-center bg-white/20 backdrop-blur-sm py-1.5 px-3 rounded-full text-sm">
                    <TagIcon className="w-3.5 h-3.5 mr-1.5" />
                    Limited Availability
                  </div>
                  <div className="flex items-center bg-white/20 backdrop-blur-sm py-1.5 px-3 rounded-full text-sm">
                    <ShieldCheck className="w-3.5 h-3.5 mr-1.5" />
                    Verified Offers
                  </div>
                  <div className="flex items-center bg-white/20 backdrop-blur-sm py-1.5 px-3 rounded-full text-sm">
                    <Bookmark className="w-3.5 h-3.5 mr-1.5" />
                    Early Access
                  </div>
                </div>
              </div>
              
              {!user && (
                <div className="flex flex-col items-center">
                  <Button asChild className="bg-white text-amber-600 hover:bg-white/90 shadow-md font-medium px-6">
                    <Link to="/auth?mode=signup">Join Now</Link>
                  </Button>
                  <p className="text-sm mt-2 text-white/80">Already a member? <Link to="/auth" className="underline">Sign in</Link></p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
        
        {/* Coupons grid with staggered animation */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm h-64 animate-pulse">
                <div className="flex justify-between items-center mb-4">
                  <Skeleton className="h-12 w-12 rounded-lg" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-6" />
                <Skeleton className="h-10 w-full mt-4" />
              </div>
            ))
          ) : error ? (
            <div className="col-span-full text-center py-16 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <Crown className="mx-auto h-12 w-12 text-red-400 mb-4" />
              <h3 className="text-xl font-medium text-red-700 dark:text-red-300 mb-2">Error Loading Premium Coupons</h3>
              <p className="text-red-600 dark:text-red-400">{error.message}</p>
            </div>
          ) : premiumCoupons && premiumCoupons.length > 0 ? (
            premiumCoupons.map((coupon, index) => (
              <motion.div
                key={coupon.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 * (index % 4) }}
              >
                <CouponCard
                  id={coupon.id}
                  brandName={coupon.brand?.name || "Unknown Brand"}
                  brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                  influencerName={coupon.influencer?.full_name || ""}
                  influencerImage={coupon.influencer?.avatar_url || undefined}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "Uncategorized"}
                  featured={coupon.featured}
                  isPremium={true}
                  isLocked={!user || !hasPurchasedMap[coupon.id]}
                  price={coupon.price || 0}
                />
              </motion.div>
            ))
          ) : (
            <div className="col-span-full text-center py-16 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <Crown className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-2">No premium coupons available</h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                There are currently no premium coupons matching your filters. Try changing your filter settings or check back later.
              </p>
            </div>
          )}
        </div>
        
        {/* Upgrade CTA - shown to non-premium users or non-logged in users */}
        {(!user || (user && !user.user_metadata?.is_premium)) && premiumCoupons?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl shadow-xl overflow-hidden"
          >
            <div className="px-6 py-8 sm:p-10 lg:flex lg:items-center lg:justify-between gap-8">
              <div>
                <h3 className="text-xl md:text-2xl font-bold tracking-tight text-white flex items-center">
                  <Star className="w-6 h-6 mr-2 text-yellow-300 fill-yellow-300" />
                  Upgrade to Premium
                </h3>
                <p className="mt-3 max-w-xl text-indigo-100 text-sm md:text-base">
                  Get access to all premium coupons and exclusive deals. Save more with premium-only offers from top brands.
                </p>
              </div>
              <div className="mt-6 lg:mt-0 flex flex-shrink-0">
                <Button asChild className="bg-white text-indigo-600 hover:bg-white/90 shadow-md font-medium px-6">
                  <Link to="/pricing">View Plans</Link>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default Premium; 