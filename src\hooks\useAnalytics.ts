import { supabase } from '@/integrations/supabase/client';

interface TrackViewParams {
  type: 'profile' | 'coupon';
  id: string;
  referrer?: string;
  interaction?: string;
}

interface TrackShareParams {
  profile_id: string;
  platform?: string;
}

// Create a completely new implementation to avoid any caching issues
export const useAnalytics = () => {
  // Direct database approach - no Edge Function
  const trackDirectly = async (params: TrackViewParams) => {
    try {
      const { type, id, referrer, interaction = 'view' } = params;
      console.log(`[Direct] Tracking ${type} (${id}) - ${interaction}`);
      
      // Create timestamp and get user agent
      const timestamp = new Date().toISOString();
      const userAgent = navigator.userAgent || 'unknown';
      
      if (type === 'profile') {
        try {
          // Insert directly into profile_views
          await supabase.from('profile_views').insert({
            profile_id: id,
            ip_address: 'client-side',
            user_agent: userAgent,
            referrer: referrer || null
          });
        } catch (insertError) {
          // Silently log error and continue - never break the UI
          console.warn('Failed to track profile view:', insertError);
        }
      } else if (type === 'coupon') {
        try {
          // Insert directly into coupon_interactions
          await supabase.from('coupon_interactions').insert({
            coupon_id: id,
            interaction_type: interaction,
            ip_address: 'client-side',
            user_agent: userAgent,
            occurred_at: timestamp
          });
        } catch (insertError) {
          // Silently log error and continue - never break the UI
          console.warn('Failed to track coupon interaction:', insertError);
        }
      }
      
      return { success: true };
    } catch (error) {
      console.warn('Analytics tracking failed (non-critical):', error);
      // Never throw - analytics should be non-blocking
      return { success: false, silent: true };
    }
  };
  
  // Track profile shares using the Edge Function
  const trackShareDirectly = async (params: TrackShareParams) => {
    try {
      const { profile_id, platform } = params;
      console.log(`[Direct] Tracking profile share (${profile_id}) on platform: ${platform || 'unknown'}`);
      
      try {
        // Insert directly into profile_shares
        await supabase.from('profile_shares').insert({
          profile_id,
          sharer_ip: 'client-side',
          user_agent: navigator.userAgent || 'unknown',
          platform: platform || null
        });
      } catch (insertError) {
        // Silently log error and continue - never break the UI
        console.warn('Failed to track profile share:', insertError);
      }
      
      return { success: true };
    } catch (error) {
      console.warn('Share tracking failed (non-critical):', error);
      // Never throw - analytics should be non-blocking
      return { success: false, silent: true };
    }
  };
  
  // Public API methods
  const trackProfileView = (profileId: string, referrer?: string) => {
    try {
      return trackDirectly({ 
        type: 'profile', 
        id: profileId, 
        referrer 
      }).catch(() => {
        // Fail silently - analytics should never break the app
        return { success: false, silent: true };
      });
    } catch (error) {
      // Double protection - never break the UI for analytics
      console.warn('Error in trackProfileView:', error);
      return Promise.resolve({ success: false, silent: true });
    }
  };
  
  const trackProfileShare = (profileId: string, platform?: string) => {
    try {
      return trackShareDirectly({
        profile_id: profileId,
        platform
      }).catch(() => {
        // Fail silently - analytics should never break the app
        return { success: false, silent: true };
      });
    } catch (error) {
      // Double protection - never break the UI for analytics
      console.warn('Error in trackProfileShare:', error);
      return Promise.resolve({ success: false, silent: true });
    }
  };
  
  const trackCouponView = (couponId: string) => {
    try {
      return trackDirectly({ 
        type: 'coupon', 
        id: couponId, 
        interaction: 'view' 
      }).catch(() => {
        // Fail silently - analytics should never break the app
        return { success: false, silent: true };
      });
    } catch (error) {
      // Double protection - never break the UI for analytics
      console.warn('Error in trackCouponView:', error);
      return Promise.resolve({ success: false, silent: true });
    }
  };
  
  const trackCouponCopy = (couponId: string) => {
    try {
      return trackDirectly({ 
        type: 'coupon', 
        id: couponId, 
        interaction: 'copy' 
      }).catch(() => {
        // Fail silently - analytics should never break the app
        return { success: false, silent: true };
      });
    } catch (error) {
      // Double protection - never break the UI for analytics
      console.warn('Error in trackCouponCopy:', error);
      return Promise.resolve({ success: false, silent: true });
    }
  };
  
  const trackCouponUse = (couponId: string) => {
    try {
      return trackDirectly({ 
        type: 'coupon', 
        id: couponId, 
        interaction: 'use' 
      }).catch(() => {
        // Fail silently - analytics should never break the app
        return { success: false, silent: true };
      });
    } catch (error) {
      // Double protection - never break the UI for analytics
      console.warn('Error in trackCouponUse:', error);
      return Promise.resolve({ success: false, silent: true });
    }
  };
  
  return {
    trackProfileView,
    trackProfileShare,
    trackCouponView,
    trackCouponCopy,
    trackCouponUse
  };
};
