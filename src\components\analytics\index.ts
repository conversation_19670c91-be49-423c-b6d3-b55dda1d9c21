import { ReactNode } from 'react';

// Define types
export interface StatItem {
  label: string;
  value: string | number;
  icon: ReactNode;
  color: string;
  percentChange?: number; // Add percent change for growth indicators
}

// Export components still in use
export { default as PerformanceChart } from './PerformanceChart';
export { default as CouponPerformanceCard } from './CouponPerformanceCard';

// Also export other types
export type { CouponPerformanceData } from './CouponPerformanceCard';
export type { ChartDataPoint } from './PerformanceChart'; 