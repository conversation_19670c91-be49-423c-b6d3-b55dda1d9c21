import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { BookOpen } from 'lucide-react';
import SEO from '@/seo/components/SEO';
import { Link } from 'react-router-dom';

// Sample blog post data - In a real app, this would come from your backend
const blogPosts = [
  {
    id: 1,
    title: "How to Find the Best Coupon Deals Online",
    excerpt: "Learn expert tips and tricks for discovering the most valuable coupon codes and promotional offers online.",
    category: "Tips & Tricks",
    author: "CouponLink Team",
    date: "March 15, 2024",
    imageUrl: "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "how-to-find-best-coupon-deals"
  },
  {
    id: 2,
    title: "The Rise of Digital Coupons in E-commerce",
    excerpt: "Explore how digital coupons are transforming online shopping and influencing consumer behavior.",
    category: "Industry Insights",
    author: "Marketing Team",
    date: "March 10, 2024",
    imageUrl: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "rise-of-digital-coupons"
  },
  {
    id: 3,
    title: "Maximizing Savings with Premium Coupons",
    excerpt: "Discover why premium coupons offer better value and how to make the most of exclusive deals.",
    category: "Premium",
    author: "CouponLink Team",
    date: "March 5, 2024",
    imageUrl: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "maximizing-savings-premium-coupons"
  },
  // Add more blog posts as needed
];

const BlogCard = ({ post }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg transition-transform hover:scale-[1.02]">
    <img 
      src={post.imageUrl} 
      alt={post.title} 
      className="w-full h-48 object-cover"
    />
    <div className="p-6">
      <div className="flex items-center mb-4">
        <span className="text-sm font-medium text-primary-main">{post.category}</span>
        <span className="mx-2 text-gray-300">•</span>
        <span className="text-sm text-gray-500 dark:text-gray-400">{post.date}</span>
      </div>
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
        {post.title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {post.excerpt}
      </p>
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-500 dark:text-gray-400">
          By {post.author}
        </span>
        <Link 
          to={`/blog/${post.slug}`}
          className="text-primary-main hover:text-primary-dark font-medium text-sm"
        >
          Read More →
        </Link>
      </div>
    </div>
  </div>
);

const Blog = () => {
  return (
    <MainLayout>
      <SEO 
        title="Blog - CouponLink"
        description="Read the latest articles about saving money with coupons, digital deals, and smart shopping strategies."
        keywords="coupon blog, money saving tips, digital coupons, smart shopping, deal hunting"
      />
      
      <PageContainer>
        <PageHeaderWithBackButton
          title="Blog"
          subtitle="Tips, insights, and guides for smart shopping"
          icon={BookOpen}
        />
        
        {/* Categories Filter */}
        <div className="flex gap-2 mb-8 overflow-x-auto pb-2">
          {["All", "Tips & Tricks", "Industry Insights", "Premium", "How-to Guides"].map((category) => (
            <button
              key={category}
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap
                ${category === "All" 
                  ? "bg-primary-main text-white" 
                  : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogPosts.map((post) => (
            <BlogCard key={post.id} post={post} />
          ))}
        </div>

        {/* Newsletter Subscription */}
        <div className="mt-12 bg-gradient-to-r from-primary-main/10 to-secondary-main/10 rounded-xl p-8">
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Stay Updated with Latest Deals
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Subscribe to our newsletter to receive the latest coupon tips and exclusive deals directly in your inbox.
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
              />
              <button className="px-6 py-2 bg-primary-main text-white rounded-lg hover:bg-primary-dark transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default Blog; 