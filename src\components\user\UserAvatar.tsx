import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { getAvatarUrl } from '@/utils/avatarUtils';

interface UserAvatarProps {
  avatarUrl?: string | null;
  fullName?: string;
  userId?: string;
  email?: string;
  className?: string;
}

const UserAvatar = ({ avatarUrl, fullName, userId, email, className }: UserAvatarProps) => {
  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Get the appropriate avatar URL (custom or random default)
  const finalAvatarUrl = getAvatarUrl(avatarUrl, userId, email);

  return (
    <Avatar className={cn('relative', className)}>
      <AvatarImage
        src={finalAvatarUrl}
        alt={fullName || 'User avatar'}
        className="object-cover"
      />
      <AvatarFallback>
        {fullName ? getInitials(fullName) : '??'}
      </AvatarFallback>
    </Avatar>
  );
};

export default UserAvatar; 