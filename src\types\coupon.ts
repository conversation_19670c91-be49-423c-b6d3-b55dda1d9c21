export interface Coupon {
  id: string;
  title: string;
  code: string;
  discount_percent?: number;
  discount_amount?: number;
  discount_description?: string;
  expires_at?: string;
  status: 'active' | 'expired' | 'draft';
  featured?: boolean;
  price?: number;
  is_premium?: boolean;
  affiliate_link?: string;
  view_count?: number;
  copy_count?: number;
  user_id?: string;
  brand_id?: string;
  category_id?: string;
  influencer_id?: string;
  brand?: {
    id: string;
    name: string;
    logo_url?: string;
    website?: string;
  };
  category?: {
    id: string;
    name: string;
  };
  influencer?: {
    id: string;
    full_name: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  updated_at?: string;
  active?: boolean;
}

// Extended version with analytics fields
export interface ExtendedCoupon extends Coupon {
  click_count?: number;
  copy_count?: number;
  view_count?: number;
} 