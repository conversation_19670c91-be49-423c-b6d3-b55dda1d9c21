import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { useAuth } from '@/context/AuthContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { AlertCircle, ArrowDown, ArrowUp, DollarSign, Eye, ExternalLink, Lock, Unlock, WalletCards, Receipt } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Transaction {
  id: string;
  buyer_id: string;
  seller_id: string;
  coupon_id: string;
  amount: number;
  platform_fee: number;
  seller_amount: number;
  payment_id: string;
  payment_method: string;
  status: string;
  created_at: string;
  coupon: {
    title: string;
    code: string;
    discount_percent: number;
    discount_description: string;
    is_premium: boolean;
  };
  buyer: {
    id: string;
    full_name: string;
    username: string;
    avatar_url: string;
  };
  seller: {
    id: string;
    full_name: string;
    username: string;
    avatar_url: string;
  };
}

const TransactionsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all');
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['transactions', user?.id, activeTab],
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated');
      
      // Get all transactions involving the user (as buyer or seller)
      const { data, error } = await supabase
        .from('transactions')
        .select(`
          *,
          coupon:coupons(
            title, 
            code, 
            discount_percent, 
            discount_description,
            is_premium
          ),
          buyer:profiles!buyer_id(
            id, 
            full_name, 
            username, 
            avatar_url
          ),
          seller:profiles!seller_id(
            id, 
            full_name, 
            username, 
            avatar_url
          )
        `)
        .or(`buyer_id.eq.${user.id},seller_id.eq.${user.id}`)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Calculate summary stats
      const totalEarned = data
        .filter(t => t.seller_id === user.id && t.status === 'completed')
        .reduce((sum, t) => sum + parseFloat(t.seller_amount), 0);
        
      const totalSpent = data
        .filter(t => t.buyer_id === user.id && t.status === 'completed')
        .reduce((sum, t) => sum + parseFloat(t.amount), 0);
      
      return {
        transactions: data as Transaction[],
        totalEarned,
        totalSpent,
        transactionCount: data.length
      };
    },
    enabled: !!user,
  });
  
  if (isLoading) {
    return (
      <MainLayout>
        <PageContainer decorationType="default" decorationOpacity={0.6}>
          <PageHeaderWithBackButton
            title="Transactions"
            subtitle="Your transaction history and earnings"
            icon={Receipt}
          />
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Skeleton className="h-[120px] w-full rounded-lg" />
              <Skeleton className="h-[120px] w-full rounded-lg" />
              <Skeleton className="h-[120px] w-full rounded-lg" />
            </div>
            <Skeleton className="h-[400px] w-full rounded-lg" />
          </div>
        </PageContainer>
      </MainLayout>
    );
  }
  
  if (error || !data) {
    return (
      <MainLayout>
        <PageContainer decorationType="default" decorationOpacity={0.6}>
          <PageHeaderWithBackButton
            title="Transactions"
            subtitle="Your transaction history and earnings"
            icon={Receipt}
          />
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <AlertCircle className="w-10 h-10 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-red-800 mb-2">Failed to load transaction data</h3>
            <p className="text-red-600">Please try again later or contact support if the problem persists.</p>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Handle coupon view
  const handleCouponView = (couponId: string) => {
    navigate(`/coupon/${couponId}`);
  };
  
  // Filter transactions based on active tab
  const filteredTransactions = data.transactions.filter(transaction => {
    if (activeTab === 'all') return true;
    if (activeTab === 'sales') return transaction.seller_id === user.id;
    if (activeTab === 'purchases') return transaction.buyer_id === user.id;
    return true;
  });
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Transactions"
          subtitle="Your transaction history and earnings"
          icon={Receipt}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earned</CardTitle>
              <ArrowDown className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.totalEarned)}</div>
              <p className="text-xs text-muted-foreground">
                From selling premium coupons
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <ArrowUp className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.totalSpent)}</div>
              <p className="text-xs text-muted-foreground">
                On premium coupon purchases
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transaction Count</CardTitle>
              <WalletCards className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.transactionCount}</div>
              <p className="text-xs text-muted-foreground">
                Total number of transactions
              </p>
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>
                  Your complete transaction history for premium coupons
                </CardDescription>
              </div>
              
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-auto">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="sales">Sales</TabsTrigger>
                  <TabsTrigger value="purchases">Purchases</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableCaption>A list of your recent transactions</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Coupon</TableHead>
                    <TableHead>{activeTab === 'purchases' ? 'Seller' : activeTab === 'sales' ? 'Buyer' : 'User'}</TableHead>
                    <TableHead>Total</TableHead>
                    {activeTab === 'sales' && <TableHead>Your Earnings</TableHead>}
                    {activeTab === 'sales' && <TableHead>Platform Fee</TableHead>}
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={activeTab === 'sales' ? 7 : 5} className="text-center py-8 text-gray-500">
                        No transactions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTransactions.map((transaction) => {
                      const isSeller = transaction.seller_id === user.id;
                      const otherParty = isSeller ? transaction.buyer : transaction.seller;
                      
                      return (
                        <TableRow key={transaction.id}>
                          <TableCell className="whitespace-nowrap">
                            {formatDate(transaction.created_at)}
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{transaction.coupon?.title || 'Unknown Coupon'}</div>
                            <div className="text-xs text-gray-500">
                              {transaction.coupon?.discount_description || 
                               `${transaction.coupon?.discount_percent || 0}% off`}
                            </div>
                            <div className="flex items-center mt-1">
                              {transaction.coupon?.is_premium ? (
                                isSeller ? (
                                  <div className="flex items-center">
                                    <span className="text-xs font-mono bg-blue-50 text-blue-700 px-1.5 py-0.5 rounded">
                                      {transaction.coupon.code}
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <span className="text-xs font-mono bg-green-50 text-green-700 px-1.5 py-0.5 rounded flex items-center gap-1">
                                      <Unlock className="w-3 h-3" />
                                      {transaction.coupon.code}
                                    </span>
                                  </div>
                                )
                              ) : null}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="h-8 w-8 rounded-full bg-gray-200 overflow-hidden">
                                {otherParty?.avatar_url ? (
                                  <img 
                                    src={otherParty.avatar_url} 
                                    alt={otherParty.full_name || 'User'} 
                                    className="h-full w-full object-cover"
                                  />
                                ) : (
                                  <div className="h-full w-full flex items-center justify-center bg-blue-100 text-blue-800 text-xs font-medium">
                                    {(otherParty?.full_name || 'U')[0].toUpperCase()}
                                  </div>
                                )}
                              </div>
                              <div>
                                {otherParty?.full_name || 'Anonymous'}
                                {otherParty?.username && (
                                  <div className="text-xs text-gray-500">@{otherParty.username}</div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          {isSeller && (
                            <TableCell className="text-green-600 font-medium whitespace-nowrap">
                              {formatCurrency(transaction.seller_amount)}
                            </TableCell>
                          )}
                          {isSeller && (
                            <TableCell className="text-gray-600 whitespace-nowrap">
                              {formatCurrency(transaction.platform_fee)}
                              <div className="text-xs text-gray-500">
                                ({((transaction.platform_fee / transaction.amount) * 100).toFixed(1)}%)
                              </div>
                            </TableCell>
                          )}
                          <TableCell>
                            <span className={`px-2 py-1 text-xs rounded-full whitespace-nowrap ${
                              transaction.status === 'completed' 
                                ? 'bg-green-100 text-green-800' 
                                : transaction.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-gray-100 text-gray-800'
                            }`}>
                              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => handleCouponView(transaction.coupon_id)}
                                title="View coupon details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {transaction.payment_id && (
                                <Button 
                                  size="icon"
                                  variant="ghost"
                                  title="View on Stripe"
                                  onClick={() => window.open(`https://dashboard.stripe.com/payments/${transaction.payment_id}`, '_blank')}
                                >
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
        
        {/* Info card with transaction explanation */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>How Payments Work</CardTitle>
            <CardDescription>
              Understanding the payment process for premium coupons
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium text-base mb-2">For Buyers</h3>
              <p className="text-sm text-gray-600">
                When you purchase a premium coupon, you get exclusive access to special discount codes not available to regular users.
                These premium coupons often provide higher discounts and are verified to work.
                Your payment goes directly to the influencer who created the coupon, with a small platform fee.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-base mb-2">For Sellers (Influencers)</h3>
              <p className="text-sm text-gray-600">
                When a user purchases your premium coupon, you receive the payment minus a small platform fee of {activeTab === 'sales' && data.transactions.length > 0 ? `${((data.transactions[0].platform_fee / data.transactions[0].amount) * 100).toFixed(1)}%` : '5%'}.
                This creates a direct revenue stream between you and your audience. 
                Payments are processed securely through Stripe.
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-base mb-2">Platform Fee</h3>
              <p className="text-sm text-gray-600">
                The platform charges a minimal fee on each transaction to cover processing costs and maintain the service.
                Most of the payment goes directly to the coupon creator.
              </p>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    </MainLayout>
  );
};

export default TransactionsPage; 