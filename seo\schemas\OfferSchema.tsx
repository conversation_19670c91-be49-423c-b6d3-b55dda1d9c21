import React from 'react';
import Head from 'next/head';

interface OfferSchemaProps {
  brandName: string;
  couponCode: string;
  description: string;
  discount: string; // e.g., "20% OFF", "$15 OFF"
  expirationDate: string; // ISO format: "2023-12-31"
  validFrom?: string; // ISO format: "2023-01-01"
  category?: string;
  image?: string;
  url: string;
  successRate?: number; // 0-100
  usageCount?: number;
  termsAndConditions?: string;
}

const OfferSchema: React.FC<OfferSchemaProps> = ({
  brandName,
  couponCode,
  description,
  discount,
  expirationDate,
  validFrom,
  category,
  image,
  url,
  successRate,
  usageCount,
  termsAndConditions
}) => {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Offer',
    name: `${brandName} Coupon: ${discount}`,
    description: description,
    category: category || 'Coupon',
    image: image || `https://couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}/logo.png`,
    url: url,
    offeredBy: {
      '@type': 'Organization',
      name: brandName,
      logo: image || `https://couponlink.in/brands/${brandName.toLowerCase().replace(/\s+/g, '-')}/logo.png`
    },
    priceValidUntil: expirationDate,
    validFrom: validFrom || new Date().toISOString().split('T')[0],
    priceCurrency: 'USD',
    availability: 'https://schema.org/InStock',
    itemCondition: 'https://schema.org/NewCondition',
    potentialAction: {
      '@type': 'UseAction',
      actionStatus: 'https://schema.org/PotentialActionStatus',
      object: {
        '@type': 'Coupon',
        code: couponCode
      },
      target: {
        '@type': 'EntryPoint',
        urlTemplate: url
      }
    }
  };

  // Add optional aggregated ratings if success rate is provided
  if (successRate) {
    schema['aggregateRating'] = {
      '@type': 'AggregateRating',
      ratingValue: (successRate / 20).toFixed(1), // Convert to 5-star scale
      bestRating: '5',
      worstRating: '1',
      ratingCount: usageCount || 10,
      reviewCount: Math.floor((usageCount || 10) * 0.3) // Assume 30% of users leave reviews
    };
  }

  // Add terms and conditions if provided
  if (termsAndConditions) {
    schema['termsOfService'] = termsAndConditions;
  }

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    </Head>
  );
};

export default OfferSchema; 