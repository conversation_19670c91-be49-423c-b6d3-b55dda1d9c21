import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Calendar, 
  Gift, 
  ShoppingBag, 
  Snowflake, 
  Heart, 
  Star,
  TrendingUp,
  Clock,
  Percent,
  ArrowRight
} from 'lucide-react';
import { Link } from 'react-router-dom';

const SeasonalShoppingGuide = () => {
  const seasonalTips = [
    {
      season: "Black Friday",
      icon: ShoppingBag,
      color: "from-red-500 to-red-600",
      bgColor: "bg-red-50",
      textColor: "text-red-600",
      tips: [
        "Start your wishlist early and track prices",
        "Sign up for brand newsletters for exclusive deals",
        "Compare prices across multiple retailers",
        "Check return policies before purchasing"
      ],
      link: "/deals/black-friday"
    },
    {
      season: "Cyber Monday",
      icon: TrendingUp,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
      tips: [
        "Focus on electronics and tech deals",
        "Use cashback apps and browser extensions",
        "Clear your browser cache for better performance",
        "Shop early for the best selection"
      ],
      link: "/deals/cyber-monday"
    },
    {
      season: "Holiday Season",
      icon: Gift,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
      tips: [
        "Set a budget and stick to it",
        "Buy gifts throughout the year to spread costs",
        "Consider experiences over material gifts",
        "Don't forget about shipping deadlines"
      ],
      link: "/deals/holiday"
    }
  ];

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.8}>
        <PageHeaderWithBackButton
          title="Seasonal Shopping Guide"
          subtitle="Your complete guide to seasonal deals and smart shopping"
          icon={Calendar}
        />

        {/* Introduction Section */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="flex items-start gap-6">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-xl">
                <Star className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-4 text-gray-800">
                  Master the Art of Seasonal Shopping
                </h2>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  Shopping seasonally isn't just about finding great deals—it's about being strategic, 
                  prepared, and making the most of your money throughout the year. Our comprehensive 
                  guide will help you navigate the biggest shopping events and save significantly on 
                  your purchases.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <Percent className="h-5 w-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-700">Up to 80% savings</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-700">Year-round planning</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Heart className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-green-700">Smart shopping tips</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Seasonal Shopping Tips */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-6 text-gray-800">Seasonal Shopping Strategies</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {seasonalTips.map((season, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className={`bg-gradient-to-r ${season.color} p-3 rounded-lg mr-4`}>
                      <season.icon className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-800">{season.season}</h4>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {season.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start gap-2">
                        <div className={`w-2 h-2 rounded-full ${season.color.replace('from-', 'bg-').replace(' to-red-600', '').replace(' to-blue-600', '').replace(' to-green-600', '')} mt-2 flex-shrink-0`}></div>
                        <span className="text-gray-600 text-sm">{tip}</span>
                      </li>
                    ))}
                  </ul>
                  <Link to={season.link}>
                    <Button 
                      variant="outline" 
                      className={`w-full group-hover:${season.textColor} group-hover:border-current transition-colors`}
                    >
                      View {season.season} Deals
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* General Shopping Tips */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
              <Snowflake className="h-6 w-6 mr-3 text-blue-500" />
              Year-Round Shopping Tips
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-semibold mb-4 text-gray-700">Before You Shop</h4>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">1</Badge>
                    <span className="text-gray-600">Create a budget and shopping list</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">2</Badge>
                    <span className="text-gray-600">Research prices and read reviews</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">3</Badge>
                    <span className="text-gray-600">Sign up for newsletters and alerts</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">4</Badge>
                    <span className="text-gray-600">Download cashback and coupon apps</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4 text-gray-700">While Shopping</h4>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">1</Badge>
                    <span className="text-gray-600">Compare prices across multiple sites</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">2</Badge>
                    <span className="text-gray-600">Check for coupon codes before checkout</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">3</Badge>
                    <span className="text-gray-600">Read return and warranty policies</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Badge variant="secondary" className="mt-0.5">4</Badge>
                    <span className="text-gray-600">Consider shipping costs and timing</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Start Saving?</h3>
            <p className="text-purple-100 mb-6 text-lg">
              Explore our curated collection of seasonal deals and start your smart shopping journey today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/categories">
                <Button size="lg" variant="secondary" className="bg-white text-purple-600 hover:bg-gray-100">
                  Browse All Categories
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </Link>
              <Link to="/trending">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                  View Trending Deals
                  <TrendingUp className="h-5 w-5 ml-2" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    </MainLayout>
  );
};

export default SeasonalShoppingGuide;
