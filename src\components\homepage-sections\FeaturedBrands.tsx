import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { useTopBrands } from '@/hooks/useTopBrands';
import { useIsMobile } from '@/hooks/use-mobile';
import { COLORS } from '@/constants/theme';

interface Brand {
  id: string;
  name: string;
  logo_url: string;
  couponCount?: number;
}

interface FeaturedBrandsProps {
  limit?: number;
}

const FeaturedBrands = ({ limit = 6 }: FeaturedBrandsProps) => {
  const { data: topBrands, isLoading, error } = useTopBrands(limit);
  const isMobile = useIsMobile();
  const displayLimit = isMobile ? 4 : limit;
  
  // Get brands from API data only
  const brands = topBrands?.slice(0, displayLimit) || [];
  
  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  return (
    <section className="featured-brands-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex items-center justify-between mb-8">
          <motion.h2 
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-2xl font-bold text-gray-900 dark:text-white flex items-center"
          >
            <span 
              className="p-2 rounded-lg mr-3"
              style={{ 
                backgroundColor: COLORS.accent.bgLight,
                color: COLORS.accent.main 
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </span>
            Featured Brands
          </motion.h2>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link 
              to="/brands" 
              className="flex items-center text-sm font-medium group"
              style={{ 
                color: COLORS.accent.main
              }}
            >
              View all brands <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </div>

        {isLoading ? (
          // Loading state
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[...Array(displayLimit)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 shadow-md rounded-xl p-6 animate-pulse">
                <div className="h-12 w-24 mx-auto bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-red-600 dark:text-red-400 text-center">
            Unable to load featured brands. Please try again later.
          </div>
        ) : brands.length > 0 ? (
          // Success state with brands
          <motion.div 
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            className={`grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-4`}
          >
            {brands.map((brand) => (
              <motion.div
                key={brand.id}
                variants={item}
                transition={{ duration: 0.3 }}
                whileHover={{ y: -5, scale: 1.03 }}
                className="brand-card"
              >
                <Link 
                  to={`/brands/${brand.id}`}
                  className="w-full flex flex-col items-center group"
                >
                  <div className="brand-logo-container">
                    <img 
                      src={brand.logo_url} 
                      alt={brand.name} 
                      className="brand-logo" 
                      onError={(e) => {
                        // Fallback if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(brand.name)}&background=random`;
                      }}
                    />
                  </div>
                  <h3 className="brand-name">
                    {brand.name}
                  </h3>
                  {brand.couponCount !== undefined && (
                    <p className="brand-coupon-count">
                      {brand.couponCount} {brand.couponCount === 1 ? 'coupon' : 'coupons'}
                    </p>
                  )}
                </Link>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          // No brands state
          <div className="bg-white/70 backdrop-blur-sm dark:bg-gray-800/70 rounded-xl p-8 text-center shadow-md border border-white/40 dark:border-gray-700/40">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Featured Brands</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              There are no featured brands to display at the moment.
            </p>
            <Link to="/brands">
              <button 
                className="font-medium hover:underline"
                style={{ color: COLORS.accent.main }}
              >
                View All Brands
              </button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedBrands; 