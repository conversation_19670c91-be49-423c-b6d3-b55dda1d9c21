import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Export the collections hook
export const useCollections = (userId?: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['collections', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      console.log("Fetching collections for user:", userId);
      
      try {
        const { data, error } = await supabase
          .from('collections')
          .select(`
            *,
            saved_coupons:saved_coupons(count)
          `)
          .eq('user_id', userId)
          .order('name');
        
        if (error) {
          console.error('Error fetching collections:', error);
          throw error;
        }
        
        // Format the collections with a coupon count
        const formattedCollections = data?.map(collection => ({
          ...collection,
          coupon_count: collection.saved_coupons?.[0]?.count || 0
        })) || [];
        
        console.log("Fetched collections:", formattedCollections);
        return formattedCollections;
      } catch (error) {
        console.error('Error in useCollections query:', error);
        throw error;
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled : !!userId,
    retry: 1,
  });
}; 