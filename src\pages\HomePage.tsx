import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import HeroSection from '@/components/homepage-sections/HeroSection';
import FeaturedBrands from '@/components/homepage-sections/FeaturedBrands';
import TrendingDealsSection from '@/components/homepage-sections/TrendingDealsSection';
import CategoriesSection from '@/components/homepage-sections/CategoriesSection';
import PremiumOffers from '@/components/homepage-sections/PremiumOffers';
import PageContainer from '@/components/layout/PageContainer';
import SEO from '@/seo/components/SEO';
import WebsiteSchema from '@/seo/schemas/WebsiteSchema';
import OrganizationSchema from '@/seo/schemas/OrganizationSchema';
import SearchResultSchema from '@/seo/schemas/SearchResultSchema';
import { Helmet } from 'react-helmet-async';
import {
  ArrowRight,
  Tag,
  Zap,
  Monitor,
  Smartphone,
  ShoppingBag,
  Shirt,
  Users,
  Gift,
  Snowflake,
  Calendar,
  TrendingUp,
  Sparkles
} from 'lucide-react';

/**
 * Homepage for the CouponLink platform
 * Enhanced with rich structured data for search results like Perplexity
 */
const HomePage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [isStartMenuOpen, setIsStartMenuOpen] = useState(false);
  
  // Handle search submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };
  
  // Handler for Browse Deals button
  const handleBrowseDealsClick = () => {
    setIsStartMenuOpen(true);
  };
  
  // Create sections for search results schema
  const searchSections = [
    {
      name: 'Featured Brands',
      description: 'Top brands with active coupon codes and promotional offers',
      url: 'https://www.couponlink.in/#featured-brands',
      image: '/logos/featured-brands.png'
    },
    {
      name: 'Trending Deals',
      description: 'Most popular and recently verified coupon codes',
      url: 'https://www.couponlink.in/#trending-deals',
      image: '/logos/trending-deals.png'
    },
    {
      name: 'Categories',
      description: 'Browse coupons by shopping category',
      url: 'https://www.couponlink.in/#categories',
      image: '/logos/categories.png'
    },
    {
      name: 'Latest Coupons',
      description: 'Recently added and verified coupon codes',
      url: 'https://www.couponlink.in/#latest-coupons',
      image: '/logos/latest-coupons.png'
    },
    {
      name: 'Popular Stores',
      description: 'Most visited stores with active promotions',
      url: 'https://www.couponlink.in/#popular-stores',
      image: '/logos/popular-stores.png'
    }
  ];
  
  return (
    <>
      {/* Enhanced SEO components */}
      <SEO
        title="CouponLink - Find & Share Verified Coupon Codes and Promo Offers | Link in Bio Deals"
        description="Discover the best coupon codes, promo offers, and discount deals from top brands. Perfect for link in bio, influencer codes, and social media deals. Save money with verified promotional codes updated daily at CouponLink."
        image="/images/couponlink-search-preview.png"
        type="website"
        modifiedTime={new Date().toISOString()}
      />
      
      {/* Website schema */}
      <WebsiteSchema />
      
      {/* Organization schema with logo */}
      <OrganizationSchema />
      
      {/* Search result schema for multiple sections */}
      <SearchResultSchema
        mainEntity={{
          name: 'CouponLink - Verified Coupon Codes & Promo Offers',
          description: 'Find and share verified coupon codes, promo offers, and discounts from your favorite brands.',
          url: 'https://www.couponlink.in'
        }}
        sections={searchSections}
      />
      
      <MainLayout fullWidth
        isStartMenuOpen={isStartMenuOpen}
        setIsStartMenuOpen={setIsStartMenuOpen}
      >
        <PageContainer fullWidth decorationType="landing" decorationOpacity={0.8}>
          {/* Hero Section with Search */}
          <HeroSection
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            handleSearch={handleSearch}
            onBrowseDealsClick={handleBrowseDealsClick}
          />

          {/* Featured Brands Grid */}
          <FeaturedBrands limit={6} />

          {/* Trending Deals Carousel */}
          <TrendingDealsSection />

          {/* Popular Categories Grid */}
          <CategoriesSection />

          {/* Premium Offers with CTA */}
          <PremiumOffers />

          {/* Popular Deal Categories Section - Enhanced UI */}
          <div className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 relative overflow-hidden -mx-4 px-4 md:-mx-8 md:px-8">
            {/* Background decoration */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)]"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)]"></div>

            <div className="relative z-10 max-w-7xl mx-auto">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-12 text-center md:text-left">
                <div className="mb-6 md:mb-0">
                  <div className="flex items-center justify-center md:justify-start mb-4">
                    <div className="bg-gradient-to-r from-pink-500 to-purple-600 p-3 rounded-xl mr-4 shadow-lg">
                      <Sparkles className="h-7 w-7 text-white" />
                    </div>
                    <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                      Explore Popular Deal Categories
                    </h2>
                  </div>
                  <p className="text-gray-600 text-lg max-w-2xl mx-auto md:mx-0">
                    Discover amazing deals across your favorite categories and brands
                  </p>
                </div>
                <Link
                  to="/categories"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
                >
                  View All Categories
                  <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Electronics Deals Card */}
                <div className="group bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-white/50 hover:border-blue-200/50 transform hover:-translate-y-2">
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-3 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-300">
                      <Monitor className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">
                      Electronics Deals
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <Link
                      to="/categories/electronics/laptops"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-blue-600 font-medium">
                        Best Laptop Discount Codes
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-blue-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/categories/electronics/smartphones"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-blue-600 font-medium">
                        Smartphone Coupon Deals
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-blue-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/brands?name=amazon"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-blue-600 font-medium">
                        Amazon Electronics Sale
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-blue-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/brands?name=best-buy"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-blue-600 font-medium">
                        Best Buy Special Offers
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-blue-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                  </div>
                </div>

                {/* Fashion Savings Card */}
                <div className="group bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-white/50 hover:border-pink-200/50 transform hover:-translate-y-2">
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-r from-pink-500 to-rose-500 p-3 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-300">
                      <Shirt className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 group-hover:text-pink-600 transition-colors">
                      Fashion Savings
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <Link
                      to="/categories/fashion/womens"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-pink-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-pink-600 font-medium">
                        Women's Fashion Promo Codes
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-pink-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/categories/fashion/mens"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-pink-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-pink-600 font-medium">
                        Men's Clothing Discount Deals
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-pink-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/brands?name=macys"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-pink-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-pink-600 font-medium">
                        Macy's Coupon Specials
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-pink-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/brands?name=target"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-pink-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-pink-600 font-medium">
                        Target Fashion Discounts
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-pink-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                  </div>
                </div>

                {/* Seasonal Promotions Card */}
                <div className="group bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-white/50 hover:border-purple-200/50 transform hover:-translate-y-2">
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-3 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-300">
                      <Gift className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 group-hover:text-purple-600 transition-colors">
                      Seasonal Promotions
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <Link
                      to="/deals/black-friday"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-purple-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-purple-600 font-medium">
                        Black Friday Deals Guide
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-purple-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/deals/cyber-monday"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-purple-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-purple-600 font-medium">
                        Cyber Monday Coupon Codes
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-purple-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/deals/holiday"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-purple-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-purple-600 font-medium">
                        Holiday Season Discounts
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-purple-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                    <Link
                      to="/blog/seasonal-shopping-guide"
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-purple-50 transition-colors group/item"
                    >
                      <span className="text-gray-700 group-hover/item:text-purple-600 font-medium">
                        Seasonal Shopping Guide
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover/item:text-purple-500 group-hover/item:translate-x-1 transition-all" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PageContainer>
      </MainLayout>
    </>
  );
};

export default HomePage; 