# CouponLink.in - SEO-Optimized Coupon Website

This repository contains the source code for CouponLink.in, a coupon and promo code website designed with best-in-class SEO practices to rank highly in search results.

## Project Structure

```
couponlink/
├── components/
│   ├── schemas/            # Schema.org markup components
│   │   ├── FAQSchema.tsx   # FAQ structured data
│   │   └── OfferSchema.tsx # Coupon/offer structured data
│   └── templates/          # Page templates
│       └── BrandCouponPage.tsx # Brand coupon page template
├── docs/
│   ├── coupon-seo-strategy.md  # SEO strategy documentation
│   └── traffic-growth-plan.md  # Traffic growth strategy
├── lib/                    # Shared utility functions
├── pages/                  # Next.js pages
│   ├── api/                # API routes
│   ├── brands/             # Brand pages
│   │   └── [brand].tsx     # Dynamic brand page
│   └── index.tsx           # Homepage
├── public/                 # Static assets
│   ├── images/             # Image assets
│   ├── robots.txt          # Robots directives for crawlers
│   └── sitemap.xml         # XML sitemap
└── styles/                 # CSS styles
```

## SEO Implementation

This project includes comprehensive SEO optimizations:

### Technical SEO

- **Semantic HTML Structure**: Proper heading hierarchy and HTML5 elements
- **Schema.org Markup**: Rich structured data for coupons, FAQs, and organization
- **XML Sitemap**: Comprehensive sitemap with priority levels
- **Robots.txt**: Optimized crawler directives
- **URL Structure**: SEO-friendly URLs with keywords (e.g., /brands/amazon-coupon)
- **Metadata**: Complete meta tags including title, description, and Open Graph

### On-Page SEO

- **Optimized Page Templates**: Brand pages with proper keyword structure
- **Rich Snippets Support**: FAQ, offers, and ratings structured data
- **Internal Linking**: Smart internal linking between related content
- **Freshness Signals**: Last-verified dates and timestamps
- **Mobile Optimization**: Responsive design for all devices

### Content Strategy

- **Brand-Specific Content**: Unique descriptions for each brand
- **FAQ Sections**: Common questions to target long-tail keywords
- **Featured Deals**: Highlighting popular offers
- **Category Pages**: Content organized by shopping categories

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```

2. Run the development server:
   ```
   npm run dev
   ```

3. Open http://localhost:3000 to see the site

## SEO Documentation

For detailed SEO strategies, see the following documents:

- [SEO Strategy Document](docs/coupon-seo-strategy.md)
- [Traffic Growth Plan](docs/traffic-growth-plan.md)

## Search Engine Ranking Features

Key features implemented to improve search rankings:

1. **Coupon Schema Markup**: Structured data for all offers with validity periods
2. **Success Rate Display**: Shows users and search engines verification status
3. **Freshness Indicators**: Regular content updates with timestamps
4. **Breadcrumb Navigation**: Structured data for site hierarchy
5. **Verified Badge System**: Trust signals for users and search engines
6. **Custom Metadata**: Unique titles and descriptions for all pages
7. **Search Box Schema**: Enables sitelinks search box in search results
8. **Mobile Performance**: Fast-loading pages optimized for mobile devices
