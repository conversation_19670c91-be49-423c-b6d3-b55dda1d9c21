import MainLayout from '@/components/layout/MainLayout';
import { useProfileAnalytics } from '@/hooks/useProfileAnalytics';
import BackButton from '@/components/BackButton';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON>,
  MousePointer, 
  Eye, 
  Percent, 
  Calendar,
  TrendingUp,
  TrendingDown,
  Share2,
  Download,
  RefreshCw,
  BarChart4,
  Users,
  ExternalLink,
  Award,
  Check,
  ArrowRight,
  Loader2,
  Plus
} from 'lucide-react';
import { 
  PerformanceChart, 
  ChartDataPoint 
} from '@/components/analytics';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { COLORS } from '@/constants/theme';
import { motion } from 'framer-motion';
import { useOnboarding } from '@/context/OnboardingContext';

const AnalyticsDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { handleStepCompletion, completeOnboarding } = useOnboarding();
  
  // Check if we're in onboarding mode
  const searchParams = new URLSearchParams(location.search);
  const isOnboarding = searchParams.get('onboarding') === 'true';
  const onboardingStep = searchParams.get('step');
  

  
  // State for filtering and date range
  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });
  const [timeFilter, setTimeFilter] = useState('30days');
  const [isExporting, setIsExporting] = useState(false);
  
  // Update date range based on time filter
  const updateDateRange = (filter: string) => {
    const today = new Date();
    let from = new Date();
    
    switch (filter) {
      case '7days':
        from = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        from = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90days':
        from = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        from = new Date(today.getFullYear(), 0, 1);
        break;
      default:
        from = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    setDateRange({ from, to: today });
  };
  
  // Use analytics hook with updated date range
  const { 
    analytics: data, 
    isLoading, 
    refetch 
  } = useProfileAnalytics(user?.id);
  
  // Refetch data when date range changes
  useEffect(() => {
    refetch();
  }, [dateRange, refetch]);
  
  // Calculate conversion rate
  const conversionRate = ((data?.couponCopies || 0) / (data?.totalViews || 1) * 100).toFixed(1);
  
  // Prepare summary statistics
  const summaryStats = [
    {
      label: "Total Coupon Clicks",
      value: data?.couponClicks || 0,
      icon: <MousePointer className="h-6 w-6 text-primary-main" />,
      color: "primary",
      percentChange: data?.clicksPercentChange
    },
    {
      label: "Total Coupon Views",
      value: data?.totalViews || 0,
      icon: <Eye className="h-6 w-6 text-tertiary-main" />,
      color: "tertiary",
      percentChange: data?.viewsPercentChange
    },
    {
      label: "Conversion Rate",
      value: `${conversionRate}%`,
      icon: <Percent className="h-6 w-6 text-primary-main" />,
      color: "primary",
      percentChange: data?.copiesPercentChange
    },
    {
      label: "Premium Sales",
      value: data?.premiumSales || 0,
      icon: <Award className="h-6 w-6 text-secondary-main" />,
      color: "secondary",
      percentChange: data?.revenuePercentChange
    }
  ];
  
  // Growth opportunities based on real data
  const growthOpportunities = [
    {
      title: "Increase Social Traffic",
      description: data?.trafficSources?.social < 30 
        ? "Your social media traffic is low. Consider sharing your coupons on social platforms."
        : "Keep growing your social media presence to maintain high traffic.",
      icon: <Share2 className="h-8 w-8 text-tertiary-main" />,
      color: "bg-tertiary-bgLight",
      secondaryColor: "text-tertiary-dark"
    },
    {
      title: "Optimize Conversion Rate",
      description: Number(conversionRate) < 10
        ? "Your conversion rate is below average. Try A/B testing different coupon descriptions."
        : "Your conversion rate is good. Keep testing to maintain performance.",
      icon: <TrendingUp className="h-8 w-8 text-primary-main" />,
      color: "bg-primary-bgLight",
      secondaryColor: "text-primary-dark"
    }
  ];
  
  // Handle export of analytics data
  const handleExportData = async () => {
    if (!data || isExporting) return;
    
    try {
      setIsExporting(true);
      
      const exportData = {
        summary: {
          clicks: data?.couponClicks || 0,
          views: data?.totalViews || 0,
          copies: data?.couponCopies || 0,
          date_range: {
            from: dateRange.from.toISOString(),
            to: dateRange.to.toISOString()
          }
        },
        daily_interactions: data?.dailyInteractions || []
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_export_${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      
      toast.success('Analytics data exported successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export analytics data');
    } finally {
      setIsExporting(false);
    }
  };

  // Prepare chart data
  const getChartData = (): ChartDataPoint[] => {
    if (!data?.dailyInteractions || data.dailyInteractions.length === 0) {
      return [];
    }

    // Group by date
    const dateGroups: Record<string, ChartDataPoint> = {};
    
    data.dailyInteractions.forEach(interaction => {
      const date = interaction.occurred_at;
      if (!dateGroups[date]) {
        dateGroups[date] = { label: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) };
      }
      
      if (interaction.interaction_type === 'view') {
        dateGroups[date].views = interaction.count;
      } else if (interaction.interaction_type === 'click') {
        dateGroups[date].clicks = interaction.count;
      } else if (interaction.interaction_type === 'copy') {
        dateGroups[date].uses = interaction.count;
      }
    });
    
    // Convert to array and sort by date
    return Object.values(dateGroups).sort((a, b) => {
      const dateA = new Date(a.label);
      const dateB = new Date(b.label);
      return dateA.getTime() - dateB.getTime();
    });
  };
  
  return (
    <MainLayout>
      <div className="px-4 py-6 max-w-6xl mx-auto">
        {/* Top navigation */}
        <div className="mb-6">
          <BackButton label="Back to Dashboard" />
        </div>
        
        {/* Page title with gradient */}
        <motion.div 
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 
            className="text-3xl sm:text-4xl font-bold mb-2"
            style={{ color: COLORS.neutral[800] }}
          >
            Analytics Dashboard
            <span 
              className="block text-2xl sm:text-3xl bg-clip-text text-transparent"
              style={{ backgroundImage: COLORS.primary.gradient }}
            >
              Monitor Your Performance
            </span>
          </h1>
        </motion.div>
        
        {/* Date and filters section */}
        <div 
          className="flex flex-wrap items-center justify-end gap-2 mb-8 rounded-xl p-4 shadow-sm"
          style={{ 
            background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
            border: `1px solid rgba(255, 255, 255, 0.2)`
          }}
        >
          <div className="relative inline-block flex-grow md:flex-grow-0">
            <select 
              className="block appearance-none w-full bg-white/90 border-0 px-4 py-2 pr-8 rounded-md shadow-sm leading-tight focus:outline-none focus:ring-2 focus:ring-offset-0"
              style={{ borderColor: COLORS.tertiary.light }}
              value={timeFilter}
              onChange={(e) => {
                setTimeFilter(e.target.value);
                updateDateRange(e.target.value);
              }}
              disabled={isLoading}
            >
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
              <option value="year">This year</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
              </svg>
            </div>
          </div>
          
          <Button 
            size="sm" 
            variant="outline" 
            className="h-10 bg-white/90 border-0 shadow-sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-1 h-4 w-4" />
            )}
            Refresh
          </Button>
          
          <Button 
            size="sm" 
            className="h-10 text-white border-0"
            style={{ 
              background: COLORS.secondary.gradient,
              boxShadow: '0 2px 10px rgba(233, 168, 0, 0.2)'
            }}
            onClick={handleExportData}
            disabled={isLoading || isExporting || !data}
          >
            {isExporting ? (
              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-1 h-4 w-4" />
            )}
            Export Data
          </Button>
        </div>
        
        {/* Dashboard content */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center p-12 space-y-4 text-center">
            <Loader2 className="h-12 w-12 text-primary-main animate-spin" />
            <p className="text-lg font-medium text-neutral-600">Loading your analytics data...</p>
            <p className="text-sm text-neutral-500">This may take a moment</p>
          </div>
        ) : !data ? (
          <div className="text-center py-12 backdrop-blur-sm rounded-xl shadow-sm"
               style={{ 
                 background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
                 border: `1px solid rgba(255, 255, 255, 0.2)`
               }}>
            <BarChart4 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-semibold" style={{ color: COLORS.neutral[800] }}>No Analytics Data</h3>
            <p className="mt-2 text-sm" style={{ color: COLORS.neutral[600] }}>Start sharing your coupons to see performance metrics.</p>
            <Button 
              className="mt-4 text-white border-0"
              style={{ 
                background: COLORS.primary.gradient,
                boxShadow: '0 2px 10px rgba(14, 167, 107, 0.2)'
              }}
              onClick={() => navigate('/coupon/create')}
            >
              <Plus className="mr-1 h-4 w-4" />
              Create First Coupon
            </Button>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Analytics Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {summaryStats.map((stat, index) => (
                <div 
                  key={index}
                  className="backdrop-blur-sm rounded-xl p-5 shadow-md relative overflow-hidden"
                  style={{ 
                    background: stat.color === 'primary' 
                      ? `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`
                      : stat.color === 'tertiary'
                        ? `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`
                        : `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                    border: `1px solid rgba(255, 255, 255, 0.2)`
                  }}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <p className="text-sm font-medium" style={{ color: COLORS.neutral[600] }}>{stat.label}</p>
                      <h3 className="text-2xl font-bold" style={{ color: COLORS.neutral[800] }}>{stat.value}</h3>
                    </div>
                    <div className="h-10 w-10 rounded-full flex items-center justify-center" 
                      style={{ 
                        background: stat.color === 'primary' 
                          ? COLORS.primary.bgLight 
                          : stat.color === 'tertiary'
                            ? COLORS.tertiary.bgLight
                            : COLORS.secondary.bgLight
                      }}
                    >
                      {stat.icon}
                    </div>
                  </div>
                  
                  {stat.percentChange !== undefined && (
                    <div className="flex items-center mt-1">
                      {stat.percentChange > 0 ? (
                        <TrendingUp className="h-4 w-4 mr-1 text-primary-main" />
                      ) : (
                        <TrendingDown className="h-4 w-4 mr-1 text-accent-main" />
                      )}
                      <span className={stat.percentChange > 0 ? "text-primary-main" : "text-accent-main"}>
                        {stat.percentChange > 0 ? '+' : ''}{stat.percentChange}%
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Performance Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 backdrop-blur-sm rounded-xl p-6 shadow-md" 
                style={{ 
                  background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`,
                  border: `1px solid rgba(255, 255, 255, 0.2)`
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-base font-medium" style={{ color: COLORS.neutral[800] }}>Performance Trends</h3>
                  <Badge className="bg-white/80 text-primary-dark">{timeFilter === '7days' ? 'Last 7 days' : timeFilter === '30days' ? 'Last 30 days' : timeFilter === '90days' ? 'Last 90 days' : 'This year'}</Badge>
                </div>
                
                <PerformanceChart
                  data={getChartData()}
                  height={300}
                  title="Performance Trends"
                  description="Coupon engagement over time"
                />
              </div>

              {/* Traffic Sources */}
              <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
                style={{ 
                  background: `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`,
                  border: `1px solid rgba(255, 255, 255, 0.2)`
                }}
              >
                <h3 className="text-base font-medium mb-4" style={{ color: COLORS.neutral[800] }}>Traffic Sources</h3>
                <div className="space-y-4">
                  {Object.entries(data?.trafficSources || {}).map(([source, percentage], index) => {
                    const colors = [
                      { bg: COLORS.primary.main, dot: COLORS.primary.light },
                      { bg: COLORS.accent.main, dot: COLORS.accent.light },
                      { bg: COLORS.tertiary.main, dot: COLORS.tertiary.light },
                      { bg: COLORS.secondary.main, dot: COLORS.secondary.light }
                    ];
                    const color = colors[index % colors.length];
                    
                    return (
                      <div key={source} className="relative pt-1">
                        <div className="flex mb-2 items-center justify-between">
                          <div className="flex items-center">
                            <div 
                              className="h-2 w-2 rounded-full mr-2" 
                              style={{ backgroundColor: color.dot }}
                            ></div>
                            <span className="text-sm font-medium capitalize" style={{ color: COLORS.neutral[700] }}>
                              {source}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm font-semibold" style={{ color: COLORS.neutral[800] }}>{percentage}%</span>
                          </div>
                        </div>
                        <div className="overflow-hidden h-2 text-xs flex rounded bg-white/60">
                          <div 
                            style={{ 
                              width: `${percentage}%`,
                              backgroundColor: color.bg
                            }}
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center"
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Coupon Performance */}
            <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}
            >
              <h3 className="text-base font-medium mb-4" style={{ color: COLORS.neutral[800] }}>Coupon Performance</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Use a more defensive approach with type assertion to avoid TypeScript errors */}
                {data && Array.isArray((data as any).topCoupons || []) ? (
                  ((data as any).topCoupons || []).map((coupon: any) => (
                    <div 
                      key={coupon.id} 
                      className="p-4 bg-white/90 border-0 rounded-lg shadow-sm"
                    >
                      <div className="flex justify-between mb-2">
                        <div>
                          <h4 className="font-medium" style={{ color: COLORS.neutral[800] }}>{coupon.title || 'Unnamed Coupon'}</h4>
                          <p className="text-sm" style={{ color: COLORS.neutral[500] }}>{coupon.brand?.name || 'Unknown Brand'}</p>
                        </div>
                        <div 
                          className="px-2 py-1 rounded text-sm font-mono"
                          style={{ 
                            backgroundColor: COLORS.secondary.bgLight,
                            color: COLORS.secondary.dark
                          }}
                        >
                          {coupon.code || 'NO-CODE'}
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-2 mt-4">
                        <div className="text-center">
                          <p className="text-sm" style={{ color: COLORS.neutral[500] }}>Views</p>
                          <p className="font-semibold" style={{ color: COLORS.neutral[800] }}>{coupon.view_count || 0}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm" style={{ color: COLORS.neutral[500] }}>Copies</p>
                          <p className="font-semibold" style={{ color: COLORS.neutral[800] }}>{coupon.copy_count || 0}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm" style={{ color: COLORS.neutral[500] }}>Conv. Rate</p>
                          <p className="font-semibold" style={{ color: COLORS.neutral[800] }}>
                            {((coupon.copy_count || 0) / (coupon.view_count || 1) * 100).toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 text-center py-6 bg-white/80 rounded-lg shadow-sm" style={{ color: COLORS.neutral[500] }}>
                    No coupon performance data available
                  </div>
                )}
              </div>
            </div>

            {/* Growth Opportunities */}
            <div className="backdrop-blur-sm rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}
            >
              <h3 className="text-base font-medium mb-4" style={{ color: COLORS.neutral[800] }}>Growth Opportunities</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {growthOpportunities.map((opportunity, index) => (
                  <div 
                    key={index} 
                    className="p-4 rounded-lg bg-white/90 shadow-sm"
                  >
                    <div className={`w-12 h-12 ${opportunity.color} rounded-full flex items-center justify-center mb-3`}>
                      {opportunity.icon}
                    </div>
                    <h4 className="text-base font-medium mb-2" style={{ color: COLORS.neutral[800] }}>{opportunity.title}</h4>
                    <p className={`text-sm ${opportunity.secondaryColor}`}>{opportunity.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default AnalyticsDashboard;