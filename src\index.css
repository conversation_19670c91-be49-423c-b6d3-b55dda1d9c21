@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import './styles/forced-colors.css';
@import './styles/deprecation-fixes.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent browser extension SVG errors */
svg[viewBox*="%"] {
  display: none !important;
}

/* Hide Grammarly and other extension elements that might cause errors */
grammarly-extension,
grammarly-popups,
[data-grammarly-part],
[data-grammarly-shadow-root] {
  display: none !important;
  visibility: hidden !important;
}

/* Global CSS Reset + Base Styles */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;
}

/* Box model fixes */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Viewport sizing */
html, body, #root {
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Baseline font size */
html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
}

/* Font rendering */
body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  max-width: 100%;
  min-width: 320px;
  text-rendering: optimizeLegibility;
}

/* Image handling */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Form elements */
button, input, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
} 

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Updated professional colors */
    --primary: 157 85% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 43 89% 45%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 332 85% 40%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 74% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 157 85% 35%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 157 85% 35%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 43 89% 45%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 332 85% 40%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 157 85% 35%;
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-foreground font-[Poppins];
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }
}

/* Brand Color Utilities */
@layer utilities {
  .bg-brand-primary {
    @apply bg-[#0ea76b];
  }

  .bg-brand-secondary {
    @apply bg-[#e9a800];
  }

  .bg-brand-accent {
    @apply bg-[#c30f69];
  }

  .bg-brand-blue {
    @apply bg-[#0095f6];
  }

  /* Button Styles */
  .btn-primary {
    @apply bg-[#0ea76b] text-white hover:bg-[#0a8554] transition-colors rounded-full;
  }

  .btn-secondary {
    @apply bg-[#e9a800] text-white hover:bg-[#c89000] transition-colors rounded-full;
  }

  .btn-accent {
    @apply bg-[#c30f69] text-white hover:bg-[#a00954] transition-colors rounded-full;
  }

  .btn-blue {
    @apply bg-[#0095f6] text-white hover:bg-[#0077c8] transition-colors rounded-full;
  }

  /* Card Styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow;
  }

  /* Mobile Navigation */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex justify-around items-center py-2 z-50;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center p-2 text-gray-500;
  }

  .mobile-nav-item.active {
    @apply text-[#0ea76b];
  }

  /* Search Bar */
  .search-container {
    @apply relative flex items-center w-full bg-gray-100 rounded-full px-4 py-2;
  }

  .search-input {
    @apply w-full bg-transparent border-none outline-none pl-8 text-gray-700 placeholder-gray-400;
  }

  .search-icon {
    @apply absolute left-4 text-gray-400;
  }

  /* Feature Button */
  .feature-button {
    @apply w-full py-3 rounded-full text-lg font-medium text-white text-center my-2 transition-colors;
  }

  /* Category Icon */
  .category-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2;
  }

  /* Section Title */
  .section-title {
    @apply text-xl font-bold mb-4;
  }

  /* See All Link */
  .see-all-link {
    @apply flex items-center text-[#0095f6] font-medium;
  }
  
  /* Hero Section Styles */
  .hero-section {
    @apply w-full relative overflow-hidden;
  }
  
  .hero-section.dark {
    @apply w-full relative overflow-hidden;
  }
  
  .hero-section::before {
    content: none;
  }
  
  .hero-section::after {
    content: none;
  }
  
  .hero-title {
    @apply text-3xl sm:text-4xl md:text-4xl lg:text-5xl xl:text-6xl font-extrabold mb-3 md:mb-3 leading-tight;
  }
  
  .hero-subtitle {
    @apply text-base md:text-xl mb-6 md:mb-8 max-w-lg text-gray-600;
  }
  
  .hero-button-primary {
    @apply px-4 sm:px-6 py-2.5 sm:py-3 rounded-full text-white text-sm md:text-base font-medium bg-gradient-to-r from-[#0ea76b] to-[#0a8554] hover:scale-105 transition-transform;
  }
  
  .hero-button-secondary {
    @apply px-4 sm:px-6 py-2.5 sm:py-3 rounded-full text-sm md:text-base font-medium border-2 border-[#0ea76b] text-[#0ea76b] hover:scale-105 transition-transform;
  }
  
  .platform-button {
    @apply flex items-center gap-1.5 py-1.5 px-3 sm:gap-2 sm:py-2 sm:px-4 rounded-xl cursor-pointer hover:scale-105 transition-transform;
  }
  
  .deal-card {
    @apply absolute rounded-xl overflow-hidden flex flex-col bg-white/95 backdrop-blur-md shadow-lg border border-white/20;
  }
  
  .deal-card-header {
    @apply px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200;
  }
  
  .deal-card-body {
    @apply px-4 sm:px-6 py-3 sm:py-4 flex-grow flex flex-col;
  }
  
  .deal-card-footer {
    @apply py-2.5 sm:py-3 px-3 sm:px-6 text-center font-mono font-semibold text-sm sm:text-base bg-gray-100 border-t border-gray-200;
  }
  
  .deal-copy-button {
    @apply flex items-center justify-center py-2.5 rounded-lg text-xs sm:text-sm font-medium text-white bg-[#0ea76b] hover:bg-[#0a8554] transition-colors;
  }
  
  .deal-visit-button {
    @apply flex items-center justify-center py-2.5 rounded-lg text-xs sm:text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors;
  }
  
  /* Featured Brands Section */
  .featured-brands-section {
    @apply w-full py-12 relative;
  }
  
  .featured-brands-section.dark {
    @apply w-full py-12 relative;
  }
  
  .featured-brands-section::before {
    content: none;
  }
  
  .brand-card {
    @apply bg-white dark:bg-gray-800 shadow-md rounded-xl p-4 flex flex-col items-center justify-center hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700;
  }
  
  .brand-logo-container {
    @apply h-16 w-full flex items-center justify-center mb-3;
  }
  
  .brand-logo {
    @apply h-12 object-contain;
  }
  
  .brand-name {
    @apply text-sm font-medium text-gray-900 dark:text-white text-center group-hover:text-[#c30f69] dark:group-hover:text-[#e71f80];
  }
  
  .brand-coupon-count {
    @apply text-xs text-[#c30f69] dark:text-[#e71f80] mt-1;
  }
}

/* Animation Utilities */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Animations for Taskbar */
@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-5px) rotate(5deg);
  }
}

@keyframes iconGlow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.7));
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg) scale(1);
    filter: drop-shadow(0 0 0px rgba(59, 130, 246, 0.3));
  }
  25% {
    transform: rotate(90deg) scale(1.4);
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.9));
  }
  50% {
    transform: rotate(180deg) scale(1);
    filter: drop-shadow(0 0 0px rgba(59, 130, 246, 0.3));
  }
  75% {
    transform: rotate(270deg) scale(1.4);
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.9));
  }
  100% {
    transform: rotate(360deg) scale(1);
    filter: drop-shadow(0 0 0px rgba(59, 130, 246, 0.3));
  }
}

@keyframes createButtonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.8);
    background-color: rgba(37, 99, 235, 0.2);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(37, 99, 235, 0);
    background-color: rgba(37, 99, 235, 0.4);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.8);
    background-color: rgba(37, 99, 235, 0.2);
  }
}

.animate-ticket-bounce {
  animation: iconBounce 1.5s ease-in-out infinite;
}

.animate-ticket-glow {
  animation: iconGlow 1.5s ease-in-out infinite;
}

.animate-create-pulse {
  animation: createButtonPulse 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.animate-icon-spin {
  animation: iconSpin 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) translateX(-50%);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

.animate-highlight-pulse {
  animation: highlight-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) forwards;
}
