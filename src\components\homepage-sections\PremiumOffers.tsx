import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { FaCrown } from 'react-icons/fa';
import CouponCard from '@/components/CouponCard';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import { usePremiumCoupons } from '@/hooks/useCoupons';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface PremiumOffersProps {
  limit?: number;
}

const PremiumOffers: React.FC<PremiumOffersProps> = ({ limit }) => {
  const { data: premiumCoupons, isLoading, error } = usePremiumCoupons(3);
  const isMobile = useIsMobile();
  const displayLimit = limit || (isMobile ? 4 : 3);
  const { user } = useAuth();
  const [purchasedCoupons, setPurchasedCoupons] = useState<Record<string, boolean>>({});
  
  // Fetch purchased premium coupons when component mounts
  useEffect(() => {
    const fetchPurchasedCoupons = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('premium_purchases')
          .select('coupon_id')
          .eq('buyer_id', user.id);
        
        if (error) throw error;
        
        const purchasedMap: Record<string, boolean> = {};
        data.forEach(item => {
          purchasedMap[item.coupon_id] = true;
        });
        
        setPurchasedCoupons(purchasedMap);
      } catch (error) {
        console.error('Error fetching purchased coupons:', error);
      }
    };
    
    fetchPurchasedCoupons();
  }, [user]);
  
  // Process coupons for display
  const prepareForDisplay = (coupon: any) => {
    const isLocked = !user || !purchasedCoupons[coupon.id];
    
    return {
      id: coupon.id,
      brandName: coupon.brand?.name || "Unknown Brand",
      brandLogo: coupon.brand?.logo_url || "https://via.placeholder.com/50",
      influencerName: coupon.influencer?.full_name || "Anonymous",
      influencerImage: coupon.influencer?.avatar_url,
      discountAmount: coupon.discount_description || `${coupon.discount_percent || 0}% OFF`,
      expirationTime: coupon.expires_at,
      couponCode: coupon.code,
      category: coupon.category?.name || "General",
      featured: coupon.featured,
      isPremium: true,
      isLocked: isLocked,
      price: coupon.price || 0,
      brandId: coupon.brand?.id,
    };
  };
  
  // Generate fallback premium coupons if none are found
  const getFallbackCoupons = () => {
    return [
      {
        id: 'premium-1',
        brand: { name: 'Apple', logo_url: 'https://logo.clearbit.com/apple.com', id: 'apple' },
        influencer: { full_name: 'Tech Insider', avatar_url: '' },
        discount_description: '15% OFF',
        expires_at: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        code: 'PREMIUM15',
        category: { name: 'Electronics' },
        average_rating: 4.9,
        featured: true,
        is_premium: true,
        price: 2.99
      },
      {
        id: 'premium-2',
        brand: { name: 'Adidas', logo_url: 'https://logo.clearbit.com/adidas.com', id: 'adidas' },
        influencer: { full_name: 'Sport Deals', avatar_url: '' },
        discount_description: '25% OFF',
        expires_at: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
        code: 'PREMIUM25',
        category: { name: 'Fashion' },
        average_rating: 4.7,
        featured: false,
        is_premium: true,
        price: 1.99
      },
      {
        id: 'premium-3',
        brand: { name: 'Expedia', logo_url: 'https://logo.clearbit.com/expedia.com', id: 'expedia' },
        influencer: { full_name: 'Travel Deals', avatar_url: '' },
        discount_description: '$50 OFF',
        expires_at: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
        code: 'PREMIUM50',
        category: { name: 'Travel' },
        average_rating: 4.8,
        featured: true,
        is_premium: true,
        price: 4.99
      },
      {
        id: 'premium-4',
        brand: { name: 'Best Buy', logo_url: 'https://logo.clearbit.com/bestbuy.com', id: 'bestbuy' },
        influencer: { full_name: 'Tech Deals', avatar_url: '' },
        discount_description: '20% OFF',
        expires_at: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        code: 'PREMIUM20',
        category: { name: 'Electronics' },
        average_rating: 4.6,
        featured: true,
        is_premium: true,
        price: 3.99
      }
    ];
  };
  
  // Determine which coupons to display
  let displayCoupons = (premiumCoupons && premiumCoupons.length > 0) 
    ? premiumCoupons 
    : getFallbackCoupons();
  
  // Limit the number of coupons displayed
  displayCoupons = displayCoupons.slice(0, displayLimit);

  return (
    <section className="w-full py-20 relative">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-10">
          <motion.h2 
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-2xl font-bold text-gray-900 dark:text-white flex items-center"
          >
            <span className="bg-purple-100 dark:bg-purple-900/70 text-purple-600 dark:text-purple-300 p-2 rounded-lg mr-3">
              <FaCrown className="h-6 w-6" />
            </span>
            Premium Offers
          </motion.h2>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link to="/coupons/premium" className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 flex items-center text-sm font-medium group">
              View all <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </div>
        
        {/* Premium offers header text */}
        <div className="text-center mb-12">
          <motion.p 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300"
          >
            Exclusive discounts with higher savings, curated for our premium members.
          </motion.p>
        </div>
        
        {isLoading ? (
          <div className={`grid grid-cols-1 ${isMobile ? 'sm:grid-cols-2' : 'md:grid-cols-3'} gap-8`}>
            <CouponsSkeleton count={displayLimit} />
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-10 text-center">
            <p className="text-red-600 dark:text-red-400">There was an error loading premium offers. Please try again later.</p>
          </div>
        ) : (
          <div className={`grid grid-cols-1 ${isMobile ? 'sm:grid-cols-2' : 'md:grid-cols-3'} gap-8`}>
            {displayCoupons.map((coupon, index) => {
              const displayCoupon = prepareForDisplay(coupon);
              return (
                <motion.div
                  key={coupon.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="transform hover:-translate-y-2 transition-all duration-300"
                >
                  {/* Premium glow effect wrapper */}
                  <div className="relative group">
                    {/* Subtle border glow effect that doesn't interfere with content */}
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl opacity-20 group-hover:opacity-40 transition duration-300"></div>
                    <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <CouponCard {...displayCoupon} />
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
        
        {/* Premium membership CTA */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl shadow-xl overflow-hidden"
        >
          <div className="px-6 py-12 sm:px-12 sm:py-16 lg:flex lg:items-center lg:justify-between">
            <div>
              <h3 className="text-2xl font-extrabold tracking-tight text-white sm:text-3xl">
                Upgrade to Premium
              </h3>
              <p className="mt-3 max-w-xl text-indigo-100">
                Get exclusive access to high-value coupons and deals from top brands.
                Premium members save an average of 30% more on their purchases.
              </p>
            </div>
            <div className="mt-8 lg:mt-0 lg:ml-8 flex flex-shrink-0">
              <Link 
                to="/premium" 
                className="bg-white text-indigo-700 hover:text-indigo-600 shadow-lg hover:shadow-xl flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md transition-all duration-200"
              >
                Learn more
              </Link>
              <Link 
                to="/signup" 
                className="ml-4 bg-indigo-800 bg-opacity-30 text-white hover:bg-opacity-40 flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md transition-all duration-200"
              >
                Get Started
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PremiumOffers; 