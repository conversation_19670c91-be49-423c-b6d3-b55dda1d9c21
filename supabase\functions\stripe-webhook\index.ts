import { serve } from 'std/http/server.ts'
import { createClient } from 'supabase'
import Stripe from 'stripe'

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') as string, {
  apiVersion: '2022-11-15',
})

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
const supabase = createClient(supabaseUrl, supabaseKey)

// Get the webhook signing secret
const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET') as string

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  
  if (!signature) {
    return new Response('Missing stripe-signature header', { status: 400 })
  }

  try {
    const body = await req.text()
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      webhookSecret
    )

    // Handle specific event types
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object
      
      // First try to get user ID from session metadata
      let userId = session.metadata?.userId
      
      // If not available in metadata, fall back to customer lookup
      if (!userId) {
        const { data: userData, error: userError } = await supabase
          .from('customers')
          .select('user_id')
          .eq('stripe_customer_id', session.customer)
          .single()
  
        if (userError) {
          console.error('Error fetching user:', userError)
          return new Response(JSON.stringify({ error: 'User not found' }), { status: 404 })
        }
        
        userId = userData.user_id
      }

      // Process the premium coupon purchase
      const { data, error } = await supabase.rpc('purchase_premium_coupon', {
        p_buyer_id: userId,
        p_coupon_id: session.metadata.couponId,
        p_stripe_payment_id: session.id,
        p_stripe_customer_id: session.customer
      })

      if (error) {
        console.error('Error processing purchase:', error)
        return new Response(JSON.stringify({ error: error.message }), { status: 400 })
      }

      return new Response(JSON.stringify({ success: true }), { status: 200 })
    }

    // Return a response to acknowledge receipt of the event
    return new Response(JSON.stringify({ received: true }), { status: 200 })
  } catch (err) {
    console.error(`Error processing webhook: ${err.message}`)
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }
}) 