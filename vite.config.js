import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Optimized config for better performance and stability
export default defineConfig({
  server: {
    port: 8080,
    host: 'localhost',
    strictPort: false,
    hmr: {
      protocol: 'ws',
      host: 'localhost',
      port: 8081,
      timeout: 60000
    },
    // Increase timeout for slow connections
    timeout: 120000,
    // Enable CORS for development
    cors: true,
    // Optimize file watching
    watch: {
      usePolling: false,
      interval: 100
    }
  },
  plugins: [
    react({
      fastRefresh: true,
      // Reduce babel overhead
      babel: {
        compact: false
      }
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Fix for date-fns and react-day-picker compatibility
      "date-fns/locale/en-US": path.resolve(__dirname, "./src/date-fns-fix.ts"),
    },
  },
  // Optimize dependency pre-bundling
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react/jsx-runtime',
      '@supabase/supabase-js',
      'lucide-react',
      '@tanstack/react-query'
    ],
    exclude: ['@vite/client', '@vite/env'],
    force: false
  },
  // Build optimizations
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['lucide-react', '@radix-ui/react-toast']
        }
      }
    }
  },
  // Improve CSS handling
  css: {
    devSourcemap: false
  }
});