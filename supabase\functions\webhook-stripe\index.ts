import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.18.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

// This is a public function that doesn't require authentication
// It needs to be configured in Stripe as a webhook endpoint
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  const stripeKey = Deno.env.get("STRIPE_SECRET_KEY") || "";
  const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET") || "";
  const supabaseUrl = "https://oewgwxxajssonxavydbx.supabase.co";
  const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

  if (!stripeKey || !webhookSecret) {
    console.error("Missing Stripe keys");
    return new Response(
      JSON.stringify({ error: "Server configuration error" }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500 
      }
    );
  }

  const stripe = new Stripe(stripeKey);
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    // Get the signature from the request headers
    const signature = req.headers.get("stripe-signature");
    if (!signature) {
      throw new Error("No signature provided");
    }

    // Get the raw request body
    const body = await req.text();
    const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);

    console.log(`Processing webhook: ${event.type}`);

    // Handle specific events
    if (event.type === "checkout.session.completed") {
      const session = event.data.object;
      
      // Extract data from the session
      const { couponId, buyerId, sellerId, platformFee, sellerAmount } = session.metadata || {};
      const amount = session.amount_total ? session.amount_total / 100 : 0; // Convert from cents to dollars
      
      if (!couponId || !buyerId || !sellerId) {
        throw new Error("Missing metadata in session");
      }

      // Record the purchase in the premium_purchases table
      const { data: purchase, error: purchaseError } = await supabase
        .from("premium_purchases")
        .insert({
          buyer_id: buyerId,
          coupon_id: couponId,
          amount: amount,
          stripe_payment_id: session.payment_intent,
          stripe_customer_id: session.customer,
          status: "completed"
        })
        .select('id')
        .single();

      if (purchaseError) {
        console.error("Error recording purchase:", purchaseError);
        throw new Error("Failed to record purchase");
      }

      // Record the transaction with platform fee and seller amount
      const { error: transactionError } = await supabase
        .from("transactions")
        .insert({
          buyer_id: buyerId,
          seller_id: sellerId,
          coupon_id: couponId,
          purchase_id: purchase.id,
          amount: amount,
          platform_fee: parseFloat(platformFee || "0"),
          seller_amount: parseFloat(sellerAmount || "0"),
          payment_id: session.payment_intent,
          payment_method: "stripe",
          status: "completed"
        });

      if (transactionError) {
        console.error("Error recording transaction:", transactionError);
        throw new Error("Failed to record transaction");
      }

      console.log(`Successfully recorded purchase and transaction for coupon ${couponId} between buyer ${buyerId} and seller ${sellerId}`);
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Webhook error:", error.message);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400
      }
    );
  }
});
