import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useNavigate, useLocation } from 'react-router-dom';

// Enhanced onboarding steps - comprehensive website overview with start menu
type OnboardingStep =
  | 'welcome'
  | 'website_tour'
  | 'start_menu_intro'
  | 'start_menu_features'
  | 'explore_brands'
  | 'explore_categories'
  | 'profile_setup'
  | 'payment_setup'
  | 'first_coupon'
  | 'completed';

// These must exactly match the values allowed in the database constraint
const VALID_DB_STEPS = [
  'welcome',
  'website_tour',
  'start_menu_intro',
  'start_menu_features',
  'explore_brands',
  'explore_categories',
  'profile_setup',
  'payment_setup',
  'first_coupon',
  'completed'
];

interface OnboardingMessage {
  title: string;
  description: string;
  nextStep?: string;
  nextPath?: string;
}

interface OnboardingContextType {
  currentStep: OnboardingStep;
  isOnboardingComplete: boolean;
  updateStep: (step: OnboardingStep) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  error: string | null;
  handleStepCompletion: (step: OnboardingStep, data?: any) => Promise<void>;
  getCurrentMessage: () => OnboardingMessage | null;
  isOnboardingInitialized: boolean;
  resetOnboarding: () => Promise<void>;
  skipStep: () => Promise<void>;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const { user, profile } = useAuth();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const [error, setError] = useState<string | null>(null);
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const [isOnboardingInitialized, setIsOnboardingInitialized] = useState(false);

  // Debug function to validate all step values
  const validateStepValues = () => {
    console.log('Validating step values:');
    console.log('Current step:', currentStep);
    console.log('Type of current step:', typeof currentStep);
    console.log('Is current step in VALID_DB_STEPS?', VALID_DB_STEPS.includes(currentStep));
    console.log('Is current step in steps array?', steps.includes(currentStep));
    
    // Check each step
    steps.forEach(step => {
      console.log(`Step "${step}" is valid for DB:`, VALID_DB_STEPS.includes(step));
    });
    
    // Check for whitespace or case issues
    const cleanCurrentStep = currentStep.trim();
    if (cleanCurrentStep !== currentStep) {
      console.warn('Current step contains whitespace!');
    }
    
    // Check for case sensitivity issues
    if (currentStep.toLowerCase() !== currentStep) {
      console.warn('Current step has uppercase characters!');
    }
  };

  // List of all possible steps
  const steps: OnboardingStep[] = [
    'welcome',
    'website_tour',
    'start_menu_intro',
    'start_menu_features',
    'explore_brands',
    'explore_categories',
    'profile_setup',
    'payment_setup',
    'first_coupon',
    'completed'
  ];

  // Helper function to get expected path for a step
  const getPathForStep = (step: OnboardingStep): string | null => {
    // Don't return a path for completed step to prevent redirection
    if (step === 'completed') {
      return null;
    }
    
    const stepPathMap: Record<string, string> = {
      'welcome': '/home',
      'website_tour': '/home',
      'start_menu_intro': '/home',
      'start_menu_features': '/home',
      'explore_brands': '/brands',
      'explore_categories': '/categories',
      'profile_setup': '/settings',
      'payment_setup': '/settings',
      'first_coupon': '/create-coupon',
    };
    
    return stepPathMap[step] || null;
  };

  // Define onboarding messages and paths for each step
  const stepMessages: Record<OnboardingStep, OnboardingMessage> = {
    welcome: {
      title: 'Welcome to CouponLink! 🎉',
      description: 'We\'re excited to have you join our community of savvy shoppers and influencers',
      nextStep: 'website_tour',
      nextPath: '/home'
    },
    website_tour: {
      title: 'Let\'s Explore CouponLink 🚀',
      description: 'Discover how to find amazing deals, create coupons, and earn money as an influencer',
      nextStep: 'start_menu_intro',
      nextPath: '/home'
    },
    start_menu_intro: {
      title: 'Meet Your Start Menu 📋',
      description: 'Your command center! The start menu will open automatically so you can explore all features and navigation options',
      nextStep: 'start_menu_features',
      nextPath: '/home'
    },
    start_menu_features: {
      title: 'Start Menu Features ⚡',
      description: 'Take a look around! You can access analytics, settings, create coupons, track your progress, and much more from here',
      nextStep: 'explore_brands',
      nextPath: '/home'
    },
    explore_brands: {
      title: 'Popular Brands & Stores 🏪',
      description: 'Browse through hundreds of popular brands and discover exclusive deals',
      nextStep: 'explore_categories',
      nextPath: '/brands'
    },
    explore_categories: {
      title: 'Shop by Categories 🛍️',
      description: 'Find deals in your favorite categories - Fashion, Electronics, Food & more',
      nextStep: 'profile_setup',
      nextPath: '/categories'
    },
    profile_setup: {
      title: 'Set Up Your Profile 👤',
      description: 'Add your details to help your audience know you better and start earning',
      nextStep: 'payment_setup',
      nextPath: '/settings'
    },
    payment_setup: {
      title: 'Set Up Payment Info 💳',
      description: 'Add your payment details to receive earnings from your coupon shares',
      nextStep: 'first_coupon',
      nextPath: '/settings'
    },
    first_coupon: {
      title: 'Create Your First Coupon 🎫',
      description: 'Start earning by creating and sharing your first coupon with your audience',
      nextStep: 'completed',
      nextPath: '/create-coupon'
    },
    completed: {
      title: 'All Set! You\'re Ready to Earn! 🎊',
      description: 'You\'re now ready to start sharing coupons and earning money. Welcome to the CouponLink family!',
    }
  };

  // Get current onboarding message
  const getCurrentMessage = () => {
    return currentStep ? stepMessages[currentStep] : null;
  };

  // Load initial onboarding state
  useEffect(() => {
    // Only load state if we have a user
    if (!user?.id) {
      setIsOnboardingInitialized(true);
      return;
    }
    
    const loadOnboardingState = async () => {
      try {
        // Get profile data to determine onboarding state
        // Add retry logic for new users whose profiles might not be immediately available
        let attempts = 0;
        let data = null;
        let error = null;

        while (attempts < 3) {
          const result = await supabase
            .from('profiles')
            .select('onboarding_step, onboarding_completed, is_new_user')
            .eq('id', user.id)
            .single();

          data = result.data;
          error = result.error;

          if (!error || error.code !== 'PGRST116') {
            // Success or non-"not found" error
            break;
          }

          // Profile not found, wait and retry
          attempts++;
          if (attempts < 3) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        if (error && error.code !== 'PGRST116') {
          throw error;
        }

        if (data) {
          // Profile data loaded

          // If onboarding is completed, set the final step
          if (data.onboarding_completed) {
            setCurrentStep('completed');
            setOnboardingCompleted(true);
            setIsOnboardingInitialized(true);
            return;
          }

          // Set current step from database or default to welcome for new users
          const initialStep = data.onboarding_step && steps.includes(data.onboarding_step as OnboardingStep)
            ? data.onboarding_step as OnboardingStep
            : 'welcome';

          setCurrentStep(initialStep);
          setIsOnboardingInitialized(true);
        } else {
          // No profile data found, assume welcome step
          setCurrentStep('welcome');
          setIsOnboardingInitialized(true);
        }
      } catch (err) {
        console.error('Error loading onboarding state:', err);
        setIsOnboardingInitialized(true);
      }
    };

    loadOnboardingState();
  }, [user?.id]);

  // Add function to reset onboarding
  const resetOnboarding = async () => {
    try {
      if (!user?.id) {
        toast.error('You must be logged in to reset onboarding');
        return;
      }

      // Ensure we're using a valid value for the database
      if (!VALID_DB_STEPS.includes('welcome')) {
        console.error('Invalid welcome step value for database');
        toast.error('Invalid step value. Please try again.');
      return;
    }

      // Update database
      const { error } = await supabase
        .from('profiles')
        .update({
          onboarding_step: 'welcome',
          onboarding_completed: false,
          last_active_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      // Update local state
      setCurrentStep('welcome');
      
      // Navigate to home
      navigate('/home');
      
      toast.success('Onboarding has been reset');
    } catch (err) {
      console.error('Failed to reset onboarding:', err);
      setError('Failed to reset onboarding');
      toast.error('Failed to reset onboarding');
    }
  };

  // Update current step
  const updateStep = async (step: OnboardingStep) => {
    try {
      if (!user?.id) {
        // During signup, the user might not be immediately available
        // Wait a short time and try again, but don't show error for new signups
        // User not available yet, waiting for authentication to complete

        // Wait up to 3 seconds for user to be available
        let attempts = 0;
        const maxAttempts = 6; // 6 attempts * 500ms = 3 seconds

        while (!user?.id && attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
        }

        // If still no user after waiting, this might be a legitimate error
        if (!user?.id) {
          console.error('No user found after waiting during onboarding step update');
          // Only show error if this isn't during initial signup
          const isSignupFlow = sessionStorage.getItem('signUpInProgress') === 'true';
          if (!isSignupFlow) {
            toast.error('You must be logged in to update onboarding progress');
          }
          return;
        }
      }

      // Ensure we're using a valid value for the database
      if (!VALID_DB_STEPS.includes(step)) {
        console.error('Invalid step value for database:', step);
        toast.error('Invalid step value. Please try again.');
        return;
      }

      // Update database
      const { error } = await supabase
          .from('profiles')
          .update({
            onboarding_step: step,
            last_active_at: new Date().toISOString()
          })
          .eq('id', user.id);
          
      if (error) throw error;

      // Update local state
      setCurrentStep(step);
      
      // Get path for next step - for modal based approach, we navigate to the page
      const nextPath = getPathForStep(step);
      if (nextPath) {
        navigate(nextPath);
      }
      
    } catch (err) {
      console.error('Failed to update onboarding step:', err);
      setError('Failed to update onboarding step');
      toast.error('Failed to update onboarding step');
    }
  };

  // Skip to next step
  const skipStep = async () => {
    try {
      // Skip current step
      
      // Ensure user is logged in
      if (!user?.id) {
        toast.error('You must be logged in to skip this step');
        return;
      }
      
      // Get current step index
      const currentIndex = steps.indexOf(currentStep);
      
      // If we're on the last step or can't find the step, complete onboarding
      if (currentIndex === -1 || currentIndex >= steps.length - 2) {
        await completeOnboarding();
        return;
      }
      
      // Get next step
      const nextStep = steps[currentIndex + 1];
      
      // Skipping to next step
      
      // Verify that the next step is valid in the database
      if (!VALID_DB_STEPS.includes(nextStep)) {
        console.error('Invalid next step for database:', nextStep);
        toast.error(`Cannot skip to step "${nextStep}" - not a valid database value. Valid steps are: ${VALID_DB_STEPS.join(', ')}`);
          return;
        }
        
      // Get path for next step 
      const nextPath = getPathForStep(nextStep);
      
      // Directly perform the update with better error handling
      const { error } = await supabase
        .from('profiles')
        .update({
          onboarding_step: nextStep,
          last_active_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        console.error('Failed to skip step:', error);
        
        // Add specific handling for constraint violations
        if (error.code === '23514') {
          toast.error(`Database constraint violation: "${nextStep}" is not a valid step value. Allowed values are: ${VALID_DB_STEPS.join(', ')}`);
          } else {
          toast.error(`Failed to skip step: ${error.message}`);
        }
        return;
      }
      
      // Update local state
      setCurrentStep(nextStep);
      
      // Navigate to next page
      if (nextPath) {
        navigate(nextPath);
      }

      // Removed toast message for smoother onboarding flow
    } catch (err) {
      console.error('Error in skipStep:', err);
      toast.error('Failed to skip step due to an unexpected error');
    }
  };

  // Complete onboarding
  const completeOnboarding = async () => {
    try {
      if (!user?.id) {
        toast.error('You must be logged in to complete onboarding');
        return;
      }
      
      // Ensure we're using a valid value for the database
      if (!VALID_DB_STEPS.includes('completed')) {
        console.error('Invalid completed step value for database');
        toast.error('Invalid step value. Please try again.');
      return;
    }

      // Update database
      const { error } = await supabase
        .from('profiles')
        .update({ 
          onboarding_step: 'completed',
          onboarding_completed: true,
          last_active_at: new Date().toISOString()
        })
        .eq('id', user.id);
        
      if (error) throw error;
      
      // Update local state
      setCurrentStep('completed');
      setOnboardingCompleted(true);

      // Removed completion message for smoother flow

      // Navigate to home page
      navigate('/home');
    } catch (err) {
      console.error('Failed to complete onboarding:', err);
      setError('Failed to complete onboarding');
      toast.error('Failed to complete onboarding');
    }
  };

  // Handle completion of specific steps
  const handleStepCompletion = async (step: OnboardingStep, data?: any) => {
    try {
      // Handle step completion
      
      // Double check that the step is a valid string and properly formatted
      if (typeof step !== 'string') {
        console.error('Step is not a string:', step);
        toast.error('Invalid step format');
        return;
      }
      
      // Strip any whitespace to ensure clean values
      const cleanStep = step.trim();
      
      // Check if user exists
      if (!user?.id) {
        console.error('No user ID found when trying to complete step');
        // Only show error if this isn't during initial signup
        const isSignupFlow = sessionStorage.getItem('signUpInProgress') === 'true';
        if (!isSignupFlow) {
          toast.error('You must be logged in to complete this step');
        }
        return;
      }
      
      // Validate the current step
      if (!steps.includes(cleanStep as OnboardingStep)) {
        console.error('Invalid step:', cleanStep);
        toast.error(`Invalid step: "${cleanStep}"`);
        return;
      }
      
      // Get current step index and next step
      const currentIndex = steps.indexOf(cleanStep as OnboardingStep);
      const nextStep = currentIndex < steps.length - 1 ? steps[currentIndex + 1] : 'completed';
      const nextPath = getPathForStep(nextStep);
      
      // Debug logging removed for cleaner onboarding experience

      // Verify that the step we're completing is valid in the database
      if (!VALID_DB_STEPS.includes(cleanStep)) {
        console.error('Invalid current step for database:', cleanStep);
        toast.error(`Invalid step value: "${cleanStep}". Valid values are: ${VALID_DB_STEPS.join(', ')}`);
      return;
    }

      // Handle step-specific logic and data
      switch (cleanStep) {
        case 'profile_setup':
          // Update profile if data is provided
          if (data && user?.id) {
            // Updating profile with data
            
            const { error: profileError } = await supabase
            .from('profiles')
            .update({ 
                ...data,
              last_active_at: new Date().toISOString()
            })
            .eq('id', user.id);
            
            if (profileError) {
              console.error('Error updating profile:', profileError);
              throw profileError;
          }
          }
          break;
          
        // Add other step-specific logic as needed
      }
      
      // Perform database update with additional error checks
      try {
        // Update to next step in database
        
        // Verify that the next step is valid in the database
        if (!VALID_DB_STEPS.includes(nextStep)) {
          console.error('Invalid next step for database:', nextStep);
          toast.error(`Invalid next step: "${nextStep}". Valid values are: ${VALID_DB_STEPS.join(', ')}`);
        return;
      } 
      
        const { error } = await supabase
          .from('profiles')
          .update({
            onboarding_step: nextStep,
            onboarding_completed: nextStep === 'completed',
            last_active_at: new Date().toISOString()
          })
          .eq('id', user.id);
            
        if (error) {
          console.error('Database error updating onboarding step:', error);
          if (error.code === '23514') { // Constraint violation error code
            toast.error(`Database constraint violation: "${error.message}". Valid steps are: ${VALID_DB_STEPS.join(', ')}`);
          } else {
            toast.error(`Database error: ${error.message}`);
          }
          throw error;
        }
        
        // Update local state
        setCurrentStep(nextStep);

        // Navigate to next path if provided
        if (nextStep === 'completed') {
          // If we just completed the final step, navigate to home
          navigate('/home');
        } else if (nextPath) {
          // For intermediate steps, navigate to the appropriate path
          navigate(nextPath);
        }
        
        // Removed completion messages for smoother onboarding flow
      
      } catch (dbError) {
        console.error('Database operation failed:', dbError);
        throw dbError;
      }
      
    } catch (err: any) {
      console.error('Failed to complete step:', err);
      // Log more detailed error information
      if (err?.message) console.error('Error message:', err.message);
      if (err?.details) console.error('Error details:', err.details);
      if (err?.hint) console.error('Error hint:', err.hint);
      if (err?.code) console.error('Error code:', err.code);
      
      setError('Failed to complete step');
      toast.error(`Failed to complete step: ${err?.message || 'Unknown error'}`);
    }
  };

  const value = {
        currentStep,
        isOnboardingComplete: currentStep === 'completed' || onboardingCompleted,
        updateStep,
        completeOnboarding,
        error,
        handleStepCompletion,
        getCurrentMessage,
    isOnboardingInitialized,
    resetOnboarding,
    skipStep
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  
  return context;
} 