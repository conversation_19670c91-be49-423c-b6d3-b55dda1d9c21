import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowRight, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { FaFire } from 'react-icons/fa';
import CouponCard from '@/components/CouponCard';
import { useTrendingCoupons } from '@/hooks/useCoupons';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import { useIsMobile } from '@/hooks/use-mobile';
import type { Coupon } from '@/hooks/useCoupons';

const TrendingDealsSection = () => {
  const { data: trendingCoupons, isLoading, error } = useTrendingCoupons();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [slideWidth, setSlideWidth] = useState(0);
  const slideRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  
  // Calculate max slides based on available coupons
  const couponCount = trendingCoupons?.length || 0;
  const itemsPerSlide = 3; // Fixed to 3 items per slide for desktop
  const maxSlides = Math.max(0, Math.ceil(couponCount / itemsPerSlide) - 1);

  // Only enable carousel if we have more than 3 coupons (more than one slide)
  const shouldShowCarousel = couponCount > 3;
  
  // Process coupons for display
  const prepareForDisplay = (coupon: Coupon) => ({
    id: coupon.id,
    brandName: coupon.brand?.name || "Unknown Brand",
    brandLogo: coupon.brand?.logo_url || "/placeholder.svg",
    brandWebsite: coupon.brand?.website,
    influencerName: coupon.influencer?.full_name || "Anonymous",
    influencerImage: coupon.influencer?.avatar_url,
    discountAmount: coupon.discount_description || `${coupon.discount_percent || 0}% OFF`,
    expirationTime: coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration",
    couponCode: coupon.code,
    category: coupon.category?.name || "General",
    featured: coupon.featured,
    isPremium: coupon.is_premium,
    brandId: coupon.brand?.id,
    price: coupon.price,
  });
  
  // Move to next slide
  const nextSlide = () => {
    if (currentSlide < maxSlides) {
      setCurrentSlide(currentSlide + 1);
    } else {
      setCurrentSlide(0);
    }
  };
  
  // Move to previous slide
  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    } else {
      setCurrentSlide(maxSlides);
    }
  };
  
  // Auto-advance slides only if we have enough coupons for carousel
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (shouldShowCarousel && maxSlides > 0 && !isMobile) {
      interval = setInterval(nextSlide, 6000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentSlide, maxSlides, isMobile, shouldShowCarousel]);
  
  // Update slide width on resize
  useEffect(() => {
    const updateSlideWidth = () => {
      if (slideRef.current) {
        setSlideWidth(slideRef.current.offsetWidth);
      }
    };
    
    updateSlideWidth();
    window.addEventListener('resize', updateSlideWidth);
    return () => window.removeEventListener('resize', updateSlideWidth);
  }, []);
  
  // Update transform on slide change
  useEffect(() => {
    if (slideRef.current && slideWidth > 0 && shouldShowCarousel) {
      slideRef.current.style.transform = `translateX(-${currentSlide * slideWidth}px)`;
    }
  }, [currentSlide, slideWidth, shouldShowCarousel]);

  // Reset slide position when coupons change or carousel is disabled
  useEffect(() => {
    if (!shouldShowCarousel || currentSlide > maxSlides) {
      setCurrentSlide(0);
    }
  }, [shouldShowCarousel, maxSlides, currentSlide]);
  
  // Determine which coupons to display
  const displayCoupons = trendingCoupons || [];
  
  // Mobile view with grid layout
  if (isMobile) {
    return (
      <section className="w-full py-20">
        <div className="relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="flex items-center justify-between mb-10">
              <motion.h2 
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="text-2xl font-bold text-gray-900 dark:text-white flex items-center"
              >
                <span className="bg-orange-100 dark:bg-orange-900/50 text-orange-600 dark:text-orange-400 p-2 rounded-lg mr-3">
                  <FaFire className="h-6 w-6" />
                </span>
                Trending Deals
              </motion.h2>
              
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Link 
                  to="/trending" 
                  className="text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 flex items-center text-sm font-medium group"
                >
                  View all <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
                </Link>
              </motion.div>
            </div>
            
            {isLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <CouponsSkeleton count={4} />
              </div>
            ) : error ? (
              <div className="w-full flex justify-center items-center bg-red-50 dark:bg-red-900/20 rounded-lg p-10">
                <p className="text-red-600 dark:text-red-400">There was an error loading trending deals. Please try again later.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 auto-rows-fr">
                {displayCoupons.slice(0, 4).map((coupon, index) => (
                  <motion.div
                    key={coupon.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="h-full"
                  >
                    <div className="h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg">
                      <CouponCard {...prepareForDisplay(coupon)} />
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </section>
    );
  }
  
  // Desktop view with carousel
  return (
    <section className="w-full py-20">
      <div className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="flex items-center justify-between mb-10">
            <motion.h2 
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-2xl font-bold text-gray-900 dark:text-white flex items-center"
            >
              <span className="bg-orange-100 dark:bg-orange-900/50 text-orange-600 dark:text-orange-400 p-2 rounded-lg mr-3">
                <FaFire className="h-6 w-6" />
              </span>
              Trending Deals
            </motion.h2>
            
            <div className="flex items-center space-x-4">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Link 
                  to="/trending" 
                  className="text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 flex items-center text-sm font-medium group"
                >
                  View all <FiArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
                </Link>
              </motion.div>
              
              {shouldShowCarousel && maxSlides > 0 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={prevSlide}
                    className="p-2 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Previous slide"
                  >
                    <FiChevronLeft className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </button>
                  <button
                    onClick={nextSlide}
                    className="p-2 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Next slide"
                  >
                    <FiChevronRight className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </button>
                </div>
              )}
            </div>
          </div>
          
          <div className="overflow-hidden">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <CouponsSkeleton count={6} />
              </div>
            ) : error ? (
              <div className="w-full flex justify-center items-center bg-red-50 dark:bg-red-900/20 rounded-lg p-10">
                <p className="text-red-600 dark:text-red-400">There was an error loading trending deals. Please try again later.</p>
              </div>
            ) : shouldShowCarousel ? (
              <div className="relative w-full overflow-hidden">
                <div
                  ref={slideRef}
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{ width: `${100 * Math.ceil(displayCoupons.length / 3)}%` }}
                >
                  {Array.from({ length: Math.ceil(displayCoupons.length / 3) }).map((_, slideIndex) => (
                    <div
                      key={slideIndex}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr"
                      style={{ width: `${100 / Math.ceil(displayCoupons.length / 3)}%` }}
                    >
                      {displayCoupons
                        .slice(slideIndex * 3, (slideIndex * 3) + 3)
                        .map((coupon, index) => (
                          <motion.div
                            key={coupon.id}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                            className="h-full"
                          >
                            <div className="h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg">
                              <CouponCard {...prepareForDisplay(coupon)} />
                            </div>
                          </motion.div>
                        ))}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              // Static grid when we have 3 or fewer coupons
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr">
                {displayCoupons.map((coupon, index) => (
                  <motion.div
                    key={coupon.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="h-full"
                  >
                    <div className="h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg">
                      <CouponCard {...prepareForDisplay(coupon)} />
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrendingDealsSection; 