import { Coupon } from '@/types/coupon';

/**
 * Enhanced search utility for coupons with scoring and ranking
 */
export const searchCoupons = (coupons: Coupon[], searchQuery: string): Coupon[] => {
  if (!searchQuery.trim()) return coupons;

  const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
  
  const searchResults = coupons.map(item => {
    let score = 0;
    let matchedFields: string[] = [];

    // Define searchable fields with their weights (higher = more important)
    // Only using fields that actually exist in the database
    const searchableFields = [
      { field: item.title?.toLowerCase() || '', weight: 10, name: 'title' },
      { field: item.brand?.name?.toLowerCase() || '', weight: 8, name: 'brand' },
      { field: item.code?.toLowerCase() || '', weight: 7, name: 'code' },
      { field: item.discount_description?.toLowerCase() || '', weight: 6, name: 'discount' },
      { field: item.category?.name?.toLowerCase() || '', weight: 5, name: 'category' },
      { field: item.influencer?.full_name?.toLowerCase() || '', weight: 4, name: 'influencer' },
      { field: item.influencer?.username?.toLowerCase() || '', weight: 4, name: 'username' },
    ];

    // Calculate score for each search term
    searchTerms.forEach(term => {
      searchableFields.forEach(({ field, weight, name }) => {
        if (field.includes(term)) {
          // Exact match gets full weight
          if (field === term) {
            score += weight * 3;
          }
          // Word boundary match gets double weight
          else if (field.split(' ').some((word: string) => word === term)) {
            score += weight * 2;
          }
          // Substring match gets normal weight
          else {
            score += weight;
          }
          
          if (!matchedFields.includes(name)) {
            matchedFields.push(name);
          }
        }
      });
    });

    return { ...item, searchScore: score, matchedFields };
  });

  // Filter out items with no matches and sort by score
  return searchResults
    .filter(item => item.searchScore > 0)
    .sort((a, b) => {
      // Primary sort: by search score (descending)
      if (b.searchScore !== a.searchScore) {
        return b.searchScore - a.searchScore;
      }
      // Secondary sort: by creation date (newest first)
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
};

/**
 * Simple search function for basic filtering (backwards compatibility)
 */
export const simpleSearchCoupons = (coupons: Coupon[], searchQuery: string): Coupon[] => {
  if (!searchQuery.trim()) return coupons;

  const searchTerm = searchQuery.toLowerCase();
  
  return coupons.filter(coupon => {
    const brandName = coupon.brand?.name?.toLowerCase() || '';
    const influencerName = coupon.influencer?.full_name?.toLowerCase() || '';
    const influencerUsername = coupon.influencer?.username?.toLowerCase() || '';
    const categoryName = coupon.category?.name?.toLowerCase() || '';
    const discountDescription = coupon.discount_description?.toLowerCase() || '';
    const code = coupon.code?.toLowerCase() || '';
    const title = coupon.title?.toLowerCase() || '';

    return brandName.includes(searchTerm) ||
           influencerName.includes(searchTerm) ||
           influencerUsername.includes(searchTerm) ||
           categoryName.includes(searchTerm) ||
           discountDescription.includes(searchTerm) ||
           code.includes(searchTerm) ||
           title.includes(searchTerm);
  });
};

/**
 * Get search suggestions based on popular terms
 */
export const getSearchSuggestions = (coupons: Coupon[], query: string): string[] => {
  if (!query.trim() || query.length < 2) return [];

  const suggestions = new Set<string>();
  const queryLower = query.toLowerCase();

  coupons.forEach(coupon => {
    // Add brand names that start with or contain the query
    if (coupon.brand?.name) {
      const brandName = coupon.brand.name;
      if (brandName.toLowerCase().includes(queryLower)) {
        suggestions.add(brandName);
      }
    }

    // Add category names
    if (coupon.category?.name) {
      const categoryName = coupon.category.name;
      if (categoryName.toLowerCase().includes(queryLower)) {
        suggestions.add(categoryName);
      }
    }

    // Add influencer names
    if (coupon.influencer?.full_name) {
      const influencerName = coupon.influencer.full_name;
      if (influencerName.toLowerCase().includes(queryLower)) {
        suggestions.add(influencerName);
      }
    }

    // Add coupon codes that match
    if (coupon.code?.toLowerCase().includes(queryLower)) {
      suggestions.add(coupon.code);
    }

    // Add discount descriptions (first few words)
    if (coupon.discount_description?.toLowerCase().includes(queryLower)) {
      const words = coupon.discount_description.split(' ').slice(0, 3).join(' ');
      if (words.length > query.length) {
        suggestions.add(words);
      }
    }

    // Add titles that match
    if (coupon.title?.toLowerCase().includes(queryLower)) {
      suggestions.add(coupon.title);
    }
  });

  // Sort suggestions by relevance (exact matches first, then partial matches)
  const sortedSuggestions = Array.from(suggestions).sort((a, b) => {
    const aLower = a.toLowerCase();
    const bLower = b.toLowerCase();

    // Exact matches first
    if (aLower === queryLower && bLower !== queryLower) return -1;
    if (bLower === queryLower && aLower !== queryLower) return 1;

    // Starts with query
    if (aLower.startsWith(queryLower) && !bLower.startsWith(queryLower)) return -1;
    if (bLower.startsWith(queryLower) && !aLower.startsWith(queryLower)) return 1;

    // Alphabetical order
    return a.localeCompare(b);
  });

  return sortedSuggestions.slice(0, 5); // Limit to 5 suggestions
};

/**
 * Highlight search terms in text
 */
export const highlightSearchTerms = (text: string, searchQuery: string): string => {
  if (!searchQuery.trim()) return text;

  const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
  let highlightedText = text;

  searchTerms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
  });

  return highlightedText;
};

/**
 * Get search analytics for debugging and optimization
 */
export const getSearchAnalytics = (coupons: Coupon[], searchQuery: string) => {
  if (!searchQuery.trim()) return null;

  const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
  const results = searchCoupons(coupons, searchQuery);

  const analytics = {
    query: searchQuery,
    searchTerms,
    totalCoupons: coupons.length,
    resultsCount: results.length,
    matchRate: (results.length / coupons.length) * 100,
    fieldMatches: {
      title: 0,
      brand: 0,
      code: 0,
      discount: 0,
      category: 0,
      influencer: 0,
      description: 0,
      terms: 0
    },
    topResults: results.slice(0, 5).map(coupon => ({
      id: coupon.id,
      title: coupon.title,
      brand: coupon.brand?.name,
      score: (coupon as any).searchScore || 0,
      matchedFields: (coupon as any).matchedFields || []
    }))
  };

  // Count field matches
  results.forEach(coupon => {
    const matchedFields = (coupon as any).matchedFields || [];
    matchedFields.forEach((field: string) => {
      if (field in analytics.fieldMatches) {
        (analytics.fieldMatches as any)[field]++;
      }
    });
  });

  return analytics;
};

/**
 * Popular search terms (could be enhanced with actual usage data)
 */
export const getPopularSearchTerms = (coupons: Coupon[]): string[] => {
  const termFrequency = new Map<string, number>();

  coupons.forEach(coupon => {
    // Count brand names
    if (coupon.brand?.name) {
      const brand = coupon.brand.name.toLowerCase();
      termFrequency.set(brand, (termFrequency.get(brand) || 0) + 1);
    }

    // Count categories
    if (coupon.category?.name) {
      const category = coupon.category.name.toLowerCase();
      termFrequency.set(category, (termFrequency.get(category) || 0) + 1);
    }

    // Count common discount terms
    if (coupon.discount_description) {
      const words = coupon.discount_description.toLowerCase().split(' ');
      words.forEach(word => {
        if (word.length > 3 && !['with', 'your', 'this', 'that', 'from'].includes(word)) {
          termFrequency.set(word, (termFrequency.get(word) || 0) + 1);
        }
      });
    }
  });

  // Sort by frequency and return top terms
  return Array.from(termFrequency.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([term]) => term);
};
