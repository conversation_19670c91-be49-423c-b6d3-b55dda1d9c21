#!/usr/bin/env node
// This script creates the missing tables and RLS policies in your Supabase database
// Run it with: node scripts/run-missing-tables-setup.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get Supabase credentials 
const SUPABASE_URL = process.env.SUPABASE_URL || "https://oewgwxxajssonxavydbx.supabase.co";
const SUPABASE_KEY = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!SUPABASE_KEY) {
  console.error('No Supabase key provided. Please set SUPABASE_SERVICE_KEY or SUPABASE_ANON_KEY environment variable.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Create the profile_shares table SQL
const createProfileSharesTableSQL = `
-- Create the profile_shares table
CREATE TABLE IF NOT EXISTS public.profile_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  sharer_ip TEXT,
  user_agent TEXT,
  platform TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_shares_user_id ON public.profile_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_profile_id ON public.profile_shares(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_created_at ON public.profile_shares(created_at);
CREATE INDEX IF NOT EXISTS idx_profile_shares_platform ON public.profile_shares(platform);

-- Enable Row Level Security
ALTER TABLE public.profile_shares ENABLE ROW LEVEL SECURITY;

-- Allow anonymous inserts to profile_shares (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to profile_shares"
ON public.profile_shares
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to profile_shares"
ON public.profile_shares
USING (true)
WITH CHECK (true);

-- Allow users to see their own shares and shares of their profile
CREATE POLICY "Allow users to see relevant profile shares"
ON public.profile_shares
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  (profile_id = auth.uid())
);
`;

// Create the profile_views table SQL
const createProfileViewsTableSQL = `
-- Create the profile_views table
CREATE TABLE IF NOT EXISTS public.profile_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address TEXT,
  user_agent TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_views_viewer_id ON public.profile_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_profile_id ON public.profile_views(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_created_at ON public.profile_views(created_at);

-- Enable Row Level Security
ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;

-- Allow anonymous inserts to profile_views (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to profile_views"
ON public.profile_views
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to profile_views"
ON public.profile_views
USING (true)
WITH CHECK (true);

-- Allow users to see views of their profile
CREATE POLICY "Allow users to see views of their profile"
ON public.profile_views
FOR SELECT
TO authenticated
USING (
  (viewer_id = auth.uid()) OR
  (profile_id = auth.uid())
);
`;

// Create the coupon_interactions table SQL
const createCouponInteractionsTableSQL = `
-- Create the coupon_interactions table
CREATE TABLE IF NOT EXISTS public.coupon_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coupon_id UUID NOT NULL REFERENCES public.coupons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  interaction_type TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  occurred_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_user_id ON public.coupon_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_coupon_id ON public.coupon_interactions(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_type ON public.coupon_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_occurred_at ON public.coupon_interactions(occurred_at);

-- Enable Row Level Security
ALTER TABLE public.coupon_interactions ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into coupon_interactions (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to coupon_interactions"
ON public.coupon_interactions
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to coupon_interactions"
ON public.coupon_interactions
USING (true)
WITH CHECK (true);

-- Allow users to see their own interactions and interactions with their coupons
CREATE POLICY "Allow users to see relevant coupon interactions"
ON public.coupon_interactions
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  EXISTS (
    SELECT 1 FROM coupons
    WHERE coupons.id = coupon_interactions.coupon_id
    AND coupons.influencer_id = auth.uid()
  )
);
`;

async function checkAndCreateTable(tableName, sqlCommands) {
  console.log(`\nChecking if ${tableName} table exists...`);
  
  try {
    // Check if table exists
    const { data, error } = await supabase.from(tableName).select('id').limit(1);
    
    if (error && error.code === '42P01') { // Table doesn't exist error
      console.log(`👉 ${tableName} table doesn't exist. Creating it...`);
      
      // Execute the SQL directly
      const { error: sqlError } = await supabase.rpc('run_sql', { sql_query: sqlCommands });
      
      if (sqlError) {
        if (sqlError.message.includes('function "run_sql" does not exist')) {
          console.error('The run_sql function doesn\'t exist in your Supabase instance.');
          console.log('\n⚠️ Please run this SQL in your Supabase dashboard SQL Editor:');
          console.log(sqlCommands);
          return false;
        } else {
          console.error(`❌ Error creating ${tableName} table:`, sqlError.message);
          return false;
        }
      }
      
      console.log(`✅ ${tableName} table created successfully!`);
      return true;
    } else if (error) {
      console.error(`❌ Error checking ${tableName} table:`, error.message);
      return false;
    } else {
      console.log(`✅ ${tableName} table already exists.`);
      return true;
    }
  } catch (error) {
    console.error(`❌ Unexpected error with ${tableName}:`, error.message);
    return false;
  }
}

async function createRunSqlFunction() {
  console.log('Checking/creating necessary helper function...');
  
  const createFunctionSQL = `
CREATE OR REPLACE FUNCTION run_sql(sql_query text)
RETURNS text
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql_query;
  RETURN 'SQL executed successfully';
END;
$$;
  `;
  
  try {
    // Try to use the function to check if it exists
    const { error } = await supabase.rpc('run_sql', { sql_query: 'SELECT 1;' });
    
    if (error && error.message.includes('function "run_sql" does not exist')) {
      console.log('Creating run_sql helper function...');
      
      // Create the function using a raw query
      const { error: rawError } = await supabase.rpc('create_run_sql_function');
      
      if (rawError) {
        console.error('Error creating helper function:', rawError.message);
        console.log('\n⚠️ Please run this SQL in your Supabase dashboard SQL Editor:');
        console.log(createFunctionSQL);
        return false;
      }
      
      console.log('✅ Helper function created successfully.');
      return true;
    } else if (error) {
      console.error('Error testing helper function:', error.message);
      return false;
    } else {
      console.log('✅ Helper function already exists.');
      return true;
    }
  } catch (error) {
    console.error('Unexpected error:', error.message);
    return false;
  }
}

async function createHelperFunctionCreator() {
  console.log('Creating helper function creator...');
  
  const createHelperFunctionCreatorSQL = `
CREATE OR REPLACE FUNCTION create_run_sql_function()
RETURNS text
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  CREATE OR REPLACE FUNCTION run_sql(sql_query text)
  RETURNS text
  LANGUAGE plpgsql SECURITY DEFINER
  AS $inner$
  BEGIN
    EXECUTE sql_query;
    RETURN 'SQL executed successfully';
  END;
  $inner$;
  
  RETURN 'Helper function created successfully';
END;
$$;
  `;
  
  try {
    // Run the raw SQL directly (this is allowed for function creation)
    const { data, error } = await supabase.rpc('create_helper_function_creator');
    
    if (error) {
      if (error.message.includes('function "create_helper_function_creator" does not exist')) {
        console.log('⚠️ Cannot create helper automatically.');
        console.log('\nPlease run this SQL in your Supabase dashboard SQL Editor:');
        console.log(createHelperFunctionCreatorSQL);
        console.log(`\nThen run this script again.`);
        return false;
      } else {
        console.error('Error:', error.message);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Unexpected error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔧 Setting up missing analytics tables in Supabase...\n');
  
  // Create the helper function creator if needed
  const helperCreatorSuccess = await createHelperFunctionCreator();
  if (!helperCreatorSuccess) return;
  
  // Create the run_sql function
  const runSqlSuccess = await createRunSqlFunction();
  if (!runSqlSuccess) return;
  
  // Create tables
  const tables = [
    { name: 'profile_shares', sql: createProfileSharesTableSQL },
    { name: 'profile_views', sql: createProfileViewsTableSQL },
    { name: 'coupon_interactions', sql: createCouponInteractionsTableSQL }
  ];
  
  for (const table of tables) {
    await checkAndCreateTable(table.name, table.sql);
  }
  
  console.log('\n🎉 Database setup process completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Run your application to verify the error is fixed');
  console.log('2. If issues persist, check the browser console for more specific error messages');
}

// Run the script
main(); 