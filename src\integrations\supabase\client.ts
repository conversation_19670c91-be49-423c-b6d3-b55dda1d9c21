// This file provides Supabase client instances for the application.
import { createClient } from '@supabase/supabase-js';
import { Database } from './types';

// Supabase connection details
const SUPABASE_URL = "https://oewgwxxajssonxavydbx.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ld2d3eHhhanNzb254YXZ5ZGJ4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2OTkxMjAsImV4cCI6MjA1OTI3NTEyMH0.Ku-KmiLJ-6Is2uZIDNgs7MEX9jz5v7G-36Aj0eFhKug";

// Add environment specific configuration with error handling
let isProduction = true;
let connectionTimeout = 120000;

try {
  isProduction = import.meta.env?.PROD ?? true; // Default to production mode if env is undefined
  connectionTimeout = isProduction ? 120000 : 60000; // Much longer timeout in production (120s) and dev (60s)
  console.log(`Supabase client initialized in ${isProduction ? 'production' : 'development'} mode with ${connectionTimeout}ms timeout`);
} catch (error) {
  console.warn('Error accessing environment variables, using production defaults:', error);
  isProduction = true;
  connectionTimeout = 120000;
}

// Add this after the connectionTimeout constant
const MAX_RETRIES = 3;

/**
 * Enhanced fetch that handles errors gracefully - especially for analytics endpoints
 * This prevents permission errors from causing white screens
 */
const enhancedFetch = (url: RequestInfo | URL, options?: RequestInit) => {
  // Add timestamp to log for debugging
  const requestTime = new Date().toISOString();

  // Safely convert URL to string with error handling
  let urlString = '';
  try {
    urlString = url.toString();
  } catch (error) {
    console.warn('Error converting URL to string:', error);
    urlString = '';
  }

  // Check if this is an analytics request (coupon_interactions or profile_shares)
  const isAnalyticsRequest =
    urlString.includes('coupon_interactions') ||
    urlString.includes('profile_shares') ||
    urlString.includes('profile_views');

  // Additional request types that should fail silently
  const isSavedCouponRequest = urlString.includes('saved_coupons');
  const isUserAnalyticsRequest = urlString.includes('profile_analytics');
  const isCollectionRequest = urlString.includes('collections');
  
  // Any request that should fail silently without breaking the UI
  const isSilentFailRequest = isAnalyticsRequest || isSavedCouponRequest || 
                              isUserAnalyticsRequest || isCollectionRequest;
  
  // If options already has a signal, we don't want to override it
  const hasExistingSignal = options?.signal !== undefined;
  
  // Only create an abort controller if there isn't already a signal
  const controller = hasExistingSignal ? null : new AbortController();
  let timeoutId: number | null = null;
  
  if (controller) {
    timeoutId = window.setTimeout(() => {
      const safeUrl = urlString.split('?')[0] || 'unknown';
      console.warn(`Request timeout after ${connectionTimeout}ms: ${safeUrl}`);
      controller.abort(new Error(`Request timed out after ${connectionTimeout}ms`));
    }, connectionTimeout);
  }
  
  // Merge our signal with existing options
  const fetchOptions = {
    ...options,
    signal: hasExistingSignal ? options.signal : controller?.signal
  };
  
  return fetch(url, fetchOptions)
    .then(response => {
      if (timeoutId !== null) clearTimeout(timeoutId);
      
      // For requests that should fail silently, log but don't break the app
      if (isSilentFailRequest && (response.status === 403 || response.status === 401 || response.status === 404 || response.status >= 500)) {
        const safeUrl = urlString.split('?')[0] || 'unknown';
        console.warn(`Error for request to ${safeUrl} (Status: ${response.status}). This won't affect the application.`);
        // Return a fake success response to prevent app failure
        return new Response(JSON.stringify({
          data: null,
          error: null,
          success: true,
          error_handled: true
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Handle 500 errors for non-silent requests with better error messages
      if (response.status >= 500) {
        const safeUrl = urlString.split('?')[0] || 'unknown';
        console.error(`Server error ${response.status} for ${safeUrl}`);
        // Try to get error details from response (without await since this is not an async function)
        response.clone().text().then(errorText => {
          console.error('Server error details:', errorText);
        }).catch(e => {
          console.error('Could not read server error details');
        });
      }
      
      // Clone the response so we can both check it and return it
      const clonedResponse = response.clone();
      
      // Handle PGRST116 error (no rows found) for .single() requests
      if (urlString.includes('single=true')) {
        return clonedResponse.json().catch(() => null).then(data => {
          if (data?.code === 'PGRST116') {
            console.warn(`PGRST116 error detected: ${data.message}. Returning empty object instead.`);
            // Return empty data instead of error for .single() with no results
            return new Response(JSON.stringify({ 
              data: null, 
              error: null,
              count: null,
              status: 200,
              statusText: 'OK'
            }), {
              status: 200,
              headers: { 'Content-Type': 'application/json' }
            });
          }
          // Not a PGRST116 error, return original response
          return response;
        }).catch(() => response); // On any parse error, return original response
      }
      
      return response;
    })
    .catch(error => {
      if (timeoutId !== null) clearTimeout(timeoutId);
      console.error('Supabase fetch error:', error);
      
      // For requests that should fail silently, never break the app
      if (isSilentFailRequest) {
        console.warn(`Request failed but will not break the app:`, error.message);
        // Return a fake success response
        return new Response(JSON.stringify({ 
          data: null, 
          error: null, 
          success: true, 
          error_handled: true 
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // For timeout errors, provide a clear message
      if (error.name === 'AbortError') {
        const safeUrl = urlString || 'unknown';
        throw new Error(`Request timeout after ${connectionTimeout}ms: ${safeUrl}`);
      }

      // For other requests, rethrow with more context
      throw new Error(`Network error during Supabase request: ${error.message}`);
    });
};

/**
 * Standard Supabase client for client-side and authenticated operations
 * Import the supabase client like this:
 * import { supabase } from "@/integrations/supabase/client";
 */
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
  global: {
    fetch: enhancedFetch
  }
});

/**
 * Helper function to handle RLS policy violations for brands table
 * This provides a more user-friendly handling of permission issues
 */
export const safeCreateBrand = async (brandData: any) => {
  try {
    // First check if the user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession();
    
    if (!session) {
      console.error('Cannot create brand: No active session');
      return { 
        data: null, 
        error: new Error('Authentication required to create brands'), 
        success: false 
      };
    }
    
    console.log('Creating brand with authenticated user:', session.user.id);
    
    // Validate the brand data before submission
    if (!brandData.name || typeof brandData.name !== 'string' || brandData.name.trim().length < 2) {
      console.error('Invalid brand data: name is required and must be at least 2 characters');
      return {
        data: null,
        error: new Error('Brand name is required and must be at least 2 characters'),
        success: false
      };
    }
    
    // Try to create the brand with retry logic
    let lastError = null;
    let data = null;
    
    // Try up to 2 times with exponential backoff
    for (let attempt = 0; attempt < 2; attempt++) {
      try {
        if (attempt > 0) {
          console.log(`Retry attempt ${attempt+1} for brand creation`);
          // Wait a bit before retrying
          await new Promise(r => setTimeout(r, 1000 * attempt));
        }
        
        const result = await supabase
          .from('brands')
          .insert([brandData])
          .select();
        
        if (result.error) {
          lastError = result.error;
          console.error(`Attempt ${attempt+1} failed:`, result.error);
          
          // Don't retry for these specific error types
          if (
            result.error.code === '23505' || // Duplicate key
            result.error.message?.includes('violates row-level security policy') || // RLS violation
            result.error.code === '23502' || // Not null violation
            result.error.code === '23514' // Check constraint
          ) {
            throw result.error;
          }
          
          // For other errors, we'll retry
          continue;
        }
        
        // Success - no need for more retries
        data = result.data;
        lastError = null;
        break;
      } catch (error) {
        lastError = error;
        // If it's a specific error we don't want to retry, rethrow
        if (
          error.code === '23505' || 
          error.message?.includes('violates row-level security policy') ||
          error.code === '23502' ||
          error.code === '23514'
        ) {
          throw error;
        }
      }
    }
    
    // Check if we had an error after all retries
    if (lastError) {
      console.error('Brand creation error after all retries:', lastError);
      
      // Specially handle RLS policy violations
      if (lastError.message && lastError.message.includes('violates row-level security policy')) {
        return {
          data: null,
          error: new Error('You do not have permission to create brands. Please contact an administrator.'),
          success: false
        };
      }
      
      // Handle other common database errors
      if (lastError.code === '23505') {
        return {
          data: null,
          error: new Error('A brand with this name already exists.'),
          success: false
        };
      }
      
      return { data: null, error: lastError, success: false };
    }
    
    return { data, error: null, success: true };
  } catch (error) {
    console.error('Unexpected error during brand creation:', error);
    
    // Make error messages more user friendly
    let errorMessage = 'Unknown error occurred';
    
    if (error instanceof Error) {
      if (error.message && error.message.includes('violates row-level security policy')) {
        errorMessage = 'You do not have permission to create brands. Please contact an administrator.';
      } else if ((error as any).code === '23505') {
        errorMessage = 'A brand with this name already exists.';
      } else {
        errorMessage = error.message;
      }
    }
    
    return { 
      data: null, 
      error: new Error(errorMessage), 
      success: false 
    };
  }
};

/**
 * Creates a Supabase client with service role privileges for admin operations
 * Only use server-side where the service role key can be securely stored
 * @param serviceRoleKey The Supabase service role key
 * @returns A Supabase client with admin privileges
 */
export const createServiceClient = (serviceRoleKey: string) => {
  if (!serviceRoleKey) {
    throw new Error('Service role key is required for admin operations');
  }
  
  return createClient<Database>(SUPABASE_URL, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    global: {
      fetch: enhancedFetch
    }
  });
};

/**
 * Helper to check if Supabase is properly configured
 * Useful for debugging connection issues
 */
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase.from('categories').select('count').limit(1);
    if (error) {
      console.error('Supabase connection test error:', error);
      return false;
    }
    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Supabase connection error:', error);
    return false;
  }
};

/**
 * Utility to retry a Supabase operation with backoff
 * @param operation Function that performs a Supabase operation
 * @param maxRetries Maximum number of retry attempts
 * @param delayMs Base delay in milliseconds
 * @returns Result of the operation
 */
export const retryOperation = async <T>(
  operation: () => Promise<T>, 
  maxRetries = 3, 
  delayMs = 300
): Promise<T> => {
  let lastError: any;
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      console.warn(`Supabase operation attempt ${attempt + 1} failed:`, error);
      lastError = error;
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delayMs * Math.pow(2, attempt)));
    }
  }
  // If we get here, all retries failed
  throw lastError;
};

/**
 * Check if Supabase is actually accessible
 * Used to detect if the database is unavailable or there are connection issues
 */
export const checkSupabaseHealth = async () => {
  try {
    console.log('Performing Supabase health check...');

    // Use a shorter timeout for health check to avoid long app loading
    const healthCheckTimeout = 5000; // 5 seconds max for health check

    // Check for recent failures, but be less aggressive about caching
    const lastFailureTime = localStorage.getItem('supabase_last_failure');
    const failureThreshold = 2 * 60 * 1000; // Reduced to 2 minutes

    if (lastFailureTime && Date.now() - parseInt(lastFailureTime) < failureThreshold) {
      console.warn('Recent Supabase failure detected, but attempting fresh check anyway');
      // Don't immediately return degraded - try a quick check first
    }
    
    try {
      // Use the retry operation with shorter timeout
      return await retryOperation(async () => {
        const startTime = Date.now();

        // Make a simple ping request with reduced timeout
        const response = await fetch(`${SUPABASE_URL}/rest/v1/?apikey=${SUPABASE_ANON_KEY}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': SUPABASE_ANON_KEY
          },
          signal: AbortSignal.timeout(healthCheckTimeout)
        });

        const elapsed = Date.now() - startTime;

        if (response.ok) {
          console.log(`Supabase health check succeeded in ${elapsed}ms`);
          // Clear any saved failure timestamp on success
          localStorage.removeItem('supabase_last_failure');
          return { status: 'healthy', elapsed };
        } else {
          console.error(`Supabase health check failed: HTTP ${response.status}`);
          // Only record failure time for serious errors (not 4xx client errors)
          if (response.status >= 500) {
            localStorage.setItem('supabase_last_failure', Date.now().toString());
          }
          return { status: 'unhealthy', reason: `HTTP ${response.status}`, elapsed };
        }
      }, 1, 500); // Reduced to 1 retry with 500ms delay for faster response
    } catch (timeoutError) {
      // If the main check times out, try a much simpler test
      try {
        console.warn('Primary health check failed, attempting fallback check');
        const fallbackResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
          method: 'HEAD', // Just check if the endpoint exists, don't fetch data
          headers: { 'apikey': SUPABASE_ANON_KEY },
          signal: AbortSignal.timeout(1500) // Very short timeout
        });

        if (fallbackResponse.ok) {
          console.log('Fallback health check succeeded');
          // Don't record this as a failure since fallback worked
          return {
            status: 'degraded',
            reason: 'Slow connection',
            suggestion: 'Database connection is slow but available. Some operations may take longer.'
          };
        }
        throw new Error('Fallback check failed');
      } catch (fallbackError) {
        throw timeoutError; // Rethrow the original error if fallback also fails
      }
    }
  } catch (error) {
    console.error('Supabase health check error after retries:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Only record failure time for network errors, not client errors
    if (errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('fetch')) {
      localStorage.setItem('supabase_last_failure', Date.now().toString());
    }

    // Return a more graceful response instead of failing completely
    return {
      status: 'degraded', // Changed from 'error' to 'degraded' to indicate app can still function
      reason: errorMessage,
      suggestion: 'Database connection is slow or intermittent. Some features may be limited.'
    };
  }
};