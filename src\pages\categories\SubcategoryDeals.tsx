import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import CouponCard from '@/components/CouponCard';
import CouponsSkeleton from '@/components/CouponsSkeleton';
import { useCoupons } from '@/hooks/useCoupons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Filter, 
  ShoppingBag,
  Monitor,
  Smartphone,
  Shirt,
  Users,
  Tag
} from 'lucide-react';

const SubcategoryDeals = () => {
  const { category, subcategory } = useParams<{ category: string; subcategory: string }>();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  
  // Fetch all coupons
  const { data: allCoupons, isLoading, error } = useCoupons();
  
  // Get display names and icons
  const getCategoryInfo = (cat: string, subcat: string) => {
    const categoryMap = {
      electronics: {
        name: 'Electronics',
        icon: Monitor,
        subcategories: {
          laptops: { name: 'Laptops', description: 'Best laptop deals and discount codes' },
          smartphones: { name: 'Smartphones', description: 'Mobile phone deals and offers' }
        }
      },
      fashion: {
        name: 'Fashion',
        icon: Shirt,
        subcategories: {
          womens: { name: "Women's Fashion", description: "Women's clothing and accessories" },
          mens: { name: "Men's Fashion", description: "Men's clothing and accessories" }
        }
      }
    };
    
    const catInfo = categoryMap[cat as keyof typeof categoryMap];
    const subcatInfo = catInfo?.subcategories[subcat as keyof typeof catInfo.subcategories];
    
    return {
      categoryName: catInfo?.name || cat,
      subcategoryName: subcatInfo?.name || subcat,
      description: subcatInfo?.description || `${subcatInfo?.name || subcat} deals and discounts`,
      icon: catInfo?.icon || ShoppingBag
    };
  };
  
  const categoryInfo = getCategoryInfo(category || '', subcategory || '');
  
  // Filter coupons for this subcategory
  const subcategoryCoupons = allCoupons?.filter(coupon => {
    // Match by category name
    const categoryMatch = coupon.category?.name?.toLowerCase().includes(category?.toLowerCase() || '');
    
    // Match by subcategory keywords in description or code
    const subcategoryKeywords = subcategory?.toLowerCase().split('-') || [];
    const subcategoryMatch = subcategoryKeywords.some(keyword =>
      coupon.discount_description?.toLowerCase().includes(keyword) ||
      coupon.code?.toLowerCase().includes(keyword) ||
      coupon.brand?.name?.toLowerCase().includes(keyword)
    );
    
    return categoryMatch || subcategoryMatch;
  }) || [];
  
  // Apply search filter
  const filteredCoupons = subcategoryCoupons.filter(coupon => {
    if (!searchQuery) return true;
    
    return coupon.brand?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
           coupon.discount_description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
           coupon.code?.toLowerCase().includes(searchQuery.toLowerCase());
  });
  
  // Sort coupons
  const sortedCoupons = [...filteredCoupons].sort((a, b) => {
    switch (sortBy) {
      case 'discount':
        return (b.discount_percent || 0) - (a.discount_percent || 0);
      case 'expiry':
        if (!a.expires_at) return 1;
        if (!b.expires_at) return -1;
        return new Date(a.expires_at).getTime() - new Date(b.expires_at).getTime();
      case 'newest':
      default:
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
    }
  });

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.8}>
        <PageHeaderWithBackButton
          title={categoryInfo.subcategoryName}
          subtitle={categoryInfo.description}
          icon={categoryInfo.icon}
        />

        {/* Category Banner */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-6 mb-8 relative overflow-hidden">
          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-white/20 p-2 rounded-lg">
                <categoryInfo.icon className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">{categoryInfo.subcategoryName}</h2>
                <p className="text-blue-100">{categoryInfo.description}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
              <div className="bg-white/10 rounded-lg p-3">
                <Tag className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">{subcategoryCoupons.length}</div>
                <div className="text-xs text-blue-100">Active Deals</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <Users className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">
                  {Math.max(...subcategoryCoupons.map(c => c.discount_percent || 0), 0)}%
                </div>
                <div className="text-xs text-blue-100">Max Discount</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <ShoppingBag className="h-5 w-5 mx-auto mb-1" />
                <div className="text-lg font-bold">
                  {new Set(subcategoryCoupons.map(c => c.brand?.name)).size}
                </div>
                <div className="text-xs text-blue-100">Brands</div>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
        </div>

        {/* Search and Sort Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={`Search ${categoryInfo.subcategoryName.toLowerCase()} deals...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={sortBy === 'newest' ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy('newest')}
              >
                Newest
              </Button>
              <Button
                variant={sortBy === 'discount' ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy('discount')}
              >
                Best Discount
              </Button>
              <Button
                variant={sortBy === 'expiry' ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy('expiry')}
              >
                Expiring Soon
              </Button>
            </div>
          </div>
        </div>

        {/* Deals Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <CouponsSkeleton count={12} />
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
            <p className="text-red-600">Failed to load deals. Please try again later.</p>
          </div>
        ) : sortedCoupons.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedCoupons.map((coupon, index) => (
              <motion.div
                key={coupon.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="relative"
              >
                <div className="absolute -top-2 -right-2 z-10">
                  <Badge className="bg-blue-600 text-white">
                    {categoryInfo.categoryName}
                  </Badge>
                </div>
                <CouponCard
                  id={coupon.id}
                  brandName={coupon.brand?.name || "Unknown Brand"}
                  brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                  influencerName={coupon.influencer?.full_name || "Anonymous"}
                  influencerImage={coupon.influencer?.avatar_url}
                  discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                  expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                  couponCode={coupon.code}
                  category={coupon.category?.name || "General"}
                  featured={coupon.featured}
                  isPremium={coupon.is_premium}
                  brandId={coupon.brand?.id}
                  price={coupon.price}
                />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
            <categoryInfo.icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No {categoryInfo.subcategoryName.toLowerCase()} deals found.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => setSearchQuery('')}
            >
              Clear Search
            </Button>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default SubcategoryDeals;
