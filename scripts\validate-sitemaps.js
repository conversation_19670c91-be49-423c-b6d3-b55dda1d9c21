#!/usr/bin/env node

/**
 * <PERSON>ript to validate consolidated sitemap structure
 * Ensures all sitemaps are properly organized and accessible
 */

const fs = require('fs');
const path = require('path');

console.log('🗺️  Validating Consolidated Sitemap Structure...\n');

// Expected sitemap files
const expectedSitemaps = [
  'public/sitemap.xml',
  'public/sitemapindex.xml',
  'public/sitemap-main.xml',
  'public/sitemap-brands.xml',
  'public/sitemap-categories.xml',
  'public/sitemap-deals.xml',
  'public/sitemap-blog.xml'
];

let allValid = true;

console.log('📁 File Structure Check:');
expectedSitemaps.forEach(file => {
  if (fs.existsSync(file)) {
    const stats = fs.statSync(file);
    const fileSizeKB = Math.round(stats.size / 1024);
    console.log(`✅ ${file} (${fileSizeKB} KB)`);
  } else {
    console.log(`❌ ${file} missing`);
    allValid = false;
  }
});

console.log('');

// Check for duplicate sitemaps folder
console.log('🔍 Duplicate Check:');
if (fs.existsSync('public/sitemaps')) {
  console.log('⚠️  Warning: public/sitemaps directory still exists');
  console.log('   This may cause confusion - consider removing it');
} else {
  console.log('✅ No duplicate sitemaps directory found');
}

console.log('');

// Validate sitemapindex.xml content
console.log('🔗 Sitemapindex Validation:');
try {
  const sitemapIndexContent = fs.readFileSync('public/sitemapindex.xml', 'utf8');
  
  // Check if all referenced sitemaps exist
  const referencedSitemaps = [
    'sitemap-main.xml',
    'sitemap-brands.xml', 
    'sitemap-categories.xml',
    'sitemap-deals.xml',
    'sitemap-blog.xml'
  ];
  
  referencedSitemaps.forEach(sitemap => {
    if (sitemapIndexContent.includes(sitemap)) {
      if (fs.existsSync(`public/${sitemap}`)) {
        console.log(`✅ ${sitemap} referenced and exists`);
      } else {
        console.log(`❌ ${sitemap} referenced but missing`);
        allValid = false;
      }
    } else {
      console.log(`⚠️  ${sitemap} not referenced in sitemapindex.xml`);
    }
  });
  
  // Check lastmod dates
  if (sitemapIndexContent.includes('2025-06-16')) {
    console.log('✅ Sitemapindex has current dates');
  } else {
    console.log('⚠️  Sitemapindex may have outdated dates');
  }
  
} catch (error) {
  console.log('❌ Error reading sitemapindex.xml:', error.message);
  allValid = false;
}

console.log('');

// Validate robots.txt references
console.log('🤖 Robots.txt Validation:');
try {
  const robotsContent = fs.readFileSync('public/robots.txt', 'utf8');
  
  const expectedSitemapRefs = [
    'https://couponlink.in/sitemap.xml',
    'https://couponlink.in/sitemapindex.xml',
    'https://couponlink.in/sitemap-main.xml',
    'https://couponlink.in/sitemap-brands.xml',
    'https://couponlink.in/sitemap-categories.xml',
    'https://couponlink.in/sitemap-deals.xml',
    'https://couponlink.in/sitemap-blog.xml'
  ];
  
  expectedSitemapRefs.forEach(ref => {
    if (robotsContent.includes(ref)) {
      console.log(`✅ ${ref} referenced in robots.txt`);
    } else {
      console.log(`⚠️  ${ref} not referenced in robots.txt`);
    }
  });
  
} catch (error) {
  console.log('❌ Error reading robots.txt:', error.message);
}

console.log('');

// Check XML validity (basic)
console.log('📄 XML Structure Check:');
expectedSitemaps.forEach(file => {
  if (fs.existsSync(file)) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('<?xml') && (content.includes('<urlset') || content.includes('<sitemapindex'))) {
        console.log(`✅ ${path.basename(file)} has valid XML structure`);
      } else {
        console.log(`❌ ${path.basename(file)} has invalid XML structure`);
        allValid = false;
      }
    } catch (error) {
      console.log(`❌ Error reading ${file}:`, error.message);
      allValid = false;
    }
  }
});

console.log('');

// Summary and recommendations
console.log('📊 Summary:');
if (allValid) {
  console.log('✅ All sitemaps are properly consolidated and valid!');
  console.log('');
  console.log('🚀 Your sitemap structure is now:');
  console.log('   📁 public/');
  console.log('   ├── 🗺️  sitemapindex.xml (main index)');
  console.log('   ├── 🗺️  sitemap.xml (comprehensive sitemap)');
  console.log('   ├── 🗺️  sitemap-main.xml (homepage & main pages)');
  console.log('   ├── 🗺️  sitemap-brands.xml (brand pages)');
  console.log('   ├── 🗺️  sitemap-categories.xml (category pages)');
  console.log('   ├── 🗺️  sitemap-deals.xml (deals pages)');
  console.log('   └── 🗺️  sitemap-blog.xml (blog pages)');
  console.log('');
  console.log('🎯 Benefits:');
  console.log('   ✅ No duplicate sitemaps');
  console.log('   ✅ Clean, organized structure');
  console.log('   ✅ All files accessible at root level');
  console.log('   ✅ Proper robots.txt references');
  console.log('   ✅ Current modification dates');
} else {
  console.log('⚠️  Some issues found. Please review and fix.');
}

console.log('');
console.log('🔗 Test URLs:');
console.log('After deployment, verify these URLs work:');
console.log('   https://couponlink.in/sitemapindex.xml');
console.log('   https://couponlink.in/sitemap.xml');
console.log('   https://couponlink.in/sitemap-main.xml');
console.log('   https://couponlink.in/sitemap-brands.xml');
console.log('   https://couponlink.in/sitemap-categories.xml');
console.log('   https://couponlink.in/sitemap-deals.xml');
console.log('   https://couponlink.in/sitemap-blog.xml');

console.log('');
console.log('📝 Next Steps:');
console.log('1. Deploy your changes');
console.log('2. Test the URLs above');
console.log('3. Submit sitemapindex.xml to Google Search Console');
console.log('4. Remove any old sitemap submissions in Search Console');
