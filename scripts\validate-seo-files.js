#!/usr/bin/env node

/**
 * Script to validate SEO files accessibility
 * This helps ensure robots.txt and sitemaps are properly served
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating SEO files...\n');

// Check if files exist
const seoFiles = [
  'public/robots.txt',
  'public/sitemap.xml',
  'public/sitemapindex.xml',
  'public/sitemap-main.xml',
  'public/sitemap-brands.xml',
  'public/sitemap-categories.xml',
  'public/sitemap-deals.xml',
  'public/sitemap-blog.xml'
];

let allFilesExist = true;

seoFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
    allFilesExist = false;
  }
});

// Validate robots.txt content
console.log('\n🤖 Validating robots.txt content...');
try {
  const robotsContent = fs.readFileSync('public/robots.txt', 'utf8');
  
  // Check for common issues
  if (robotsContent.includes('Disallow: /*?*')) {
    console.log('⚠️  Warning: robots.txt contains overly restrictive query parameter blocking');
  } else {
    console.log('✅ Query parameter handling looks good');
  }
  
  if (robotsContent.includes('Sitemap:')) {
    console.log('✅ Sitemap references found in robots.txt');
  } else {
    console.log('❌ No sitemap references found in robots.txt');
  }
  
  // Check sitemap URLs
  const sitemapLines = robotsContent.split('\n').filter(line => line.startsWith('Sitemap:'));
  sitemapLines.forEach(line => {
    const url = line.replace('Sitemap:', '').trim();
    const filename = url.split('/').pop();
    if (fs.existsSync(`public/${filename}`)) {
      console.log(`✅ Sitemap file exists: ${filename}`);
    } else {
      console.log(`❌ Sitemap file missing: ${filename}`);
      allFilesExist = false;
    }
  });
  
} catch (error) {
  console.log('❌ Error reading robots.txt:', error.message);
  allFilesExist = false;
}

// Validate XML sitemaps
console.log('\n🗺️  Validating XML sitemaps...');
const xmlFiles = seoFiles.filter(file => file.endsWith('.xml'));

xmlFiles.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    if (content.includes('<?xml') && content.includes('<urlset') || content.includes('<sitemapindex')) {
      console.log(`✅ ${file} has valid XML structure`);
    } else {
      console.log(`❌ ${file} has invalid XML structure`);
      allFilesExist = false;
    }
  } catch (error) {
    console.log(`❌ Error reading ${file}:`, error.message);
    allFilesExist = false;
  }
});

console.log('\n📊 Validation Summary:');
if (allFilesExist) {
  console.log('✅ All SEO files are properly configured!');
  console.log('\n📝 Next steps:');
  console.log('1. Deploy your changes');
  console.log('2. Test robots.txt: https://couponlink.in/robots.txt');
  console.log('3. Test sitemap: https://couponlink.in/sitemapindex.xml');
  console.log('4. Submit to Google Search Console');
  console.log('5. Request re-indexing in Google Search Console');
} else {
  console.log('❌ Some issues found. Please fix them before deploying.');
  process.exit(1);
}
