import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { COLORS } from '@/constants/theme';

interface BackButtonProps {
  className?: string;
  label?: string;
  customAction?: () => void;
}

/**
 * BackButton - A standardized back button component 
 * Used for consistent navigation experience across pages
 */
const BackButton = ({ 
  className = '',
  label = 'Back',
  customAction
}: BackButtonProps) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (customAction) {
      customAction();
    } else {
      navigate(-1);
    }
  };
  
  return (
    <button 
      onClick={handleClick}
      className={`p-2 rounded-full shadow-sm hover:shadow-md transition-all flex items-center justify-center ${className}`}
      style={{ background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})` }}
      aria-label="Go back"
    >
      <ArrowLeft className="w-5 h-5" style={{ color: COLORS.neutral[700] }} />
      {label !== 'Back' && <span className="ml-2">{label}</span>}
    </button>
  );
};

export default BackButton;
