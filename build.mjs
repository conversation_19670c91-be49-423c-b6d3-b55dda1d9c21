#!/usr/bin/env node
import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔧 Starting production build...');
console.log('📂 Working directory:', process.cwd());
console.log('🔧 Node version:', process.version);

try {
  // Ensure dist directory exists
  if (!fs.existsSync('./dist')) {
    fs.mkdirSync('./dist', { recursive: true });
  }

  // Set production environment
  process.env.NODE_ENV = 'production';
  
  console.log('🔨 Building application...');
  // Check if vite.config.js exists
  if (fs.existsSync('./vite.config.js')) {
    console.log('✅ vite.config.js found');
  } else {
    console.log('❌ vite.config.js not found');
  }

  execSync('vite build --mode production', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'production'
    }
  });
  
  console.log('✅ Build completed successfully!');
  
  // Check if the build files exist
  if (fs.existsSync('./dist/index.html')) {
    console.log('✅ Build files verified in dist/');
  } else {
    console.log('❌ index.html not found in dist/');
  }
} catch (error) {
  console.error('❌ Build failed:', error);
  process.exit(1);
} 