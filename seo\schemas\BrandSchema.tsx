import React from 'react';
import { Helmet } from 'react-helmet-async';

interface BrandSchemaProps {
  id: string;
  name: string;
  description?: string;
  logoUrl?: string;
  websiteUrl?: string;
  couponsCount?: number;
}

/**
 * Structured data schema for brand pages
 * Helps improve search results with rich data about the brand and its offers
 */
const BrandSchema: React.FC<BrandSchemaProps> = ({
  id,
  name,
  description = '',
  logoUrl = '',
  websiteUrl = '',
  couponsCount = 0
}) => {
  // Default description if none provided
  const brandDescription = description || 
    `Find the latest ${name} coupon codes, promo offers, and discounts. Save money on your next purchase with exclusive ${name} deals.`;
  
  // Brand organization schema
  const brandSchema = {
    "@context": "https://schema.org",
    "@type": "Brand",
    "@id": `https://www.couponlink.in/brands/${id}#brand`,
    "name": name,
    "description": brandDescription,
    "url": `https://www.couponlink.in/brands/${id}`,
    "logo": logoUrl || "https://www.couponlink.in/placeholder.svg",
    "sameAs": websiteUrl ? [websiteUrl] : []
  };
  
  // Offer catalog schema to show Google this is a collection of offers
  const offerCatalogSchema = {
    "@context": "https://schema.org",
    "@type": "OfferCatalog",
    "@id": `https://www.couponlink.in/brands/${id}#offerings`,
    "name": `${name} Coupons and Promo Codes`,
    "description": `Collection of ${couponsCount} verified coupon codes and promotional offers for ${name}.`,
    "url": `https://www.couponlink.in/brands/${id}`,
    "itemListElement": [
      {
        "@type": "OfferCatalog",
        "name": `Active ${name} Coupons`,
        "numberOfItems": couponsCount
      }
    ]
  };
  
  // Breadcrumb schema for better navigation in search results
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://www.couponlink.in/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Brands",
        "item": "https://www.couponlink.in/brands"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": name,
        "item": `https://www.couponlink.in/brands/${id}`
      }
    ]
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(brandSchema)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(offerCatalogSchema)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
      <link rel="canonical" href={`https://www.couponlink.in/brands/${id}`} />
    </Helmet>
  );
};

export default BrandSchema; 