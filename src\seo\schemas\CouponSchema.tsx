import React from 'react';
import { Helmet } from 'react-helmet-async';

interface CouponSchemaProps {
  name: string;
  description: string;
  code: string;
  discount?: {
    type: 'Percentage' | 'Amount';
    value: number;
    currency?: string;
  };
  validFrom?: string;
  validThrough?: string;
  merchant?: {
    name: string;
    url?: string;
  };
  category?: string;
  termsAndConditions?: string;
}

/**
 * Component that adds Schema.org structured data for coupons
 * This helps search engines understand coupon information and can lead to rich snippets
 */
const CouponSchema: React.FC<CouponSchemaProps> = ({
  name,
  description,
  code,
  discount,
  validFrom,
  validThrough,
  merchant,
  category,
  termsAndConditions
}) => {
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "Offer",
    "name": name,
    "description": description,
    "category": category,
    ...(discount && {
      "priceSpecification": {
        "@type": "PriceSpecification",
        "type": discount.type === 'Percentage' ? 'Percentage' : 'MonetaryAmount',
        "value": discount.value,
        ...(discount.currency && { "currency": discount.currency })
      }
    }),
    ...(merchant && {
      "merchant": {
        "@type": "Organization",
        "name": merchant.name,
        ...(merchant.url && { "url": merchant.url })
      }
    }),
    "couponCode": code,
    ...(validFrom && { "validFrom": validFrom }),
    ...(validThrough && { "validThrough": validThrough }),
    ...(termsAndConditions && { "termsAndConditions": termsAndConditions })
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schemaData)}
      </script>
    </Helmet>
  );
};

export default CouponSchema; 