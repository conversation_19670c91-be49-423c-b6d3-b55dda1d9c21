<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Multi-Size Favicon Generator - CouponLink</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .container {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      background-color: #f9f9f9;
    }
    h1, h2 {
      color: #0095F6;
    }
    .btn {
      display: inline-block;
      background-color: #0095F6;
      color: white;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 5px;
      margin: 10px 0;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: none;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #007ad1;
    }
    .preview-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin: 30px 0;
    }
    .preview-box {
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: white;
    }
    .preview-box p {
      margin: 5px 0;
    }
    #colorPicker {
      width: 80px;
      height: 40px;
      padding: 0;
      border: none;
      border-radius: 4px;
      margin: 10px 0;
    }
    .input-group {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 100%;
      max-width: 300px;
    }
    .result {
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #4caf50;
      border-radius: 8px;
      background-color: #f1f8e9;
      display: none;
    }
    .download-section {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-top: 20px;
    }
    canvas {
      border: 1px solid #ddd;
      background: white;
    }
  </style>
</head>
<body>
  <h1>Multi-Size Favicon Generator</h1>
  <p>Generate multiple sizes of your favicon for better Windows visibility. This tool creates PNG files in different sizes that can be converted to a single ICO file.</p>
  
  <div class="container">
    <h2>Customize Your Favicon</h2>
    
    <div class="input-group">
      <label for="textInput">Text (1-2 characters):</label>
      <input type="text" id="textInput" maxlength="2" value="P" placeholder="P">
    </div>
    
    <div class="input-group">
      <label for="colorPicker">Background Color:</label>
      <input type="color" id="colorPicker" value="#0095F6">
    </div>
    
    <button id="generateBtn" class="btn">Generate Favicons</button>
    
    <div id="previewContainer" class="preview-grid" style="display: none;">
      <!-- Preview boxes will be generated here -->
    </div>
    
    <div id="result" class="result">
      <h3>Generated Favicons</h3>
      <p>Right-click on each favicon and select "Save image as..." to download:</p>
      <div id="downloadContainer"></div>
      
      <h3>Next Steps</h3>
      <ol>
        <li>Download all size variations</li>
        <li>Go to <a href="https://convertico.com/favicon-generator/" target="_blank">ConvertICO Favicon Generator</a></li>
        <li>Upload the PNG files you just downloaded</li>
        <li>Generate a multi-size ICO file</li>
        <li>Replace your existing favicon.ico in public directory</li>
      </ol>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const sizes = [16, 32, 48, 64];
      const textInput = document.getElementById('textInput');
      const colorPicker = document.getElementById('colorPicker');
      const generateBtn = document.getElementById('generateBtn');
      const previewContainer = document.getElementById('previewContainer');
      const result = document.getElementById('result');
      const downloadContainer = document.getElementById('downloadContainer');
      
      generateBtn.addEventListener('click', function() {
        const text = textInput.value || 'P';
        const color = colorPicker.value;
        
        previewContainer.innerHTML = '';
        downloadContainer.innerHTML = '';
        
        // Generate favicons for each size
        sizes.forEach(size => {
          generateFavicon(text, color, size);
        });
        
        previewContainer.style.display = 'grid';
        result.style.display = 'block';
      });
      
      function generateFavicon(text, bgColor, size) {
        // Create canvas
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        // Draw background
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, size, size);
        
        // Make rounded corners - scale according to size
        const radius = Math.max(size / 8, 2);
        ctx.fillStyle = bgColor;
        ctx.beginPath();
        ctx.moveTo(0, radius);
        ctx.arcTo(0, 0, radius, 0, radius);
        ctx.lineTo(size - radius, 0);
        ctx.arcTo(size, 0, size, radius, radius);
        ctx.lineTo(size, size - radius);
        ctx.arcTo(size, size, size - radius, size, radius);
        ctx.lineTo(radius, size);
        ctx.arcTo(0, size, 0, size - radius, radius);
        ctx.closePath();
        ctx.fill();
        
        // Draw text
        ctx.fillStyle = 'white';
        ctx.font = `bold ${Math.floor(size * 0.6)}px Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, size / 2, size / 2);
        
        // Create preview box
        const previewBox = document.createElement('div');
        previewBox.className = 'preview-box';
        
        // Clone the canvas for preview
        const previewCanvas = canvas.cloneNode(true);
        const previewCtx = previewCanvas.getContext('2d');
        previewCtx.drawImage(canvas, 0, 0);
        previewBox.appendChild(previewCanvas);
        
        // Add size label
        const sizeLabel = document.createElement('p');
        sizeLabel.textContent = `${size}×${size}`;
        previewBox.appendChild(sizeLabel);
        
        // Add to preview container
        previewContainer.appendChild(previewBox);
        
        // Create download section
        const downloadDiv = document.createElement('div');
        downloadDiv.className = 'download-section';
        
        const downloadCanvas = canvas.cloneNode(true);
        const downloadCtx = downloadCanvas.getContext('2d');
        downloadCtx.drawImage(canvas, 0, 0);
        downloadDiv.appendChild(downloadCanvas);
        
        const downloadLink = document.createElement('a');
        downloadLink.href = canvas.toDataURL('image/png');
        downloadLink.download = `favicon-${size}x${size}.png`;
        downloadLink.className = 'btn';
        downloadLink.textContent = `Download ${size}×${size}`;
        downloadDiv.appendChild(downloadLink);
        
        downloadContainer.appendChild(downloadDiv);
      }
    });
  </script>
  
  <p><a href="/favicon-fix.html" style="color: #0095F6;">Back to Favicon Fix Tool</a></p>
</body>
</html> 