
import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { Search, Filter, SortAsc } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSearchCoupons, useCoupons } from '@/hooks/useCoupons';
import CouponCard from '@/components/CouponCard';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import EnhancedSearchBar from '@/components/search/EnhancedSearchBar';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  // Get search query from URL
  const searchQuery = searchParams.get('q') || '';

  // Initialize local search with URL query
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Use search hook if there's a query, otherwise use all coupons
  const { data: searchResults, isLoading: isSearchLoading, error: searchError } = useSearchCoupons(searchQuery);
  const { data: allCoupons, isLoading: isAllLoading } = useCoupons();

  // Determine which data to use
  const coupons = searchQuery ? searchResults : allCoupons;
  const isLoading = searchQuery ? isSearchLoading : isAllLoading;

  // Sort coupons based on selected option
  const sortedCoupons = React.useMemo(() => {
    if (!coupons) return [];

    const sorted = [...coupons];
    switch (sortBy) {
      case 'newest':
        return sorted.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      case 'brand':
        return sorted.sort((a, b) => (a.brand?.name || '').localeCompare(b.brand?.name || ''));
      case 'discount':
        return sorted.sort((a, b) => {
          const aDiscount = parseInt(a.discount_description?.match(/\d+/)?.[0] || '0');
          const bDiscount = parseInt(b.discount_description?.match(/\d+/)?.[0] || '0');
          return bDiscount - aDiscount;
        });
      default:
        return sorted;
    }
  }, [coupons, sortBy]);

  // Handle search from enhanced search bar
  const handleEnhancedSearch = (query: string) => {
    setSearchParams({ q: query });
  };

  // Handle clear search
  const handleClearSearch = () => {
    setLocalSearchQuery('');
    setSearchParams({});
  };

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title={searchQuery ? `Search Results for "${searchQuery}"` : "Search Coupons"}
          subtitle={searchQuery ? `Found ${sortedCoupons?.length || 0} results` : "Find the perfect coupon codes and deals"}
          icon={Search}
        />

        {/* Enhanced Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <div className="flex gap-3 mb-4">
            <div className="flex-grow">
              <EnhancedSearchBar
                placeholder="Search by brand, influencer, category, code, or description..."
                onSearch={handleEnhancedSearch}
                size="md"
                showPopup={true}
                autoFocus={!searchQuery}
                className="w-full"
              />
            </div>
            {searchQuery && (
              <Button type="button" variant="outline" onClick={handleClearSearch}>
                Clear
              </Button>
            )}
          </div>

          {/* Filters and Sort */}
          <div className="flex flex-wrap gap-3 items-center">
            <div className="flex items-center gap-2">
              <SortAsc className="h-4 w-4 text-gray-500" />
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="brand">Brand A-Z</SelectItem>
                  <SelectItem value="discount">Highest Discount</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </motion.div>

        {/* Search Results */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array(8).fill(null).map((_, index) => (
                <Skeleton key={index} className="h-64 rounded-lg" />
              ))}
            </div>
          ) : searchError ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-2">Error loading search results</div>
              <p className="text-gray-500">Please try again later</p>
            </div>
          ) : sortedCoupons && sortedCoupons.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {sortedCoupons.map((coupon, index) => (
                <motion.div
                  key={coupon.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <CouponCard
                    id={coupon.id}
                    brandName={coupon.brand?.name || "Unknown Brand"}
                    brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
                    influencerName={coupon.influencer?.full_name || ""}
                    influencerImage={coupon.influencer?.avatar_url || undefined}
                    discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                    expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                    couponCode={coupon.code}
                    category={coupon.category?.name || "Uncategorized"}
                    featured={coupon.featured}
                    isPremium={coupon.is_premium}
                    brandId={coupon.brand?.id}
                    price={coupon.price}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'No results found' : 'Start searching'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchQuery
                  ? `No coupons found for "${searchQuery}". Try different keywords.`
                  : 'Enter a search term to find coupons and deals'
                }
              </p>
              {searchQuery && (
                <Button onClick={handleClearSearch} variant="outline">
                  Browse All Coupons
                </Button>
              )}
            </div>
          )}
        </motion.div>
      </PageContainer>
    </MainLayout>
  );
};

export default SearchPage;
