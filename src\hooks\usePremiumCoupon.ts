import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { useQuery } from '@tanstack/react-query';

export const usePremiumCoupon = (couponId: string) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(false);
  
  // Check if user has already purchased this premium coupon
  const { data: hasPurchased, isLoading: checkingPurchase } = useQuery({
    queryKey: ['premium-purchase', couponId, user?.id],
    queryFn: async () => {
      if (!user || !couponId) return false;
      
      const { data, error } = await supabase
        .from('premium_purchases')
        .select('id')
        .eq('buyer_id', user.id)
        .eq('coupon_id', couponId)
        .maybeSingle();
      
      if (error) {
        console.error('Error checking purchase:', error);
        return false;
      }
      
      return !!data;
    },
    enabled: !!user && !!couponId,
  });
  
  const purchaseCoupon = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to purchase premium coupons",
        variant: "destructive",
      });
      return;
    }
    
    if (hasPurchased) {
      toast({
        title: "Already purchased",
        description: "You already have access to this coupon",
      });
      return;
    }
    
    setLoading(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: { 
          couponId,
          successUrl: `${window.location.origin}/my-coupons?purchase=success`,
          cancelUrl: `${window.location.origin}/coupon/${couponId}?purchase=cancelled`
        },
      });
      
      if (error) throw error;
      
      if (data.alreadyPurchased) {
        toast({
          title: "Already purchased",
          description: "You already have access to this coupon",
        });
        return;
      }
      
      // Handle both possible URL formats
      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      } else if (data.checkoutUrl) {
        window.location.href = data.checkoutUrl;
      } else {
        throw new Error("No checkout URL returned");
      }
    } catch (error) {
      console.error('Error purchasing coupon:', error);
      toast({
        title: "Payment failed",
        description: "Could not process your payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return {
    purchaseCoupon,
    hasPurchased,
    isLoading: loading || checkingPurchase,
  };
};
