import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { DateRange } from 'react-day-picker';

// Define Supabase response types
interface CouponInteraction {
  interaction_type: 'view' | 'click' | 'use';
}

// This type reflects the actual structure returned by Supabase
interface SupabaseCoupon {
  id: string;
  name?: string;
  code?: string;
  expires_at?: string;
  interactions: CouponInteraction[];
}

// Define the dashboard data interface
export interface DashboardData {
  // Basic metrics
  totalCouponClicks: number;
  totalCouponViews: number;
  totalCouponUses: number;
  
  // Coupon performance
  couponPerformance: {
    id: string;
    name: string;
    views: number;
    clicks: number;
    uses: number;
    conversionRate: number;
    expiry?: string;
    code?: string;
  }[];
}

export const useDashboardData = (dateRange?: DateRange) => {
  return useQuery({
    queryKey: ['dashboardData', dateRange],
    queryFn: async (): Promise<DashboardData> => {
      try {
        // Prepare date filters if dateRange is provided
        const fromDate = dateRange?.from ? dateRange.from.toISOString() : undefined;
        const toDate = dateRange?.to ? dateRange.to.toISOString() : undefined;
        
        // Base query filters for date range
        const dateFilter = (query: any) => {
          if (fromDate) {
            query = query.gte('created_at', fromDate);
          }
          if (toDate) {
            query = query.lte('created_at', toDate);
          }
          return query;
        };
        
        // Get total coupon views
        const viewsQuery = supabase
          .from('coupon_interactions')
          .select('*', { count: 'exact', head: true })
          .eq('interaction_type', 'view');
        const { count: totalCouponViews, error: viewsError } = await dateFilter(viewsQuery);
        if (viewsError) throw viewsError;
        
        // Get total coupon clicks
        const clicksQuery = supabase
          .from('coupon_interactions')
          .select('*', { count: 'exact', head: true })
          .eq('interaction_type', 'click');
        const { count: totalCouponClicks, error: clicksError } = await dateFilter(clicksQuery);
        if (clicksError) throw clicksError;
        
        // Get total coupon uses
        const usesQuery = supabase
          .from('coupon_interactions')
          .select('*', { count: 'exact', head: true })
          .eq('interaction_type', 'use');
        const { count: totalCouponUses, error: usesError } = await dateFilter(usesQuery);
        if (usesError) throw usesError;
        
        // Get coupon performance data
        const { data: couponData, error: couponDataError } = await supabase
          .from('coupons')
          .select('id, name, code, expires_at, interactions:coupon_interactions(interaction_type)')
          .order('created_at', { ascending: false })
          .limit(10);
        if (couponDataError) throw couponDataError;
        
        // Process coupon data with proper type checking
        const couponPerformance = (couponData || []).map((coupon: any) => {
          const interactions = Array.isArray(coupon.interactions) ? coupon.interactions : [];
          const views = interactions.filter(i => i.interaction_type === 'view').length;
          const clicks = interactions.filter(i => i.interaction_type === 'click').length;
          const uses = interactions.filter(i => i.interaction_type === 'use').length;
          const conversionRate = views > 0 ? parseFloat(((uses / views) * 100).toFixed(1)) : 0;
          
          return {
            id: coupon.id || 'unknown',
            name: coupon.name || `Coupon ${(coupon.id || 'unknown').substring(0, 5)}`,
            code: coupon.code || 'SAVE20',
            expiry: coupon.expires_at,
            views,
            clicks,
            uses,
            conversionRate
          };
        });
        
        // Sort coupons by conversion rate to show best performing first
        couponPerformance.sort((a, b) => b.conversionRate - a.conversionRate);
        
        return {
          totalCouponViews: totalCouponViews || 0,
          totalCouponClicks: totalCouponClicks || 0,
          totalCouponUses: totalCouponUses || 0,
          couponPerformance,
        };
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        return {
          totalCouponViews: 0,
          totalCouponClicks: 0,
          totalCouponUses: 0,
          couponPerformance: [],
        };
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
