// vite-start.js
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

// Set environment variables for better debugging
process.env.ROLLUP_SKIP_NODEJS_NATIVE = 'true';
process.env.DEBUG = 'vite:*';

// Check if port 8080 is in use
function checkPort(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    server.once('error', () => {
      // Port is in use
      resolve(false);
    });
    server.once('listening', () => {
      // Port is available
      server.close();
      resolve(true);
    });
    server.listen(port);
  });
}

// Check if src/main.tsx exists
const mainPath = path.join(__dirname, 'src', 'main.tsx');
if (!fs.existsSync(mainPath)) {
  console.error('\x1b[31m%s\x1b[0m', '❌ Error: src/main.tsx file not found!');
  console.log('This file is required for the application to start.');
  process.exit(1);
}

console.log('\x1b[36m%s\x1b[0m', '🚀 Starting Vite development server...');
console.log('📁 Checking source files...');
console.log(`✅ Found main entry point: ${mainPath}`);

// Start Vite server with port handling
async function startViteServer() {
  // Port 8080 is the primary choice
  const port = 8080;
  const isPortAvailable = await checkPort(port);
  
  // Additional options for Vite
  const viteArgs = [
    path.join(__dirname, 'node_modules', 'vite', 'bin', 'vite.js'),
    '--host', 'localhost',
    '--clearScreen', 'false'
  ];
  
  // Force specific port if available, otherwise let Vite find an open port
  if (isPortAvailable) {
    viteArgs.push('--port', port.toString());
    console.log(`✅ Port ${port} is available. Using this port.`);
  } else {
    console.log(`⚠️ Port ${port} is in use. Vite will automatically find an open port.`);
    // When the port is unavailable, we need to update the base URL to match
    // This writes a temporary vite.config.ts to handle port redirection
    const configPath = path.join(__dirname, 'vite.config.ts');
    const configBackup = path.join(__dirname, 'vite.config.backup.ts');
    
    // Backup original config
    if (fs.existsSync(configPath)) {
      fs.copyFileSync(configPath, configBackup);
      console.log('✅ Original vite.config.ts backed up');
    }
  }

  // Start Vite with enhanced error handling
  const vite = spawn('node', viteArgs, {
    env: { ...process.env },
    stdio: 'inherit'
  });

  vite.on('error', (err) => {
    console.error('\x1b[31m%s\x1b[0m', 'Failed to start Vite:', err);
    
    if (err.code === 'ENOENT') {
      console.log('Make sure you have all dependencies installed:');
      console.log('\x1b[33m%s\x1b[0m', 'npm install');
    }
    
    process.exit(1);
  });

  process.on('SIGINT', () => {
    console.log('\x1b[36m%s\x1b[0m', '⏹️ Stopping Vite server...');
    vite.kill();
    
    // Restore config backup if exists
    if (fs.existsSync(configBackup)) {
      fs.copyFileSync(configBackup, configPath);
      fs.unlinkSync(configBackup);
      console.log('✅ Restored original vite.config.ts');
    }
    
    process.exit(0);
  });
}

// Start the server
startViteServer();
  