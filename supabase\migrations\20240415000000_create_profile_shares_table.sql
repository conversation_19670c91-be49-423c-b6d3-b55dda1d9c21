-- Create the profile_shares table that is currently missing
CREATE TABLE IF NOT EXISTS public.profile_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  sharer_ip TEXT,
  user_agent TEXT,
  platform TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_shares_user_id ON public.profile_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_profile_id ON public.profile_shares(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_created_at ON public.profile_shares(created_at);
CREATE INDEX IF NOT EXISTS idx_profile_shares_platform ON public.profile_shares(platform);

-- Enable Row Level Security
ALTER TABLE public.profile_shares ENABLE ROW LEVEL SECURITY;

-- Allow anonymous inserts to profile_shares (anonymous tracking)
CREATE POLICY IF NOT EXISTS "Allow anonymous inserts to profile_shares"
ON public.profile_shares
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY IF NOT EXISTS "Allow service role full access to profile_shares"
ON public.profile_shares
USING (true)
WITH CHECK (true);

-- Allow users to see their own shares and shares of their profile
CREATE POLICY IF NOT EXISTS "Allow users to see relevant profile shares"
ON public.profile_shares
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  (profile_id = auth.uid())
); 