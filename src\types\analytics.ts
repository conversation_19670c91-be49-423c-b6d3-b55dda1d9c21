import { ReactNode } from 'react';
import { JSX } from 'react';

export interface InteractionCount {
  interaction_type: string;
  count: number;
}

export interface RevenueData {
  id: string;
  amount: number;
  purchased_at: string;
  buyer_id: string;
  coupon_id: string;
}

export interface DailyInteraction {
  occurred_at: string;
  interaction_type: string;
  count: number;
}

export interface DailyRevenue {
  date: string;
  revenue: number;
}

export interface TrafficSources {
  social: number;
  direct: number;
  search: number;
}

export interface GrowthOpportunity {
  color: string;
  secondaryColor: string;
  icon: string;
  title: string;
  description: string;
}

export interface ProfileAnalytics {
  totalCouponViews: number;
  totalCouponClicks: number;
  totalCouponCopies: number;
  totalPremiumPurchases: number;
  totalRevenue: number;
  interactionCounts: {
    views: number;
    clicks: number;
    copies: number;
  };
  dailyInteractions: DailyInteraction[];
  dailyRevenue: DailyRevenue[];
  totalViews: number;
  couponClicks: number;
  couponCopies: number;
  premiumSales: number;
  previousPeriodRevenue: number;
  percentChange: number;
  viewsPercentChange: number;
  clicksPercentChange: number;
  copiesPercentChange: number;
  revenuePercentChange: number;
  trafficSources: TrafficSources;
  growthOpportunities: GrowthOpportunity[];
} 