// This script checks if the database is properly set up with the necessary tables and RLS policies
// Run it with: node scripts/check-db-setup.js

const { createClient } = require('@supabase/supabase-js');

// Get from environment or use the public key (safe to commit)
const SUPABASE_URL = process.env.SUPABASE_URL || "https://oewgwxxajssonxavydbx.supabase.co";
const SUPABASE_KEY = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!SUPABASE_KEY) {
  console.error('No Supabase key provided. Please set SUPABASE_SERVICE_KEY or SUPABASE_ANON_KEY environment variable.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkDbSetup() {
  console.log('Checking database setup...\n');
  
  // Check for tables
  try {
    console.log('Checking required tables...');
    const { data: tables, error } = await supabase.rpc('get_tables');
    
    if (error) {
      console.error('Error fetching tables:', error.message);
      console.log('\nTry running this SQL in your Supabase dashboard:');
      console.log(`
CREATE OR REPLACE FUNCTION get_tables()
RETURNS TABLE (
  table_name text
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT t.table_name::text
  FROM information_schema.tables t
  WHERE t.table_schema = 'public';
END;
$$;
      `);
      return;
    }
    
    const requiredTables = ['profiles', 'coupons', 'coupon_interactions', 'profile_shares', 'profile_views'];
    const missingTables = requiredTables.filter(table => !tables.some(t => t.table_name === table));
    
    if (missingTables.length === 0) {
      console.log('✓ All required tables exist');
    } else {
      console.error(`✗ Missing tables: ${missingTables.join(', ')}`);
      
      // Provide SQL to create missing tables
      if (missingTables.includes('profile_shares')) {
        console.log('\nRun this SQL to create the profile_shares table:');
        console.log(`
CREATE TABLE IF NOT EXISTS public.profile_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES profiles(id),
  user_id UUID REFERENCES auth.users(id),
  sharer_ip TEXT,
  user_agent TEXT,
  platform TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_profile_shares_user_id ON profile_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_profile_id ON profile_shares(profile_id);
        `);
      }
      
      if (missingTables.includes('profile_views')) {
        console.log('\nRun this SQL to create the profile_views table:');
        console.log(`
CREATE TABLE IF NOT EXISTS public.profile_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID NOT NULL REFERENCES profiles(id),
  viewer_id UUID REFERENCES auth.users(id),
  ip_address TEXT,
  user_agent TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_profile_views_viewer_id ON profile_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_profile_id ON profile_views(profile_id);
        `);
      }
      
      if (missingTables.includes('coupon_interactions')) {
        console.log('\nRun this SQL to create the coupon_interactions table:');
        console.log(`
CREATE TABLE IF NOT EXISTS public.coupon_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coupon_id UUID NOT NULL REFERENCES coupons(id),
  user_id UUID REFERENCES auth.users(id),
  interaction_type TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  occurred_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_user_id ON coupon_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_coupon_id ON coupon_interactions(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_type ON coupon_interactions(interaction_type);
        `);
      }
    }
    
    // Check for RLS policies
    console.log('\nChecking RLS policies...');
    
    for (const table of ['coupon_interactions', 'profile_shares', 'profile_views']) {
      if (missingTables.includes(table)) continue;
      
      const { data: policies, error } = await supabase.rpc('get_policies_for_table', { table_name: table });
      
      if (error) {
        console.error(`Error fetching policies for ${table}:`, error.message);
        continue;
      }
      
      if (!policies || policies.length === 0) {
        console.error(`✗ No RLS policies found for ${table}`);
        
        // Provide SQL to create policies
        if (table === 'coupon_interactions') {
          console.log('\nRun this SQL to create the RLS policies for coupon_interactions:');
          console.log(`
-- Enable Row Level Security on coupon_interactions table
ALTER TABLE IF EXISTS public.coupon_interactions ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into coupon_interactions (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to coupon_interactions"
ON public.coupon_interactions
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to coupon_interactions"
ON public.coupon_interactions
USING (true)
WITH CHECK (true);

-- Allow users to see their own interactions and interactions with their coupons
CREATE POLICY "Allow users to see relevant coupon interactions"
ON public.coupon_interactions
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  EXISTS (
    SELECT 1 FROM coupons
    WHERE coupons.id = coupon_interactions.coupon_id
    AND coupons.influencer_id = auth.uid()
  )
);
          `);
        } else if (table === 'profile_shares') {
          console.log('\nRun this SQL to create the RLS policies for profile_shares:');
          console.log(`
-- Enable Row Level Security on profile_shares table
ALTER TABLE IF EXISTS public.profile_shares ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into profile_shares (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to profile_shares"
ON public.profile_shares
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to profile_shares"
ON public.profile_shares
USING (true)
WITH CHECK (true);

-- Allow users to see their own shares and shares of their profile
CREATE POLICY "Allow users to see relevant profile shares"
ON public.profile_shares
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  (profile_id = auth.uid())
);
          `);
        } else if (table === 'profile_views') {
          console.log('\nRun this SQL to create the RLS policies for profile_views:');
          console.log(`
-- Enable Row Level Security on profile_views table
ALTER TABLE IF EXISTS public.profile_views ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into profile_views (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to profile_views"
ON public.profile_views
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to profile_views"
ON public.profile_views
USING (true)
WITH CHECK (true);

-- Allow users to see views of their profile
CREATE POLICY "Allow users to see views of their profile"
ON public.profile_views
FOR SELECT
TO authenticated
USING (
  (viewer_id = auth.uid()) OR
  (profile_id = auth.uid())
);
          `);
        }
      } else {
        const policyNames = policies.map(p => p.policy_name);
        console.log(`✓ Found ${policies.length} policies for ${table}: ${policyNames.join(', ')}`);
      }
    }
    
    console.log('\nDatabase check complete.');
  } catch (error) {
    console.error('Error checking database setup:', error.message);
  }
}

// Function to add the get_policies_for_table function if needed
async function addHelperFunction() {
  console.log('Adding helper function to get policies...');
  
  const { error } = await supabase.rpc('get_policies_for_table', { table_name: 'coupons' });
  
  if (error && error.message.includes('function get_policies_for_table() does not exist')) {
    console.log('Helper function missing, attempting to create it...');
    
    const { error: createError } = await supabase.rpc('create_helper_function');
    
    if (createError) {
      console.error('Error creating helper function:', createError.message);
      console.log('\nTry running this SQL in your Supabase dashboard:');
      console.log(`
CREATE OR REPLACE FUNCTION get_policies_for_table(table_name text)
RETURNS TABLE (
  policy_name text,
  command text,
  roles text[]
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT p.policyname::text, p.cmd::text, p.roles::text[]
  FROM pg_policies p
  WHERE p.tablename = table_name AND p.schemaname = 'public';
END;
$$;

CREATE OR REPLACE FUNCTION create_helper_function()
RETURNS text
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  RETURN 'Function created';
END;
$$;
      `);
    } else {
      console.log('Helper function created successfully.');
    }
  }
}

// Run the checks
(async () => {
  await addHelperFunction();
  await checkDbSetup();
})(); 