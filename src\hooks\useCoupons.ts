import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Coupon } from '@/types/coupon';
import { searchCoupons } from '@/utils/searchUtils';

// Optimized function that fetches coupons with pagination and selective loading
export const useCoupons = (options?: {
  limit?: number;
  offset?: number;
  includeRelations?: boolean;
}) => {
  const { limit = 50, offset = 0, includeRelations = true } = options || {};

  return useQuery({
    queryKey: ['all-coupons', limit, offset, includeRelations],
    queryFn: async (): Promise<Coupon[]> => {
      if (!includeRelations) {
        const { data, error } = await supabase
          .from('coupons')
          .select(`
            id, title, code, discount_percent, discount_description,
            expires_at, status, featured, is_premium, view_count, created_at
          `)
          .eq('status', 'active')
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1);

        if (error) throw error;
        return (data || []).map(item => ({...item, active: item.status === 'active'}));
      }

      // Get coupons first
      const { data: couponData, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      if (!couponData) return [];

      // Get related data for each coupon
      const couponsWithRelations = await Promise.all(
        couponData.map(async (coupon) => {
          const [brandData, influencerData, categoryData] = await Promise.all([
            coupon.brand_id ? supabase
              .from('brands')
              .select('id, name, logo_url, website')
              .eq('id', coupon.brand_id)
              .single()
              .then(({ data }) => data)
              .catch(() => null) : null,

            coupon.influencer_id ? supabase
              .from('profiles')
              .select('id, full_name, username, avatar_url')
              .eq('id', coupon.influencer_id)
              .single()
              .then(({ data }) => data)
              .catch(() => null) : null,

            coupon.category_id ? supabase
              .from('categories')
              .select('id, name')
              .eq('id', coupon.category_id)
              .single()
              .then(({ data }) => data)
              .catch(() => null) : null
          ]);

          return {
            ...coupon,
            active: coupon.status === 'active',
            brand: brandData || undefined,
            influencer: influencerData || undefined,
            category: categoryData || undefined
          } as any;
        })
      );

      return couponsWithRelations;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    refetchOnWindowFocus: false,
  });
};

// Optimized trending coupons with better caching and selective loading
export const useTrendingCoupons = (limit = 12) => {
  return useQuery({
    queryKey: ['trending-coupons', limit],
    queryFn: async (): Promise<Coupon[]> => {
      const { data, error } = await supabase
        .from('coupons')
        .select(`
          id, title, code, discount_percent, discount_description,
          expires_at, status, featured, is_premium, view_count, created_at,
          brand:brands(id, name, logo_url, website),
          influencer:profiles(id, full_name, username, avatar_url),
          category:categories(id, name)
        `)
        .eq('status', 'active')
        .order('view_count', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // Cast data to Coupon[] type with 'active' field
      return (data || []).map(item => ({...item, active: item.status === 'active'}));
    },
    staleTime: 1000 * 60 * 10, // 10 minutes for trending (changes less frequently)
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: true, // Enable to ensure data loads
  });
};

// Optimized influencer coupons with selective loading
export const useInfluencerCoupons = (influencerId: string, options?: { limit?: number }) => {
  const { limit = 50 } = options || {};

  return useQuery({
    queryKey: ['influencer-coupons', influencerId, limit],
    queryFn: async (): Promise<Coupon[]> => {
      if (!influencerId) return [];

      const { data, error } = await supabase
        .from('coupons')
        .select(`
          id, title, code, discount_percent, discount_description,
          expires_at, status, featured, is_premium, view_count, created_at,
          brand:brands(id, name, logo_url, website),
          category:categories(id, name)
        `)
        .eq('influencer_id', influencerId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // Cast data to Coupon[] type with 'active' field
      return (data || []).map(item => ({...item, active: item.status === 'active'}));
    },
    enabled: !!influencerId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    refetchOnWindowFocus: false,
  });
};

// Optimized premium coupons with selective loading and better caching
export const usePremiumCoupons = (limit = 2) => {
  return useQuery({
    queryKey: ['premium-coupons', limit],
    queryFn: async (): Promise<Coupon[]> => {
      const { data, error } = await supabase
        .from('coupons')
        .select(`
          id, title, code, discount_percent, discount_description,
          expires_at, status, featured, is_premium, view_count, created_at,
          brand:brands(id, name, logo_url, website),
          influencer:profiles(id, full_name, username, avatar_url),
          category:categories(id, name)
        `)
        .eq('status', 'active')
        .eq('is_premium', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // Cast data to Coupon[] type with 'active' field
      return (data || []).map(item => ({...item, active: item.status === 'active'}));
    },
    staleTime: 1000 * 60 * 15, // 15 minutes for premium (changes less frequently)
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
  });
};

// Fetch user's saved coupons
export const useSavedCoupons = (userId?: string) => {
  return useQuery({
    queryKey: ['saved-coupons', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const { data, error } = await supabase
        .from('saved_coupons')
        .select(`
          coupon_id,
          coupon:coupons(
            *,
            brand:brands(*),
            influencer:profiles(id, full_name, username, avatar_url),
            category:categories(*)
          )
        `)
        .eq('user_id', userId);
      
      if (error) {
        throw error;
      }
      
      // Extract coupon data from the response
      return data?.map(item => item.coupon) || [];
    },
    enabled: !!userId,
  });
};



// Optimized search with database-level filtering and pagination
export const useSearchCoupons = (searchQuery: string, options?: { limit?: number }) => {
  const { limit = 50 } = options || {};

  return useQuery({
    queryKey: ['search-coupons', searchQuery, limit],
    queryFn: async (): Promise<Coupon[]> => {
      if (!searchQuery.trim()) return [];

      // Use database text search for better performance
      const { data, error } = await supabase
        .from('coupons')
        .select(`
          id, title, code, discount_percent, discount_description,
          expires_at, status, featured, is_premium, view_count, created_at,
          brand:brands(id, name, logo_url, website),
          influencer:profiles(id, full_name, username, avatar_url),
          category:categories(id, name)
        `)
        .eq('status', 'active')
        .or(`title.ilike.%${searchQuery}%,code.ilike.%${searchQuery}%,discount_description.ilike.%${searchQuery}%`)
        .order('view_count', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      // Cast data to Coupon[] type
      return (data || []).map(item => ({...item, active: item.status === 'active'}));
    },
    enabled: !!searchQuery.trim(),
    staleTime: 1000 * 60 * 2, // 2 minutes for search results
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
  });
};

// Export all hooks
export default {
  useTrendingCoupons,
  useInfluencerCoupons,
  usePremiumCoupons,
  useSavedCoupons,
  useCoupons,
  useSearchCoupons
};

// Re-export the Coupon type
export type { Coupon } from '@/types/coupon';
