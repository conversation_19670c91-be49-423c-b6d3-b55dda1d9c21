import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';

export const useRecentlyViewed = (userId?: string) => {
  // Don't attempt to fetch if no userId is provided
  const enabled = !!userId;
  
  return useQuery({
    queryKey: ['recentlyViewed', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      try {
        // Get recently viewed coupons for this user
        const { data, error } = await supabase
          .from('recently_viewed_coupons')
          .select(`
            id,
            coupon_id,
            viewed_at,
            coupon:coupons (
              id,
              code,
              discount_description,
              discount_percent,
              expires_at,
              created_at,
              updated_at,
              featured,
              is_premium,
              average_rating,
              brand:brands (
                id,
                name,
                logo_url
              ),
              influencer:profiles (
                id,
                full_name,
                avatar_url
              ),
              category:categories (
                id,
                name
              )
            )
          `)
          .eq('user_id', userId)
          .order('viewed_at', { ascending: false })
          .limit(5);
        
        if (error) {
          console.error('Error fetching recently viewed coupons:', error);
          return [];
        }
        
        // Format data to match the expected coupon structure
        return data.map(item => ({
          id: item.coupon.id,
          code: item.coupon.code,
          discount_description: item.coupon.discount_description,
          discount_percent: item.coupon.discount_percent,
          expires_at: item.coupon.expires_at,
          created_at: item.coupon.created_at,
          updated_at: item.coupon.updated_at,
          featured: item.coupon.featured,
          is_premium: item.coupon.is_premium,
          average_rating: item.coupon.average_rating,
          brand: item.coupon.brand,
          influencer: item.coupon.influencer,
          category: item.coupon.category,
          viewed_at: item.viewed_at
        }));
      } catch (error) {
        console.error('Error in useRecentlyViewed hook:', error);
        return [];
      }
    },
    enabled,
    staleTime: 1000 * 60 * 5 // 5 minutes
  });
};

// Helper function to track when a coupon is viewed
export const trackCouponView = async (userId: string, couponId: string) => {
  if (!userId || !couponId) return;
  
  try {
    // First, check if this coupon is already in recently_viewed
    const { data: existing } = await supabase
      .from('recently_viewed_coupons')
      .select('id')
      .eq('user_id', userId)
      .eq('coupon_id', couponId)
      .single();
    
    if (existing) {
      // Update the viewed_at timestamp
      await supabase
        .from('recently_viewed_coupons')
        .update({ viewed_at: new Date().toISOString() })
        .eq('id', existing.id);
    } else {
      // Insert a new record
      await supabase
        .from('recently_viewed_coupons')
        .insert({
          user_id: userId,
          coupon_id: couponId,
          viewed_at: new Date().toISOString()
        });
    }
  } catch (error) {
    console.error('Error tracking coupon view:', error);
  }
}; 