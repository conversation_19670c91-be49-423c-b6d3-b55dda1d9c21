import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Coupon } from '@/hooks/useCoupons';
import CouponCard from '@/components/CouponCard';
import { Skeleton } from '@/components/ui/skeleton';
import PremiumCouponButton from '@/components/PremiumCouponButton';
import { usePremiumCoupon } from '@/hooks/usePremiumCoupon';
import { useAuth } from '@/context/AuthContext';
import { Crown, Star, CheckCircle, ShieldCheck, Eye } from 'lucide-react';
import BackButton from '@/components/BackButton';

const CouponDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  
  const { data: coupon, isLoading, error } = useQuery({
    queryKey: ['coupon', id],
    queryFn: async (): Promise<Coupon | null> => {
      if (!id) return null;
      
      // First get the coupon data
      const { data: couponData, error: couponError } = await supabase
        .from('coupons')
        .select('*')
        .eq('id', id)
        .single();

      if (couponError) throw couponError;

      // Then get related data separately to avoid relation errors
      const getBrandData = async () => {
        if (!couponData.brand_id) return null;
        try {
          const { data } = await supabase
            .from('brands')
            .select('id, name, logo_url, website')
            .eq('id', couponData.brand_id)
            .single();
          return data;
        } catch {
          return null;
        }
      };

      const getInfluencerData = async () => {
        if (!couponData.influencer_id) return null;
        try {
          const { data } = await supabase
            .from('profiles')
            .select('id, full_name, username, avatar_url')
            .eq('id', couponData.influencer_id)
            .single();
          return data;
        } catch {
          return null;
        }
      };

      const getCategoryData = async () => {
        if (!couponData.category_id) return null;
        try {
          const { data } = await supabase
            .from('categories')
            .select('id, name')
            .eq('id', couponData.category_id)
            .single();
          return data;
        } catch {
          return null;
        }
      };

      const [brandData, influencerData, categoryData] = await Promise.all([
        getBrandData(),
        getInfluencerData(),
        getCategoryData()
      ]);

      // Transform the data to match Coupon type
      return {
        ...couponData,
        active: couponData.status === 'active',
        brand: brandData || undefined,
        influencer: influencerData || undefined,
        category: categoryData || undefined
      } as Coupon;
    },
    enabled: !!id,
  });
  
  // Get premium coupon purchase status
  const { hasPurchased } = usePremiumCoupon(id || '');
  
  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto py-12 px-4">
          <div className="mb-6">
            <BackButton />
          </div>
          <div className="max-w-md mx-auto">
            <Skeleton className="h-[400px] w-full rounded-lg" />
          </div>
        </div>
      </MainLayout>
    );
  }
  
  if (error || !coupon) {
    return (
      <MainLayout>
        <div className="container mx-auto py-12 px-4">
          <div className="mb-6">
            <BackButton />
          </div>
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-2">Coupon Not Found</h3>
            <p className="text-gray-600">The coupon you're looking for doesn't exist or has been removed.</p>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  // Format the price for premium coupons
  const formattedPrice = coupon.price ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(coupon.price) : null;
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Coupon Details"
          subtitle="View detailed information about this coupon code"
          icon={Eye}
        />

        <div className="max-w-3xl mx-auto">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Coupon card column */}
            <div className="md:w-1/2">
          <CouponCard
            id={coupon.id}
            brandName={coupon.brand?.name || "Unknown Brand"}
            brandLogo={coupon.brand?.logo_url || "/placeholder.svg"}
            brandWebsite={coupon.brand?.website}
            influencerName={coupon.influencer?.full_name || ""}
            influencerImage={coupon.influencer?.avatar_url || undefined}
            discountAmount={coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                expirationTime={coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                }) : "No expiration"}
            couponCode={coupon.code}
            category={coupon.category?.name || "Uncategorized"}
            featured={coupon.featured}
            isPremium={coupon.is_premium}
                isLocked={coupon.is_premium && !hasPurchased}
                price={coupon.price || 0}
              />
              
              {/* Add purchase button for premium coupons or auth prompt */}
              {coupon.is_premium && (
                <div className="mt-6">
                  {user ? (
                    <PremiumCouponButton coupon={coupon} />
                  ) : (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="text-center">
                        <h3 className="text-sm font-medium text-blue-900 mb-2">Premium Coupon Access</h3>
                        <p className="text-sm text-blue-700 mb-3">Sign in to purchase and access this premium coupon code.</p>
                        <Link to="/auth" className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors">
                          Sign In to Purchase
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {/* Coupon details column */}
            <div className="md:w-1/2">
              {coupon.is_premium && (
                <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-6 rounded-xl border border-amber-200 mb-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Crown className="w-6 h-6 text-amber-500" />
                    <h2 className="text-xl font-bold text-amber-800">Premium Coupon Details</h2>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Star className="w-5 h-5 text-amber-500 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-amber-900">Deal Value</h3>
                        <p className="text-amber-700 text-sm">{coupon.discount_description || `${coupon.discount_percent || 0}% OFF`} at {coupon.brand?.name}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-amber-500 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-amber-900">Coupon Price</h3>
                        <p className="text-amber-700 text-sm">{formattedPrice || 'Premium'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <ShieldCheck className="w-5 h-5 text-amber-500 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-amber-900">Valid Until</h3>
                        <p className="text-amber-700 text-sm">
                          {coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'No expiration date'}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 pt-4 border-t border-amber-200">
                    <h3 className="font-medium text-amber-900 mb-2">Why Purchase Premium Coupons?</h3>
                    <ul className="text-sm text-amber-700 space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" />
                        <span>Exclusive deals with higher discounts than regular coupons</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" />
                        <span>Support your favorite content creators</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" />
                        <span>Verified codes that are guaranteed to work</span>
                      </li>
                    </ul>
                  </div>
                </div>
              )}
              
              {/* General coupon information */}
              <div className="bg-white p-6 rounded-xl border border-gray-200">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Coupon Information</h2>
                
                <div className="space-y-4">
                  <div className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="text-gray-600">Brand</span>
                    <span className="font-medium">{coupon.brand?.name || "Unknown Brand"}</span>
                  </div>
                  
                  <div className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="text-gray-600">Category</span>
                    <span className="font-medium">{coupon.category?.name || "Uncategorized"}</span>
                  </div>
                  
                  <div className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="text-gray-600">Discount</span>
                    <span className="font-medium text-emerald-600">
                      {coupon.discount_description || `${coupon.discount_percent || 0}% OFF`}
                    </span>
                  </div>
                  
                  <div className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="text-gray-600">Valid Until</span>
                    <span className="font-medium">
                      {coupon.expires_at ? new Date(coupon.expires_at).toLocaleDateString() : "No expiration"}
                    </span>
                  </div>
                  
                  <div className="flex justify-between pb-2">
                    <span className="text-gray-600">Created By</span>
                    <span className="font-medium">{coupon.influencer?.full_name || "Anonymous"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
};

export default CouponDetail;
