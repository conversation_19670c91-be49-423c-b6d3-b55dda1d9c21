import React from 'react';
import { Helmet } from 'react-helmet-async';

interface CouponSchemaProps {
  couponName: string;
  description: string;
  brandName: string;
  discountAmount: string;
  couponCode: string;
  validFrom: string;
  validThrough: string;
}

/**
 * Component that adds Schema.org coupon/offer structured data to a page
 * This helps search engines understand coupon information and can lead to rich snippets
 */
const CouponSchema: React.FC<CouponSchemaProps> = ({
  couponName,
  description,
  brandName,
  discountAmount,
  couponCode,
  validFrom,
  validThrough
}) => {
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "Offer",
    "name": couponName,
    "description": description,
    "price": "0",
    "priceCurrency": "USD",
    "seller": {
      "@type": "Organization",
      "name": brandName
    },
    "discount": discountAmount,
    "couponCode": couponCode,
    "validFrom": validFrom,
    "validThrough": validThrough
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schemaData)}
      </script>
    </Helmet>
  );
};

export default CouponSchema; 