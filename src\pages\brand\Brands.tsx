import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useBrands } from '@/hooks/useBrands';
import BrandCard from '@/components/BrandCard';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Building2, ShoppingBag, Plus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';

const Brands = () => {
  const { data: brands, isLoading } = useBrands();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // Check for name parameter in URL which would indicate a brand search
  useEffect(() => {
    const brandNameParam = searchParams.get('name');
    
    if (brandNameParam && brands?.length) {
      // Find brand with matching name (case insensitive)
      const matchedBrand = brands.find(
        brand => brand.name.toLowerCase() === brandNameParam.toLowerCase()
      );
      
      if (matchedBrand) {
        // Navigate to the specific brand page
        navigate(`/brands/${matchedBrand.id}`, { replace: true });
      } else {
        // If we don't find an exact match, set the search query and show a message
        setSearchQuery(brandNameParam);
        toast.info(`Showing results for "${brandNameParam}"`);
      }
    }
  }, [searchParams, brands, navigate]);
  
  // Filter brands based on search query
  const filteredBrands = brands?.filter(brand => 
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];
  
  // Sort brands based on selected sorting option
  const sortedBrands = [...filteredBrands].sort((a, b) => {
    if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    } else if (sortBy === 'coupons') {
      return (b.coupons_count || 0) - (a.coupons_count || 0);
    }
    return 0;
  });
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Brands"
          subtitle="Discover brands and their exclusive coupon deals"
          icon={Building2}
        />
        
        <div className="flex flex-wrap gap-3 mb-8 mt-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search brands..."
              className="pl-9 min-w-[200px]"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">A-Z</SelectItem>
              <SelectItem value="coupons">Most Coupons</SelectItem>
            </SelectContent>
          </Select>
          
          <Link to="/brands/create">
            <Button className="bg-blue-500 hover:bg-blue-600 flex items-center gap-1">
              <Plus className="h-4 w-4" />
              Create Brand
            </Button>
          </Link>
        </div>
          
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <Skeleton className="h-16 w-16 rounded-lg" />
                  <Skeleton className="h-5 w-20 rounded-full" />
                </div>
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-4 w-full mt-6" />
              </div>
            ))}
          </div>
        ) : sortedBrands.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {sortedBrands.map(brand => (
              <BrandCard key={brand.id} brand={brand} />
            ))}
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center shadow-sm">
            <ShoppingBag className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">No brands found</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {searchQuery
                ? `No brands matching "${searchQuery}"`
                : "No brands available yet. Create your first brand to get started!"}
            </p>
            {searchQuery ? (
              <Button
                variant="outline"
                onClick={() => setSearchQuery('')}
                className="mr-2"
              >
                Clear search
              </Button>
            ) : null}
            <Link to="/brands/create">
              <Button className="bg-blue-500 hover:bg-blue-600">
                <Plus className="h-4 w-4 mr-1" />
                Create Brand
              </Button>
            </Link>
          </div>
        )}
      </PageContainer>
    </MainLayout>
  );
};

export default Brands; 