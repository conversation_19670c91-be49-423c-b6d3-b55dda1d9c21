import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader, User, Lock, Mail, UserPlus, AlertCircle, Check, X, Eye, EyeOff } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import BackButton from '@/components/BackButton';
import { supabase } from '@/integrations/supabase/client';


const Auth = () => {
  const { signIn, signUp, user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [showUsernameClaimed, setShowUsernameClaimed] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);
  const [resetEmail, setResetEmail] = useState('');
  const [resetSent, setResetSent] = useState(false);
  
  // Redirect if already logged in
  useEffect(() => {
    if (user && !loading && location.pathname === '/auth') {
      console.log('Auth page: User is logged in, redirecting to home...');
      navigate('/home');
    }
  }, [user, loading, navigate, location.pathname]);
  
  // Get mode from URL query params
  const queryParams = new URLSearchParams(location.search);
  const initialMode = queryParams.get('mode') === 'signup' ? 'signup' : 
                     queryParams.get('mode') === 'reset_password' ? 'reset_password' :
                     'signin';
  const usernameFromUrl = queryParams.get('username');
  
  // Show username claimed message if username is in URL
  useEffect(() => {
    if (usernameFromUrl && initialMode === 'signup') {
      setShowUsernameClaimed(true);
      // Auto-hide the message after 5 seconds
      const timer = setTimeout(() => {
        setShowUsernameClaimed(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [usernameFromUrl, initialMode]);

  // Check username from URL when component loads
  useEffect(() => {
    if (usernameFromUrl && usernameFromUrl.trim().length > 2) {
      // Check the username availability immediately
      checkUsername(usernameFromUrl.trim());
    }
  }, [usernameFromUrl]);

  const [authState, setAuthState] = useState<'signin' | 'signup' | 'username_check' | 'forgot_password'>(
    usernameFromUrl ? 'username_check' : initialMode === 'reset_password' ? 'forgot_password' : initialMode
  );

  // Username check state
  const [username, setUsername] = useState(usernameFromUrl || '');
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameCheckTimeout, setUsernameCheckTimeout] = useState<NodeJS.Timeout | null>(null);

  // Cleanup username check timeout on unmount
  useEffect(() => {
    return () => {
      if (usernameCheckTimeout) {
        clearTimeout(usernameCheckTimeout);
      }
    };
  }, [usernameCheckTimeout]);
  
  // Sign In form state
  const [signInEmail, setSignInEmail] = useState('');
  const [signInPassword, setSignInPassword] = useState('');
  
  // Sign Up form state
  const [signUpEmail, setSignUpEmail] = useState('');
  const [signUpPassword, setSignUpPassword] = useState('');
  const [signUpConfirmPassword, setSignUpConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');

  // Password visibility state
  const [showSignInPassword, setShowSignInPassword] = useState(false);
  const [showSignUpPassword, setShowSignUpPassword] = useState(false);
  const [showSignUpConfirmPassword, setShowSignUpConfirmPassword] = useState(false);
  
  // Check username availability
  const checkUsername = async (username: string) => {
    if (!username.trim()) {
      setUsernameAvailable(null);
      return;
    }

    setIsCheckingUsername(true);
    try {
      // Use count method for better performance and consistency
      const { count, error } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .ilike('username', username.trim());

      if (error) {
        console.error('Error checking username:', error);
        toast.error('Failed to check username availability');
        setUsernameAvailable(null);
        return;
      }

      // Username is available if count is 0
      const available = count === 0;
      setUsernameAvailable(available);
    } catch (err) {
      console.error('Error checking username:', err);
      toast.error('Failed to check username availability');
      setUsernameAvailable(null);
    } finally {
      setIsCheckingUsername(false);
    }
  };

  // Handle username changes with debounced checking
  const handleUsernameChange = (newUsername: string) => {
    setUsername(newUsername);
    setUsernameAvailable(null); // Reset availability status

    // Clear existing timeout
    if (usernameCheckTimeout) {
      clearTimeout(usernameCheckTimeout);
    }

    // Only check if username is not empty and has more than 2 characters
    if (newUsername.trim().length > 2) {
      const timeout = setTimeout(() => {
        checkUsername(newUsername.trim());
      }, 300); // Reduced delay for faster response
      setUsernameCheckTimeout(timeout);
    } else if (newUsername.trim().length === 0) {
      // Clear availability status for empty username
      setUsernameAvailable(null);
    }
  };

  // Handle username check form submission
  const handleUsernameCheck = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim()) {
      toast.error('Please enter a username');
      return;
    }

    if (username.trim().length < 3) {
      toast.error('Username must be at least 3 characters long');
      return;
    }

    await checkUsername(username.trim());
    if (usernameAvailable) {
      setAuthState('signup');
      // Pre-fill the username in signup form
      setSignUpEmail('');
      setFullName('');
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    const { user, error } = await signIn(signInEmail, signInPassword);
    
    if (error) {
      toast.error(error.message || 'Failed to sign in');
      setIsLoading(false);
    } else if (user) {
      toast.success('Signed in successfully');
      navigate('/home');
      setIsLoading(false);
    }
  };
  
  const [lastSignUpAttempt, setLastSignUpAttempt] = useState<number>(0);
  
  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if enough time has passed since last attempt (60 seconds cooldown)
    const now = Date.now();
    const timeSinceLastAttempt = now - lastSignUpAttempt;
    if (timeSinceLastAttempt < 60000) {
      const remainingSeconds = Math.ceil((60000 - timeSinceLastAttempt) / 1000);
      toast.error(`Please wait ${remainingSeconds} seconds before trying again`);
      return;
    }

    setIsLoading(true);
    setLastSignUpAttempt(now);

    if (signUpPassword !== signUpConfirmPassword) {
      toast.error('Passwords do not match');
      setIsLoading(false);
      return;
    }

    // Check if username is available
    if (usernameAvailable === false) {
      toast.error('Please choose a different username');
      setIsLoading(false);
      return;
    }

    // If username hasn't been checked yet, check it now
    if (usernameAvailable === null && username.trim()) {
      await checkUsername(username.trim());
      // Wait a moment for the state to update
      await new Promise(resolve => setTimeout(resolve, 100));
      if (usernameAvailable === false) {
        toast.error('Username is not available');
        setIsLoading(false);
        return;
      }
    }

    try {
      // Set flag to indicate signup is in progress
      sessionStorage.setItem('signUpInProgress', 'true');

      const { user, error } = await signUp(signUpEmail, signUpPassword, {
        full_name: fullName,
        username: username,
        is_new_user: true
      });

      if (error) {
        if (error.message.includes('rate limit')) {
          toast.error('Please wait a minute before trying again');
        } else {
          toast.error(error.message || 'Failed to create account');
        }
        setIsLoading(false);
        // Clear the signup flag on error
        sessionStorage.removeItem('signUpInProgress');
      } else if (user) {
        // Check if email confirmation is required
        if (!user.email_confirmed_at) {
          toast.success('Account created! Please check your email to confirm your account.');
        } else {
          toast.success('Account created! Welcome to CouponLink.');
          // The onboarding step is already set during profile creation in AuthContext
          // Navigate to home immediately - onboarding context will handle the timing
          navigate('/home');
        }

        // Clear the signup flag after successful completion
        sessionStorage.removeItem('signUpInProgress');
        setIsLoading(false);
      }
    } catch (err) {
      toast.error('An error occurred during sign up');
      setIsLoading(false);
      // Clear the signup flag on error
      sessionStorage.removeItem('signUpInProgress');
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/auth?mode=reset_password`,
      });

      if (error) {
        toast.error(error.message);
      } else {
        setResetSent(true);
        toast.success('Password reset instructions sent to your email');
      }
    } catch (error: any) {
      toast.error('Failed to send reset instructions');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Username claimed notification */}
      {showUsernameClaimed && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full">
          <Alert className="bg-green-50 border-green-200 shadow-md animate-fadeIn">
            <AlertCircle className="h-5 w-5 text-green-600" />
            <AlertTitle className="text-green-800">Username claimed!</AlertTitle>
            <AlertDescription className="text-green-700">
              Great choice! Complete the sign-up process to secure "{usernameFromUrl}" as your username.
            </AlertDescription>
          </Alert>
        </div>
      )}
      
      {/* Left side - Branding and Info with gradient background */}
      <div className="w-full md:w-5/12 bg-gradient-to-br from-primary-dark via-tertiary-dark to-accent-dark text-white relative overflow-hidden">
        {/* Decorative elements with brand colors */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-primary-bgLight rounded-full opacity-20"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-secondary-bgLight rounded-full opacity-20"></div>
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-accent-bgLight rounded-full opacity-20"></div>
        
        {/* Content container */}
        <div className="relative h-full flex flex-col p-8 md:p-12 justify-between z-10">
          {/* Back button */}
          <div className="mb-6">
            <BackButton customAction={() => navigate('/')} />
          </div>
          
          {/* Main content */}
          <div className="flex-grow flex items-center">
            <div>
              <h1 className="text-4xl font-bold mb-6">CouponLink</h1>
              <p className="text-xl mb-8 text-white/80">
                {authState === 'signin' 
                  ? 'Welcome back! Sign in to access your dashboard and manage your coupon campaigns.' 
                  : 'Join our community of influencers and brands to create and share exclusive coupon codes.'}
              </p>
              <div className="flex flex-wrap gap-3">
                <div className="bg-primary-main/20 backdrop-blur-sm py-2 px-4 rounded-lg text-sm text-white">
                  Seamless Sharing
                </div>
                <div className="bg-secondary-main/20 backdrop-blur-sm py-2 px-4 rounded-lg text-sm text-white">
                  Campaign Analytics
                </div>
                <div className="bg-accent-main/20 backdrop-blur-sm py-2 px-4 rounded-lg text-sm text-white">
                  Verified Brands
                </div>
                <div className="bg-tertiary-main/20 backdrop-blur-sm py-2 px-4 rounded-lg text-sm text-white">
                  Real-time Performance
                </div>
              </div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="mt-auto pt-8">
            <p className="text-white/70 text-sm">© 2024 CouponLink. All rights reserved.</p>
          </div>
        </div>
      </div>
      
      {/* Right side - Auth Form */}
      <div className="w-full md:w-7/12 bg-white">
        <div className="max-w-md mx-auto h-full flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            {/* Use different brand color backgrounds based on auth state */}
            <div className={`inline-block mb-4 p-3 rounded-full ${authState === 'signin' ? 'bg-primary-bgLight' : 'bg-accent-bgLight'}`}>
              {authState === 'signin' ? (
                <User className="w-7 h-7 text-primary-main" />
              ) : (
                <UserPlus className="w-7 h-7 text-accent-main" />
              )}
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {authState === 'signin' ? 'Sign in to your account' : 'Create a new account'}
            </h2>
            <p className="text-gray-600">
              {authState === 'signin' 
                  ? 'Enter your details below' 
                  : 'Fill out the form to get started'}
            </p>
            
            {/* Username confirmation message when signing up with claimed username */}
            {usernameFromUrl && authState === 'signup' && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100 text-left">
                <div className="flex items-start">
                  <User className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">Username reserved: <span className="font-bold">{usernameFromUrl}</span></p>
                    <p className="text-xs text-blue-700 mt-1">Complete sign-up to claim this username permanently.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        
          <div className="bg-white rounded-lg p-6 border border-gray-100 shadow-sm">
            <Tabs value={authState} onValueChange={(value: any) => setAuthState(value)}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="signin">Sign In</TabsTrigger>
                <TabsTrigger value="signup">Sign Up</TabsTrigger>
              </TabsList>

              {/* Username Check Content */}
              {authState === 'username_check' && (
                <div className="space-y-4 mt-4">
                  <h2 className="text-2xl font-bold text-center">Check Username Availability</h2>
                  <form onSubmit={handleUsernameCheck} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Choose your username</Label>
                      <div className="relative">
                        <Input
                          id="username"
                          type="text"
                          value={username}
                          onChange={(e) => handleUsernameChange(e.target.value)}
                          placeholder="Enter desired username"
                          className="pr-10"
                          disabled={isCheckingUsername}
                        />
                        {isCheckingUsername && (
                          <Loader className="absolute right-3 top-3 h-4 w-4 animate-spin" />
                        )}
                        {!isCheckingUsername && usernameAvailable !== null && (
                          <div className="absolute right-3 top-3">
                            {usernameAvailable ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isCheckingUsername || usernameAvailable === false || !username.trim() || username.trim().length < 3}
                    >
                      {isCheckingUsername ? 'Checking...' :
                       usernameAvailable === false ? 'Username Not Available' :
                       !username.trim() || username.trim().length < 3 ? 'Enter Username' :
                       'Continue'}
                    </Button>
                  </form>
                </div>
              )}

              {/* Sign In Content */}
              <TabsContent value="signin">
                <form onSubmit={handleSignIn} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="signin-email" className="text-gray-700">Email</Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input
                        id="signin-email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={signInEmail}
                        onChange={(e) => setSignInEmail(e.target.value)}
                        className="pl-10 border-gray-200 focus:border-primary-main focus:ring-primary-main"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="signin-password" className="text-gray-700">Password</Label>
                      <button
                        type="button"
                        onClick={() => setAuthState('forgot_password')}
                        className="text-sm text-tertiary-main hover:text-tertiary-dark hover:underline transition-colors"
                      >
                        Forgot password?
                      </button>
                    </div>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input
                        id="signin-password"
                        type={showSignInPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={signInPassword}
                        onChange={(e) => setSignInPassword(e.target.value)}
                        className="pl-10 pr-10 border-gray-200 focus:border-primary-main focus:ring-primary-main"
                        required
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowSignInPassword(!showSignInPassword)}
                      >
                        {showSignInPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full py-6 text-base font-medium rounded-lg bg-primary-main hover:bg-primary-dark transition-colors text-white" 
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>
                </form>
              </TabsContent>
              
              {/* Sign Up Content */}
              <TabsContent value="signup">
                <form onSubmit={handleSignUp} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      type="text"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input
                        id="username"
                        type="text"
                        value={username}
                        onChange={(e) => handleUsernameChange(e.target.value)}
                        placeholder="Choose your username"
                        className={`pl-10 pr-10 border-gray-200 focus:border-primary-main focus:ring-primary-main ${
                          usernameAvailable === false ? 'border-red-300 focus:border-red-500' :
                          usernameAvailable === true ? 'border-green-300 focus:border-green-500' : ''
                        }`}
                        required
                      />
                      {/* Username availability indicator */}
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        {isCheckingUsername && username.length > 2 && (
                          <Loader className="h-4 w-4 animate-spin text-gray-400" />
                        )}
                        {!isCheckingUsername && usernameAvailable === true && (
                          <Check className="h-4 w-4 text-green-500" />
                        )}
                        {!isCheckingUsername && usernameAvailable === false && (
                          <X className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    </div>
                    {/* Username feedback */}
                    {usernameFromUrl && usernameAvailable !== false && (
                      <p className="text-xs text-green-600 flex items-center">
                        <Check className="w-3 h-3 mr-1" />
                        This username was claimed from the landing page
                      </p>
                    )}
                    {usernameAvailable === false && (
                      <p className="text-xs text-red-600 flex items-center">
                        <X className="w-3 h-3 mr-1" />
                        This username is already taken
                      </p>
                    )}
                    {usernameAvailable === true && !usernameFromUrl && (
                      <p className="text-xs text-green-600 flex items-center">
                        <Check className="w-3 h-3 mr-1" />
                        Username is available
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signUpEmail">Email</Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input
                        id="signUpEmail"
                        type="email"
                        value={signUpEmail}
                        onChange={(e) => setSignUpEmail(e.target.value)}
                        placeholder="Enter your email"
                        className="pl-10 border-gray-200 focus:border-primary-main focus:ring-primary-main"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signUpPassword">Password</Label>
                    <div className="relative">
                      <Input
                        id="signUpPassword"
                        type={showSignUpPassword ? "text" : "password"}
                        value={signUpPassword}
                        onChange={(e) => setSignUpPassword(e.target.value)}
                        placeholder="Create a password"
                        className="pr-10"
                        required
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowSignUpPassword(!showSignUpPassword)}
                      >
                        {showSignUpPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showSignUpConfirmPassword ? "text" : "password"}
                        value={signUpConfirmPassword}
                        onChange={(e) => setSignUpConfirmPassword(e.target.value)}
                        placeholder="Confirm your password"
                        className="pr-10"
                        required
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowSignUpConfirmPassword(!showSignUpConfirmPassword)}
                      >
                        {showSignUpConfirmPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        )}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isLoading || isCheckingUsername || usernameAvailable === false}
                  >
                    {isLoading ? 'Creating Account...' :
                     isCheckingUsername ? 'Checking Username...' :
                     usernameAvailable === false ? 'Username Not Available' :
                     'Create Account'}
                  </Button>
                </form>
              </TabsContent>

              {/* Forgot Password Content */}
              <TabsContent value="forgot_password">
                {!resetSent ? (
                  <form onSubmit={handleForgotPassword} className="space-y-4">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">Reset Password</h2>
                      <p className="text-gray-600 mb-4">
                        Enter your email address and we'll send you instructions to reset your password.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reset-email" className="text-gray-700">Email</Label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-5 w-5 text-gray-400" />
                        </div>
                        <Input
                          id="reset-email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={resetEmail}
                          onChange={(e) => setResetEmail(e.target.value)}
                          className="pl-10 border-gray-200 focus:border-primary-main focus:ring-primary-main"
                          required
                        />
                      </div>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full py-6 text-base font-medium rounded-lg bg-primary-main hover:bg-primary-dark transition-colors text-white"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        'Send Reset Instructions'
                      )}
                    </Button>

                    <button
                      type="button"
                      onClick={() => setAuthState('signin')}
                      className="w-full text-sm text-gray-600 hover:text-gray-900 mt-4"
                    >
                      ← Back to Sign In
                    </button>
                  </form>
                ) : (
                  <div className="text-center space-y-4">
                    <div className="inline-block p-3 rounded-full bg-green-100 mb-4">
                      <Check className="w-8 h-8 text-green-600" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">Check Your Email</h2>
                    <p className="text-gray-600">
                      We've sent password reset instructions to <strong>{resetEmail}</strong>
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setAuthState('signin');
                        setResetSent(false);
                        setResetEmail('');
                      }}
                      className="mt-4"
                    >
                      Back to Sign In
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>
            
            <div className="mt-6 text-center text-sm text-gray-600">
              {authState === 'signin' ? (
                <>
                  Don't have an account yet?{' '}
                  <button 
                    type="button" 
                    onClick={() => setAuthState('signup')} 
                    className="text-accent-main hover:text-accent-dark font-medium hover:underline"
                  >
                    Sign up
                  </button>
                </>
              ) : (
                <>
                  Already have an account?{' '}
                  <button 
                    type="button" 
                    onClick={() => setAuthState('signin')} 
                    className="text-primary-main hover:text-primary-dark font-medium hover:underline"
                  >
                    Sign in
                  </button>
                </>
              )}
            </div>
          </div>
          
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              By signing in or creating an account, you agree to our <a href="#" className="underline hover:text-tertiary-main">Terms of Service</a> and <a href="#" className="underline hover:text-tertiary-main">Privacy Policy</a>.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;
