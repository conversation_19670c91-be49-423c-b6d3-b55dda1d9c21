import MainLayout from '@/components/layout/MainLayout';
import { Search, TrendingUp, Compass, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { COLORS } from '@/constants/theme';
import { motion } from 'framer-motion';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';
import TrendingDeals from '@/components/TrendingDeals';
import EnhancedSearchBar from '@/components/search/EnhancedSearchBar';

const Explore = () => {
  const [searchParams] = useSearchParams();
  const categoryParam = searchParams.get('category');
  const navigate = useNavigate();
  
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // Available categories - should match the ones in the database
  const categories = [
    'Fashion', 'Beauty', 'Tech', 'Home', 'Fitness', 'Travel', 'Food', 'Electronics'
  ];
  
  // Set initial category from URL parameters
  useEffect(() => {
    if (categoryParam) {
      // Find a matching category (case-insensitive)
      const matchedCategory = categories.find(
        cat => cat.toLowerCase() === categoryParam.toLowerCase()
      );
      
      if (matchedCategory) {
        setSelectedCategory(matchedCategory);
        toast.info(`Showing coupons in "${matchedCategory}" category`);
      } else {
        // If category not found in our predefined list
        setSelectedCategory(categoryParam);
        toast.info(`Showing coupons in "${categoryParam}" category`);
      }
    }
  }, [categoryParam]);
  
  return (
    <MainLayout>
      <PageContainer decorationType="minimal" decorationOpacity={0.7}>
        <PageHeaderWithBackButton
          title="Explore Deals"
          subtitle="Find trending & popular coupons"
          icon={Compass}
        />
          
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {/* Search Bar */}
          <div className="mb-8">
            <EnhancedSearchBar
              placeholder="Search by brand, influencer, category, code, or description..."
              size="lg"
              showPopup={false}
              className="w-full"
            />
          </div>
          
          {/* Category Filters */}
          <div className="rounded-xl p-4 shadow-sm mb-8" 
            style={{ 
              background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
              border: `1px solid rgba(255, 255, 255, 0.2)`
            }}>
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2"
                style={{ background: COLORS.accent.bgLight }}>
                <TrendingUp className="h-4 w-4" style={{ color: COLORS.accent.main }} />
              </div>
              <h2 className="text-lg font-semibold" style={{ color: COLORS.neutral[800] }}>Categories</h2>
            </div>
            
            <div className="flex flex-wrap gap-2 sm:gap-3">
              <Button
                key="all"
                variant="ghost"
                className={`rounded-full text-xs sm:text-sm px-3 py-1.5 h-auto`}
                style={{ 
                  background: !selectedCategory ? COLORS.primary.gradient : 'rgba(255, 255, 255, 0.5)',
                  color: !selectedCategory ? 'white' : COLORS.neutral[700],
                  boxShadow: !selectedCategory ? '0 2px 5px rgba(0,0,0,0.1)' : 'none'
                }}
                onClick={() => setSelectedCategory(null)}
              >
                All Categories
              </Button>
              
              {categories.map((category) => (
                <Button 
                  key={category}
                  variant="ghost"
                  className={`rounded-full text-xs sm:text-sm px-3 py-1.5 h-auto`}
                  style={{ 
                    background: selectedCategory === category ? COLORS.primary.gradient : 'rgba(255, 255, 255, 0.5)',
                    color: selectedCategory === category ? 'white' : COLORS.neutral[700],
                    boxShadow: selectedCategory === category ? '0 2px 5px rgba(0,0,0,0.1)' : 'none'
                  }}
                  onClick={() => setSelectedCategory(category === selectedCategory ? null : category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Trending Deals Section */}
          <div className="rounded-xl p-4 sm:p-6 shadow-md" 
            style={{ 
              background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`,
              border: `1px solid rgba(255, 255, 255, 0.2)`
            }}>
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                style={{ background: COLORS.primary.bgLight }}>
                <Zap className="h-5 w-5" style={{ color: COLORS.primary.main }} />
              </div>
              <h2 className="text-xl font-semibold" style={{ color: COLORS.neutral[800] }}>
                {selectedCategory ? `${selectedCategory} Deals` : 'Trending Deals'}
              </h2>
            </div>
            
            {/* TrendingDeals Component */}
            <TrendingDeals 
              initialCategory={selectedCategory || 'All'} 
              searchFilter={searchQuery}
            />
          </div>
        </motion.div>
      </PageContainer>
    </MainLayout>
  );
};

export default Explore;
