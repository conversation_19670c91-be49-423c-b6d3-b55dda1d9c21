/**
 * Safe wrapper for Supabase client to prevent initialization errors
 */

let supabaseClient: any = null;
let initializationError: Error | null = null;

// Create a safe getter for the Supabase client
export const getSupabaseClient = () => {
  if (initializationError) {
    console.warn('Supabase client initialization failed, returning mock client');
    return createMockClient();
  }

  if (!supabaseClient) {
    try {
      // Dynamically import the client to catch any initialization errors
      const { supabase } = require('@/integrations/supabase/client');
      supabaseClient = supabase;
    } catch (error) {
      console.error('Failed to initialize Supabase client:', error);
      initializationError = error as Error;
      return createMockClient();
    }
  }

  return supabaseClient;
};

// Create a mock client that won't break the app
const createMockClient = () => {
  const mockError = new Error('Supabase client not available');
  
  return {
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      signInWithPassword: () => Promise.resolve({ 
        data: { user: null, session: null }, 
        error: mockError 
      }),
      signUp: () => Promise.resolve({ 
        data: { user: null, session: null }, 
        error: mockError 
      }),
      signOut: () => Promise.resolve({ error: null }),
      onAuthStateChange: () => ({ 
        data: { 
          subscription: { 
            unsubscribe: () => {} 
          } 
        } 
      }),
      resetPasswordForEmail: () => Promise.resolve({ error: mockError })
    },
    from: (table: string) => ({
      select: () => Promise.resolve({ data: [], error: null }),
      insert: () => Promise.resolve({ data: null, error: mockError }),
      update: () => Promise.resolve({ data: null, error: mockError }),
      delete: () => Promise.resolve({ data: null, error: mockError }),
      eq: function() { return this; },
      order: function() { return this; },
      limit: function() { return this; },
      single: function() { return this; }
    }),
    rpc: () => Promise.resolve({ data: null, error: mockError })
  };
};

// Export a safe supabase instance
export const safeSupabase = getSupabaseClient();
