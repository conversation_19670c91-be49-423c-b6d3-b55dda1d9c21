import { ReactElement } from 'react';
import { PLATFORMS } from '@/constants/theme';

export interface Platform {
  icon: ReactElement;
  name: string;
  color: string;
  gradient: string;
  bgColor: string;
  hoverGradient: string;
}

export interface CreatorDeal {
  id: string;
  creatorName: string;
  creatorHandle: string;
  platform: keyof typeof PLATFORMS;
  category: string;
  discount: string;
  description: string;
  code?: string;
  tags: string[];
  isNew?: boolean;
  expiresIn?: number;
  addedAt: string;
  avatar: string;
  affiliate_link?: string;
} 