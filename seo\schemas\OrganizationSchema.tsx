import React from 'react';
import { Helmet } from 'react-helmet-async';

/**
 * Organization schema specifically optimized for logo display in search results
 * This helps Google display your logo in knowledge panels and search results
 */
const OrganizationSchema: React.FC = () => {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://www.couponlink.in/#organization",
    "name": "CouponLink",
    "url": "https://www.couponlink.in",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.couponlink.in/logos/logo.png",
      "width": 600,
      "height": 60,
      "caption": "CouponLink - Influencer Coupon Management Platform"
    },
    "image": {
      "@type": "ImageObject",
      "url": "https://www.couponlink.in/logos/logo.png",
      "width": 600, 
      "height": 60
    },
    "description": "Find and share verified coupon codes, promo offers, and discounts from your favorite brands. CouponLink gives content creators one powerful link to monetize their digital presence.",
    "sameAs": [
      "https://www.facebook.com/couponlink",
      "https://www.twitter.com/couponlink",
      "https://www.instagram.com/couponslink",
      "https://www.linkedin.com/company/couponlink"
    ],
    "foundingDate": "2023",
    "founders": [{
      "@type": "Person",
      "name": "CouponLink Team"
    }],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer support",
      "email": "<EMAIL>",
      "url": "https://www.couponlink.in/contact"
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema)}
      </script>
    </Helmet>
  );
};

export default OrganizationSchema; 