{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}, {"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/(sitemap|sitemapindex)*.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "routes": [{"src": "/robots.txt", "dest": "/robots.txt"}, {"src": "/(sitemap|sitemapindex).*\\.xml", "dest": "/$1.xml"}]}