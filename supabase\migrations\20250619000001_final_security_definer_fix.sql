-- Final Security Definer Views Fix
-- This migration ensures the views are completely recreated without SECURITY DEFINER
-- Date: 2025-06-19

-- ============================================================================
-- FINAL FIX FOR SECURITY DEFINER VIEWS
-- ============================================================================

-- Drop and recreate popular_brands view without SECURITY DEFINER
DROP VIEW IF EXISTS public.popular_brands CASCADE;

CREATE VIEW public.popular_brands AS
SELECT 
  b.id,
  b.name,
  b.logo_url,
  b.website,
  b.created_at,
  b.updated_at,
  count(c.id) AS coupon_count,
  sum(COALESCE(c.view_count, 0)) AS total_views
FROM brands b
LEFT JOIN coupons c ON b.id = c.brand_id
GROUP BY b.id
ORDER BY sum(COALESCE(c.view_count, 0)) DESC NULLS LAST;

-- Drop and recreate trending_coupons view without SECURITY DEFINER
DROP VIEW IF EXISTS public.trending_coupons CASCADE;

CREATE VIEW public.trending_coupons AS
SELECT 
  c.id,
  c.title,
  c.code,
  c.influencer_id,
  c.brand_id,
  c.category_id,
  c.discount_percent,
  c.discount_amount,
  c.discount_description,
  c.expires_at,
  c.status,
  c.featured,
  c.success_rate,
  c.total_ratings,
  c.average_rating,
  c.is_affiliate,
  c.affiliate_link,
  c.scraped,
  c.created_at,
  c.updated_at,
  c.is_premium,
  c.price,
  c.background_color_hex,
  c.copy_count,
  c.view_count,
  c.use_count,
  c.conversion_rate,
  COALESCE(c.view_count, 0) AS views,
  COALESCE(c.copy_count, 0) AS copies,
  COALESCE(c.use_count, 0) AS uses,
  COALESCE(c.conversion_rate, 0::numeric) AS conversion
FROM coupons c
WHERE c.status = 'active'::coupon_status
ORDER BY c.view_count DESC NULLS LAST, c.created_at DESC;

-- Drop and recreate popular_categories view without SECURITY DEFINER
DROP VIEW IF EXISTS public.popular_categories CASCADE;

CREATE VIEW public.popular_categories AS
SELECT 
  cat.id,
  cat.name,
  cat.slug,
  cat.created_at,
  cat.coupon_count,
  cat.icon_url,
  cat.cover_image_url,
  cat.description,
  cat.display_order,
  cat.color_hex,
  cat.is_featured,
  count(c.id) AS active_coupon_count,
  sum(COALESCE(c.view_count, 0)) AS total_views
FROM categories cat
LEFT JOIN coupons c ON cat.id = c.category_id
GROUP BY cat.id
ORDER BY sum(COALESCE(c.view_count, 0)) DESC NULLS LAST;

-- ============================================================================
-- RESTORE PERMISSIONS
-- ============================================================================

-- Grant SELECT on views to authenticated users
GRANT SELECT ON public.popular_brands TO authenticated;
GRANT SELECT ON public.trending_coupons TO authenticated;
GRANT SELECT ON public.popular_categories TO authenticated;

-- Grant SELECT on views to anonymous users (for public access)
GRANT SELECT ON public.popular_brands TO anon;
GRANT SELECT ON public.trending_coupons TO anon;
GRANT SELECT ON public.popular_categories TO anon;

-- ============================================================================
-- VERIFICATION COMMENTS
-- ============================================================================

-- These views are now created without SECURITY DEFINER property
-- They will use the permissions of the querying user, not the view creator
-- This resolves the Supabase linter security warnings
