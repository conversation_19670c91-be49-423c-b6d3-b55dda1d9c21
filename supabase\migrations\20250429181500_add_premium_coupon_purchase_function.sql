-- Function to handle premium coupon purchases
CREATE OR REPLACE FUNCTION public.purchase_premium_coupon(
  p_buyer_id UUID,
  p_coupon_id UUID,
  p_stripe_payment_id TEXT,
  p_stripe_customer_id TEXT
)
RETURNS UUID AS $$
DECLARE
  v_coupon_record RECORD;
  v_platform_fee NUMERIC;
  v_min_fee NUMERIC;
  v_max_fee NUMERIC;
  v_seller_amount NUMERIC;
  v_purchase_id UUID;
  v_transaction_id UUID;
BEGIN
  -- Check if coupon exists and is premium
  SELECT * INTO v_coupon_record
  FROM public.coupons
  WHERE id = p_coupon_id AND is_premium = true;
  
  IF v_coupon_record.id IS NULL THEN
    RAISE EXCEPTION 'Coupon not found or is not a premium coupon';
  END IF;
  
  -- Get platform fee information
  SELECT 
    platform_fee_percent,
    min_fee_amount,
    max_fee_amount
  INTO 
    v_platform_fee,
    v_min_fee,
    v_max_fee
  FROM public.platform_settings
  LIMIT 1;
  
  -- Calculate platform fee and seller amount
  DECLARE 
    v_calculated_fee NUMERIC := (v_coupon_record.price * v_platform_fee / 100);
  BEGIN
    -- Apply minimum fee if needed
    IF v_calculated_fee < v_min_fee THEN
      v_calculated_fee := v_min_fee;
    END IF;
    
    -- Apply maximum fee if needed and if max fee is set
    IF v_max_fee IS NOT NULL AND v_calculated_fee > v_max_fee THEN
      v_calculated_fee := v_max_fee;
    END IF;
    
    -- Calculate seller amount
    v_seller_amount := v_coupon_record.price - v_calculated_fee;
  END;
  
  -- Create premium purchase record
  INSERT INTO public.premium_purchases (
    buyer_id,
    coupon_id,
    amount,
    stripe_payment_id,
    stripe_customer_id,
    status,
    purchased_at
  ) VALUES (
    p_buyer_id,
    p_coupon_id,
    v_coupon_record.price,
    p_stripe_payment_id,
    p_stripe_customer_id,
    'completed',
    now()
  )
  RETURNING id INTO v_purchase_id;
  
  -- Create transaction record
  INSERT INTO public.transactions (
    buyer_id,
    seller_id,
    coupon_id,
    purchase_id,
    amount,
    platform_fee,
    seller_amount,
    payment_id,
    status,
    created_at
  ) VALUES (
    p_buyer_id,
    v_coupon_record.influencer_id,
    p_coupon_id,
    v_purchase_id,
    v_coupon_record.price,
    v_calculated_fee,
    v_seller_amount,
    p_stripe_payment_id,
    'completed',
    now()
  )
  RETURNING id INTO v_transaction_id;
  
  -- Create notification for the buyer
  INSERT INTO public.notifications (
    user_id,
    type,
    title,
    message,
    related_entity_id,
    related_entity_type
  ) VALUES (
    p_buyer_id,
    'purchase_made',
    'Premium Coupon Purchased',
    'You have successfully purchased the premium coupon: ' || v_coupon_record.title,
    p_coupon_id,
    'coupon'
  );
  
  -- Create notification for the seller
  INSERT INTO public.notifications (
    user_id,
    type,
    title,
    message,
    related_entity_id,
    related_entity_type
  ) VALUES (
    v_coupon_record.influencer_id,
    'payment_received',
    'Premium Coupon Sold',
    'Your premium coupon "' || v_coupon_record.title || '" was purchased. You earned $' || v_seller_amount::TEXT,
    p_coupon_id,
    'coupon'
  );
  
  -- Track coupon interaction
  INSERT INTO public.coupon_interactions (
    coupon_id,
    user_id,
    interaction_type,
    occurred_at
  ) VALUES (
    p_coupon_id,
    p_buyer_id,
    'purchase',
    now()
  );
  
  -- Return the purchase ID
  RETURN v_purchase_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if a user has purchased a premium coupon
CREATE OR REPLACE FUNCTION public.has_purchased_premium_coupon(
  p_user_id UUID,
  p_coupon_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  v_has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.premium_purchases
    WHERE buyer_id = p_user_id
    AND coupon_id = p_coupon_id
    AND status = 'completed'
  ) INTO v_has_purchased;
  
  RETURN v_has_purchased;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create API for getting all premium coupons purchased by a user
CREATE OR REPLACE FUNCTION public.get_user_premium_coupons(p_user_id UUID)
RETURNS TABLE (
  coupon_id UUID,
  coupon_title TEXT,
  brand_name TEXT,
  influencer_name TEXT,
  code TEXT,
  price NUMERIC,
  purchased_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id AS coupon_id,
    c.title AS coupon_title,
    b.name AS brand_name,
    p.full_name AS influencer_name,
    c.code,
    c.price,
    pp.purchased_at
  FROM public.premium_purchases pp
  JOIN public.coupons c ON pp.coupon_id = c.id
  LEFT JOIN public.brands b ON c.brand_id = b.id
  LEFT JOIN public.profiles p ON c.influencer_id = p.id
  WHERE pp.buyer_id = p_user_id
  AND pp.status = 'completed'
  ORDER BY pp.purchased_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 