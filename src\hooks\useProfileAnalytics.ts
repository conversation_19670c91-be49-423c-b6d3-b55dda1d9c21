import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { 
  ProfileAnalytics, 
  InteractionCount, 
  RevenueData, 
  DailyInteraction, 
  DailyRevenue, 
  TrafficSources, 
  GrowthOpportunity 
} from '@/types/analytics';

export const useProfileAnalytics = (influencerId?: string) => {
  const { user } = useAuth();
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });

  // Create optimized fetch function with better performance
  const fetchAnalytics = useCallback(async (): Promise<ProfileAnalytics> => {
    try {
      // Use the provided influencerId or fall back to the current user's id
      const targetId = influencerId || user?.id;
      if (!targetId) {
        console.warn('No user or influencer ID provided');
        return createEmptyAnalyticsData();
      }

      // Format dates for Supabase queries
      const fromDate = dateRange.from.toISOString();
      const toDate = dateRange.to.toISOString();

      // Calculate previous period dates
      const daysDiff = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24));
      const previousFromDate = new Date(dateRange.from);
      previousFromDate.setDate(previousFromDate.getDate() - daysDiff);
      const previousToDate = new Date(dateRange.from);
      previousToDate.setDate(previousToDate.getDate() - 1);

      // Execute optimized queries in parallel with reduced complexity
      const [
        interactionStatsResponse,
        revenueStatsResponse,
        topCouponsResponse,
        trafficStatsResponse
      ] = await Promise.all([
        // Optimized interaction stats query - get both current and previous period data
        supabase.rpc('get_interaction_stats', {
          p_user_id: targetId,
          p_from_date: fromDate,
          p_to_date: toDate,
          p_prev_from_date: previousFromDate.toISOString(),
          p_prev_to_date: previousToDate.toISOString()
        }),

        // Optimized revenue stats query - get both current and previous period data
        supabase.rpc('get_revenue_stats', {
          p_user_id: targetId,
          p_from_date: fromDate,
          p_to_date: toDate,
          p_prev_from_date: previousFromDate.toISOString(),
          p_prev_to_date: previousToDate.toISOString()
        }),

        // Get top performing coupons (limited to 5 for faster response)
        supabase
          .from('coupons')
          .select('id, title, code, brand:brands(id,name), view_count, copy_count')
          .eq('influencer_id', targetId)
          .order('view_count', { ascending: false })
          .limit(5),

        // Get traffic sources with limit for performance
        supabase
          .from('profile_views')
          .select('referrer')
          .eq('profile_id', targetId)
          .gte('created_at', fromDate)
          .lte('created_at', toDate)
          .limit(1000) // Limit to prevent large data sets
      ]);

      // Handle any errors from queries
      if (interactionStatsResponse.error) {
        console.warn('Interaction stats error:', interactionStatsResponse.error);
        // Fall back to basic queries if RPC functions don't exist
        return await fetchAnalyticsBasic(targetId, fromDate, toDate);
      }
      if (revenueStatsResponse.error) {
        console.warn('Revenue stats error:', revenueStatsResponse.error);
        return await fetchAnalyticsBasic(targetId, fromDate, toDate);
      }
      if (topCouponsResponse.error) throw topCouponsResponse.error;
      if (trafficStatsResponse.error) throw trafficStatsResponse.error;

      // Process optimized data from RPC functions
      const interactionStats = interactionStatsResponse.data?.[0] || {};
      const revenueStats = revenueStatsResponse.data?.[0] || {};

      // Extract current period data
      const interactionCounts = {
        views: interactionStats.current_views || 0,
        clicks: interactionStats.current_clicks || 0,
        copies: interactionStats.current_copies || 0
      };

      const totalRevenue = revenueStats.current_revenue || 0;
      const premiumSales = revenueStats.current_sales_count || 0;

      // Extract previous period data
      const previousInteractionCounts = {
        views: interactionStats.previous_views || 0,
        clicks: interactionStats.previous_clicks || 0,
        copies: interactionStats.previous_copies || 0
      };
      const previousPeriodRevenue = revenueStats.previous_revenue || 0;

      // Calculate percent changes
      const viewsPercentChange = calculatePercentChange(interactionCounts.views, previousInteractionCounts.views);
      const clicksPercentChange = calculatePercentChange(interactionCounts.clicks, previousInteractionCounts.clicks);
      const copiesPercentChange = calculatePercentChange(interactionCounts.copies, previousInteractionCounts.copies);
      const revenuePercentChange = calculatePercentChange(totalRevenue, previousPeriodRevenue);

      // Process traffic sources
      const trafficSources = processTrafficSources(trafficStatsResponse.data);

      // Process top coupons
      const topCoupons = topCouponsResponse.data || [];

      // Create daily data from aggregated stats (simplified for performance)
      const dailyInteractions = createDailyInteractionsFromStats(interactionStats, dateRange);
      const dailyRevenue = createDailyRevenueFromStats(revenueStats, dateRange);

      // Return the final optimized data
      return {
        totalViews: interactionCounts.views,
        couponClicks: interactionCounts.clicks,
        couponCopies: interactionCounts.copies,
        premiumSales,
        totalRevenue,
        dailyInteractions,
        dailyRevenue,
        viewsPercentChange,
        clicksPercentChange,
        copiesPercentChange,
        revenuePercentChange,
        trafficSources,
        growthOpportunities: generateGrowthOpportunities(interactionCounts, trafficSources),

        // Add the remaining required properties from ProfileAnalytics
        totalCouponViews: interactionCounts.views,
        totalCouponClicks: interactionCounts.clicks,
        totalCouponCopies: interactionCounts.copies,
        totalPremiumPurchases: premiumSales,
        interactionCounts,
        previousPeriodRevenue,
        percentChange: revenuePercentChange,
        topCoupons
      } as ProfileAnalytics;
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      return createEmptyAnalyticsData();
    }
  }, [influencerId, user?.id, dateRange]);

  // Fallback function for basic analytics when RPC functions are not available
  const fetchAnalyticsBasic = async (targetId: string, fromDate: string, toDate: string): Promise<ProfileAnalytics> => {
    try {
      // Basic queries with minimal data fetching
      const [interactionResponse, revenueResponse, topCouponsResponse] = await Promise.all([
        supabase
          .from('coupon_interactions')
          .select('interaction_type')
          .eq('user_id', targetId)
          .gte('occurred_at', fromDate)
          .lte('occurred_at', toDate)
          .limit(1000), // Limit for performance

        supabase
          .from('premium_purchases')
          .select('amount')
          .eq('buyer_id', targetId)
          .gte('purchased_at', fromDate)
          .lte('purchased_at', toDate)
          .limit(100), // Limit for performance

        supabase
          .from('coupons')
          .select('id, title, code, view_count, copy_count')
          .eq('influencer_id', targetId)
          .order('view_count', { ascending: false })
          .limit(5)
      ]);

      // Process basic data
      const interactionCounts = processInteractionCounts(interactionResponse.data);
      const totalRevenue = calculateTotalRevenue(revenueResponse.data);

      return {
        totalViews: interactionCounts.views,
        couponClicks: interactionCounts.clicks,
        couponCopies: interactionCounts.copies,
        premiumSales: revenueResponse.data?.length || 0,
        totalRevenue,
        dailyInteractions: [],
        dailyRevenue: [],
        viewsPercentChange: 0,
        clicksPercentChange: 0,
        copiesPercentChange: 0,
        revenuePercentChange: 0,
        trafficSources: { social: 0, direct: 0, search: 0 },
        growthOpportunities: [],
        totalCouponViews: interactionCounts.views,
        totalCouponClicks: interactionCounts.clicks,
        totalCouponCopies: interactionCounts.copies,
        totalPremiumPurchases: revenueResponse.data?.length || 0,
        interactionCounts,
        previousPeriodRevenue: 0,
        percentChange: 0,
        topCoupons: topCouponsResponse.data || []
      } as ProfileAnalytics;
    } catch (error) {
      console.error('Error in basic analytics fetch:', error);
      return createEmptyAnalyticsData();
    }
  };

  // Helper to create daily interactions from aggregated stats
  const createDailyInteractionsFromStats = (stats: any, dateRange: { from: Date; to: Date }): DailyInteraction[] => {
    // For performance, create simplified daily data
    const days = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24));
    const dailyViews = Math.floor((stats.current_views || 0) / days);
    const dailyClicks = Math.floor((stats.current_clicks || 0) / days);
    const dailyCopies = Math.floor((stats.current_copies || 0) / days);

    const result: DailyInteraction[] = [];
    for (let i = 0; i < Math.min(days, 30); i++) { // Limit to 30 days for performance
      const date = new Date(dateRange.from);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      result.push(
        { occurred_at: dateStr, interaction_type: 'view', count: dailyViews },
        { occurred_at: dateStr, interaction_type: 'click', count: dailyClicks },
        { occurred_at: dateStr, interaction_type: 'copy', count: dailyCopies }
      );
    }
    return result;
  };

  // Helper to create daily revenue from aggregated stats
  const createDailyRevenueFromStats = (stats: any, dateRange: { from: Date; to: Date }): DailyRevenue[] => {
    const days = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24));
    const dailyRevenue = (stats.current_revenue || 0) / days;

    const result: DailyRevenue[] = [];
    for (let i = 0; i < Math.min(days, 30); i++) { // Limit to 30 days for performance
      const date = new Date(dateRange.from);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      result.push({ date: dateStr, revenue: dailyRevenue });
    }
    return result;
  };

  // Helper to process interaction counts
  const processInteractionCounts = (data: any): { views: number; clicks: number; copies: number } => {
    const counts = { views: 0, clicks: 0, copies: 0 };
    
    if (Array.isArray(data)) {
      data.forEach((item: InteractionCount) => {
        if (item.interaction_type === 'view') {
          counts.views = Number(item.count) || 0;
        } else if (item.interaction_type === 'click') {
          counts.clicks = Number(item.count) || 0;
        } else if (item.interaction_type === 'copy') {
          counts.copies = Number(item.count) || 0;
        }
      });
    }
    
    return counts;
  };

  // Helper to calculate total revenue
  const calculateTotalRevenue = (data: any): number => {
    let total = 0;
    
    if (Array.isArray(data)) {
      data.forEach((item: RevenueData) => {
        total += Number(item.amount) || 0;
      });
    }
    
    return total;
  };

  // Helper to process daily interactions
  const processDailyInteractions = (data: any): DailyInteraction[] => {
    const result: DailyInteraction[] = [];
    
    if (!Array.isArray(data) || data.length === 0) {
      return result;
    }
    
    // Group interactions by date and type more efficiently
    const groupedInteractions: { [key: string]: { [type: string]: number } } = {};
    
    data.forEach((item: any) => {
      const date = new Date(item.occurred_at).toISOString().split('T')[0];
      const type = item.interaction_type;
      
      if (!groupedInteractions[date]) {
        groupedInteractions[date] = {};
      }
      
      if (!groupedInteractions[date][type]) {
        groupedInteractions[date][type] = 0;
      }
      
      groupedInteractions[date][type]++;
    });
    
    // Convert grouped data to array format
    Object.entries(groupedInteractions).forEach(([date, types]) => {
      Object.entries(types).forEach(([type, count]) => {
        result.push({
          occurred_at: date,
          interaction_type: type,
          count: count,
        });
      });
    });
    
    return result;
  };

  // Helper to process daily revenue
  const processDailyRevenue = (data: any): DailyRevenue[] => {
    const result: DailyRevenue[] = [];
    
    if (!Array.isArray(data) || data.length === 0) {
      return result;
    }
    
    const revenueByDate: { [key: string]: number } = {};
    
    data.forEach((item: any) => {
      const date = new Date(item.purchased_at).toISOString().split('T')[0];
      revenueByDate[date] = (revenueByDate[date] || 0) + Number(item.amount || 0);
    });
    
    Object.entries(revenueByDate).forEach(([date, revenue]) => {
      result.push({ date, revenue });
    });
    
    return result;
  };

  // Helper to calculate percent change
  const calculatePercentChange = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  // Helper to process traffic sources
  const processTrafficSources = (data: any): TrafficSources => {
    const sources: TrafficSources = {
      social: 0,
      direct: 0,
      search: 0,
    };

    if (!Array.isArray(data) || data.length === 0) {
      return sources;
    }
    
    const total = data.length;
    
    const socialCount = data.filter(item => 
      item.referrer?.includes('facebook.com') || 
      item.referrer?.includes('twitter.com') || 
      item.referrer?.includes('instagram.com')
    ).length;
    
    const searchCount = data.filter(item => 
      item.referrer?.includes('google.com') || 
      item.referrer?.includes('bing.com')
    ).length;
    
    const directCount = data.filter(item => !item.referrer).length;
    
    sources.social = Math.round((socialCount / total) * 100);
    sources.search = Math.round((searchCount / total) * 100);
    sources.direct = Math.round((directCount / total) * 100);
    
    return sources;
  };

  // Helper to generate growth opportunities
  const generateGrowthOpportunities = (
    interactionCounts: { views: number; clicks: number; copies: number },
    trafficSources: TrafficSources
  ): GrowthOpportunity[] => {
    const opportunities: GrowthOpportunity[] = [];
    
    // Social media opportunity
    if (trafficSources.social < 30) {
      opportunities.push({
        title: 'Increase Social Traffic',
        description: 'Your social media traffic is low. Consider sharing your coupons on social platforms.',
        icon: 'share',
        color: 'tertiary',
        secondaryColor: 'text-tertiary-dark'
      });
    }
    
    // Conversion opportunity
    const conversionRate = (interactionCounts.copies / (interactionCounts.views || 1)) * 100;
    if (conversionRate < 10) {
      opportunities.push({
        title: 'Optimize Conversion Rate',
        description: 'Your conversion rate is below average. Try A/B testing different coupon descriptions.',
        icon: 'trend-up',
        color: 'primary',
        secondaryColor: 'text-primary-dark'
      });
    }
    
    return opportunities;
  };

  // Create empty analytics data object
  const createEmptyAnalyticsData = (): ProfileAnalytics => ({
    totalViews: 0,
    couponClicks: 0,
    couponCopies: 0,
    premiumSales: 0,
    totalRevenue: 0,
    dailyInteractions: [],
    dailyRevenue: [],
    viewsPercentChange: 0,
    clicksPercentChange: 0,
    copiesPercentChange: 0,
    revenuePercentChange: 0,
    trafficSources: { social: 0, direct: 0, search: 0 },
    growthOpportunities: [],
    
    // Add the remaining required properties from ProfileAnalytics
    totalCouponViews: 0,
    totalCouponClicks: 0,
    totalCouponCopies: 0,
    totalPremiumPurchases: 0,
    interactionCounts: { views: 0, clicks: 0, copies: 0 },
    previousPeriodRevenue: 0,
    percentChange: 0,
    
    // Add empty topCoupons
    topCoupons: []
  } as ProfileAnalytics);

  // Use React Query with optimized caching strategy for better performance
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['profileAnalytics', influencerId || user?.id, dateRange.from.toDateString(), dateRange.to.toDateString()],
    queryFn: fetchAnalytics,
    staleTime: 10 * 60 * 1000, // 10 minutes - longer cache for analytics
    gcTime: 60 * 60 * 1000, // 1 hour cache time
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Don't refetch on mount if data exists
    retry: 2, // Retry failed requests
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    enabled: !!(influencerId || user?.id), // Only run if we have a user ID
  });

  return {
    analytics: data,
    isLoading,
    error,
    refetch,
    setDateRange,
    dateRange
  };
};

// Add these exports to help with TypeScript compatibility
export const useProfileMetrics = useProfileAnalytics;
export const useProfileViewsOverTime = useProfileAnalytics;
export const useRevenueSummary = useProfileAnalytics;
