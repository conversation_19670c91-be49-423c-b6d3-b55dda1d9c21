import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Layers, 
  BarChart3, 
  Share2, 
  Presentation, 
  QrCode, 
  CreditCard,
  Plus
} from 'lucide-react';

// Features data
const features = [
  {
    title: "Coupon Organization",
    description: "Organize promotional codes with custom tagging and search to quickly find what you need.",
    icon: <Layers className="h-5 w-5 text-white" />,
    color: "bg-primary-main",
    textColor: "text-primary-main",
  },
  {
    title: "Performance Analytics",
    description: "Track usage, conversions, and revenue with detailed reports that help optimize your strategy.",
    icon: <BarChart3 className="h-5 w-5 text-white" />,
    color: "bg-secondary-main",
    textColor: "text-secondary-main",
  },
  {
    title: "Easy Sharing",
    description: "Share across social platforms with one click, reaching your audience wherever they are.",
    icon: <Share2 className="h-5 w-5 text-white" />,
    color: "bg-tertiary-main",
    textColor: "text-tertiary-main",
  },
  {
    title: "Campaign Management",
    description: "Create campaigns with scheduled releases and expirations to automate your promotional calendar.",
    icon: <Presentation className="h-5 w-5 text-white" />,
    color: "bg-accent-main",
    textColor: "text-accent-main",
  },
  {
    title: "QR Code Generation",
    description: "Create branded QR codes for easy coupon redemption across both digital and print media.",
    icon: <QrCode className="h-5 w-5 text-white" />,
    color: "bg-primary-main",
    textColor: "text-primary-main",
  },
  {
    title: "Revenue Tracking",
    description: "Monitor earnings with comprehensive dashboards that visualize your financial performance.",
    icon: <CreditCard className="h-5 w-5 text-white" />,
    color: "bg-secondary-main",
    textColor: "text-secondary-main",
  }
];

const FeatureHighlight: React.FC = () => {
  const [activeFeature, setActiveFeature] = useState(0);
  
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold mb-3">Our Features</h2>
          <p className="text-gray-600 max-w-xl mx-auto">
            Everything you need to manage your promotional campaigns effectively
          </p>
        </motion.div>
        
        {/* Desktop & Tablet Layout (2-column with sidebar) */}
        <div className="hidden md:flex gap-8 mb-12">
          {/* Sidebar with feature list */}
          <div className="w-1/3 bg-gray-50 rounded-xl p-5 h-[500px] sticky top-20">
            <h3 className="text-lg font-medium mb-4 text-gray-700">Explore Features</h3>
            
            <div className="space-y-2">
              {features.map((feature, index) => (
                <div 
                  key={index}
                  className={`p-3 rounded-lg flex items-center cursor-pointer transition-all ${
                    activeFeature === index 
                      ? `${feature.color} text-white` 
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className={`${activeFeature === index ? 'bg-white/20' : feature.color} p-2 rounded-lg mr-3`}>
                    {feature.icon}
                  </div>
                  <span className="font-medium">{feature.title}</span>
                </div>
              ))}
            </div>
          </div>
          
          {/* Feature details */}
          <div className="w-2/3">
            <motion.div
              key={activeFeature}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-100 rounded-xl p-6 shadow-sm"
            >
              <div className="flex items-center mb-6">
                <div className={`${features[activeFeature].color} p-3 rounded-lg mr-4`}>
                  {features[activeFeature].icon}
                </div>
                <h2 className="text-2xl font-bold">{features[activeFeature].title}</h2>
              </div>
              
              <p className="text-gray-600 mb-6">
                {features[activeFeature].description}
              </p>
              
              <div className="grid grid-cols-2 gap-4 mt-8">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Benefits</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Time savings</span>
                    </li>
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Better organization</span>
                    </li>
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Improved analytics</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Use Cases</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Content creators</span>
                    </li>
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Marketing teams</span>
                    </li>
                    <li className="flex items-center">
                      <Plus className="h-4 w-4 mr-2" />
                      <span className="text-sm text-gray-600">Affiliate marketers</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
        
        {/* Mobile Layout (Alternating Cards) */}
        <div className="md:hidden space-y-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`bg-white rounded-lg border border-gray-200 overflow-hidden ${
                index % 2 === 0 ? 'border-l-4' : 'border-r-4'
              }`}
              style={{ borderLeftColor: index % 2 === 0 ? 'var(--color-primary-main)' : '', 
                       borderRightColor: index % 2 !== 0 ? 'var(--color-secondary-main)' : '' }}
            >
              <div className="p-4">
                <div className={`flex items-center ${index % 2 !== 0 ? 'flex-row-reverse' : ''}`}>
                  <div className={`${feature.color} p-2 rounded-md flex items-center justify-center ${index % 2 !== 0 ? 'ml-3' : 'mr-3'}`}>
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className={`text-gray-600 text-sm mt-3 ${index % 2 !== 0 ? 'text-right' : ''}`}>
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureHighlight; 