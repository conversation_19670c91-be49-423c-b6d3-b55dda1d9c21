// Helper functions for dealing with localStorage

// Keys
const MENU_INDICATOR_SHOWN = 'menu_indicator_shown_permanent';
const CURRENT_SESSION = 'current_session_id';

/**
 * Initialize a new session ID on app start
 * Call this from App.tsx useEffect on first load
 */
export const initializeSession = (): void => {
  try {
    // Generate a random session ID
    const sessionId = Math.random().toString(36).substring(2, 15);
    localStorage.setItem(CURRENT_SESSION, sessionId);
    // Do not clear menu indicator flag anymore as we want it to persist
  } catch (error) {
    console.error('Error initializing session:', error);
  }
};

/**
 * Get the current session ID safely
 * @returns session ID or null if not available
 */
export const getCurrentSessionId = (): string | null => {
  try {
    return localStorage.getItem(CURRENT_SESSION);
  } catch (error) {
    console.error('Error getting session ID:', error);
    return null;
  }
};

/**
 * Check if the menu indicator has been shown to the user ever
 */
export const hasSeenMenuIndicator = (): boolean => {
  try {
    return localStorage.getItem(MENU_INDICATOR_SHOWN) === 'true';
  } catch (e) {
    // Handle any localStorage errors gracefully
    console.error('Error checking localStorage:', e);
    return false;
  }
};

/**
 * Mark that the user has seen the menu indicator permanently
 */
export const markMenuIndicatorAsShown = (): void => {
  try {
    localStorage.setItem(MENU_INDICATOR_SHOWN, 'true');
  } catch (e) {
    console.error('Error setting localStorage:', e);
  }
};

/**
 * Reset the menu indicator state (for testing purposes)
 */
export const resetMenuIndicator = (): void => {
  try {
    localStorage.removeItem(MENU_INDICATOR_SHOWN);
  } catch (e) {
    console.error('Error removing from localStorage:', e);
  }
};

/**
 * Safely parse JSON from localStorage with error handling
 * @param key - localStorage key
 * @param defaultValue - default value to return if parsing fails
 * @returns parsed value or default value
 */
export const safeParseLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    return JSON.parse(item);
  } catch (error) {
    console.error(`Error parsing localStorage key "${key}":`, error);
    // Clear the corrupted data
    try {
      localStorage.removeItem(key);
    } catch (removeError) {
      console.error(`Error removing corrupted localStorage key "${key}":`, removeError);
    }
    return defaultValue;
  }
};

/**
 * Safely set JSON data to localStorage with error handling
 * @param key - localStorage key
 * @param value - value to store
 * @returns boolean indicating success
 */
export const safeSetLocalStorage = (key: string, value: any): boolean => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error setting localStorage key "${key}":`, error);
    return false;
  }
};

/**
 * Clean up any corrupted localStorage data
 * This function can be called on app initialization to clean up any invalid JSON
 */
export const cleanupLocalStorage = (): void => {
  // Define which keys should contain JSON data vs plain strings
  const jsonKeys = ['recentSearches', 'createButtonClicked'];
  const stringKeys = [MENU_INDICATOR_SHOWN, CURRENT_SESSION, 'supabase_last_failure'];

  // Check JSON keys for corruption
  jsonKeys.forEach(key => {
    try {
      const item = localStorage.getItem(key);
      if (item !== null) {
        // Try to parse it to see if it's valid JSON
        JSON.parse(item);
      }
    } catch (error) {
      console.warn(`Removing corrupted localStorage key "${key}":`, error);
      try {
        localStorage.removeItem(key);
      } catch (removeError) {
        console.error(`Error removing corrupted localStorage key "${key}":`, removeError);
      }
    }
  });

  // Check string keys for basic validity (no need to parse as JSON)
  stringKeys.forEach(key => {
    try {
      const item = localStorage.getItem(key);
      if (item !== null) {
        // For boolean flags, ensure they're 'true' or 'false'
        if (key === MENU_INDICATOR_SHOWN && item !== 'true' && item !== 'false') {
          console.warn(`Removing invalid boolean localStorage key "${key}": ${item}`);
          localStorage.removeItem(key);
        }
        // For session ID, just ensure it's a reasonable string (not empty, not too long)
        if (key === CURRENT_SESSION && (item.length === 0 || item.length > 50)) {
          console.warn(`Removing invalid session localStorage key "${key}": ${item}`);
          localStorage.removeItem(key);
        }
        // For timestamp, ensure it's a valid number
        if (key === 'supabase_last_failure' && (isNaN(parseInt(item)) || parseInt(item) < 0)) {
          console.warn(`Removing invalid timestamp localStorage key "${key}": ${item}`);
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn(`Error checking localStorage key "${key}":`, error);
      try {
        localStorage.removeItem(key);
      } catch (removeError) {
        console.error(`Error removing localStorage key "${key}":`, removeError);
      }
    }
  });
};