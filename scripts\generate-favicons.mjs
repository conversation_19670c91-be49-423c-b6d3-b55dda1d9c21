import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const require = createRequire(import.meta.url);
const sharp = require('sharp');
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function generateFavicon(size, outputName) {
  try {
    const sourcePath = join(__dirname, '../public/favicon/temp/source.png');
    const outputPath = join(__dirname, '../public/favicon', outputName);
    
    await sharp(sourcePath)
      .resize(size, size, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .toFile(outputPath);
    console.log(`Generated ${outputName}`);
  } catch (err) {
    console.error(`Error generating ${outputName}:`, err);
  }
}

async function main() {
  try {
    await Promise.all([
      generateFavicon(16, 'favicon-16x16.png'),
      generateFavicon(32, 'favicon-32x32.png'),
      generateFavicon(180, 'apple-touch-icon.png'),
      generateFavicon(192, 'android-chrome-192x192.png'),
      generateFavicon(512, 'android-chrome-512x512.png')
    ]);
    console.log('All favicons generated successfully!');
  } catch (err) {
    console.error('Error:', err);
  }
}

main(); 