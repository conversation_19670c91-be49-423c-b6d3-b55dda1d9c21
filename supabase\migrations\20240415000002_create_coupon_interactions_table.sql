-- Create the coupon_interactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.coupon_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coupon_id UUID NOT NULL REFERENCES public.coupons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  interaction_type TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  occurred_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_user_id ON public.coupon_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_coupon_id ON public.coupon_interactions(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_type ON public.coupon_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_coupon_interactions_occurred_at ON public.coupon_interactions(occurred_at);

-- Enable Row Level Security
ALTER TABLE public.coupon_interactions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow anonymous inserts to coupon_interactions" ON public.coupon_interactions;
DROP POLICY IF EXISTS "Allow service role full access to coupon_interactions" ON public.coupon_interactions;
DROP POLICY IF EXISTS "Allow users to see relevant coupon interactions" ON public.coupon_interactions;

-- Allow anyone to insert into coupon_interactions (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to coupon_interactions"
ON public.coupon_interactions
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to coupon_interactions"
ON public.coupon_interactions
USING (true)
WITH CHECK (true);

-- Allow users to see their own interactions and interactions with their coupons
CREATE POLICY "Allow users to see relevant coupon interactions"
ON public.coupon_interactions
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  EXISTS (
    SELECT 1 FROM coupons
    WHERE coupons.id = coupon_interactions.coupon_id
    AND coupons.influencer_id = auth.uid()
  )
); 