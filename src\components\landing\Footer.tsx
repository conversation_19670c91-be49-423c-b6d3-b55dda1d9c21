import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  ArrowUpRight,
  Instagram,
  Twitter,
  Github,
  Heart,
  Mail
} from 'lucide-react';
import { COLORS } from '@/constants/theme';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };
  
  return (
    <footer className="relative pt-16 pb-10 overflow-hidden">
      {/* Abstract creative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Gradient circles */}
        <div className="absolute right-[10%] top-[30%] w-[30%] aspect-square rounded-full bg-gradient-to-br from-primary-bgLight to-primary-bgLight/50 blur-3xl"></div>
        <div className="absolute left-[5%] bottom-[20%] w-[25%] aspect-square rounded-full bg-gradient-to-br from-secondary-bgLight to-secondary-bgLight/50 blur-3xl"></div>
        
        {/* Decorative lines */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <path d="M0,0 Q50,50 100,0" fill="none" stroke="rgba(220, 220, 220, 0.2)" strokeWidth="0.2" />
          <path d="M0,100 Q50,50 100,100" fill="none" stroke="rgba(220, 220, 220, 0.2)" strokeWidth="0.2" />
        </svg>
        
        {/* Minimalist circles */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 rounded-full bg-primary-bgLight"></div>
        <div className="absolute top-1/2 right-1/3 w-3 h-3 rounded-full bg-secondary-bgLight"></div>
        <div className="absolute bottom-1/4 right-1/4 w-2 h-2 rounded-full bg-tertiary-bgLight"></div>
      </div>

      <div className="max-w-5xl mx-auto px-6 relative z-10">
        {/* Logo and Tagline - Centered */}
        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeIn}
          className="flex flex-col items-center mb-12 text-center"
        >
          <div className="inline-flex items-center justify-center mb-5">
            <div className="h-12 w-12 rounded-xl overflow-hidden bg-white shadow-sm mr-3">
              <img 
                src="/logos/logo.png" 
                alt="CouponLink Logo" 
                className="w-full h-full object-contain"
              />
            </div>
            <span className="text-2xl font-bold text-primary-main">
              CouponLink
            </span>
          </div>
          
          <p className="text-gray-600 text-sm max-w-md text-center mb-8">
            One link to share everything — content, coupons, and deals
          </p>
          
          {/* Main CTA Button */}
          <Link to="/auth?mode=signup" className="group relative mb-12">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-main to-primary-dark rounded-full blur opacity-60 group-hover:opacity-80 transition duration-300"></div>
            <Button className="relative bg-white hover:bg-gray-50 text-gray-800 px-8 py-2 h-auto rounded-full shadow-md group-hover:shadow-lg transition-all duration-300">
              <span>Get Started</span>
              <ArrowUpRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
            </Button>
          </Link>
        </motion.div>

        {/* Minimal Links - Horizontal Bar */}
        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeIn}
          className="flex flex-wrap justify-center gap-x-8 gap-y-3 mb-12"
        >
          {[
            { name: "Features", url: "/features" },
            { name: "Blog", url: "/blog" },
            { name: "Help", url: "/help" },
            { name: "Terms", url: "/terms" },
            { name: "Privacy", url: "/privacy" },
            { name: "Contact", url: "/contact" }
          ].map((link) => (
            <Link 
              key={link.name}
              to={link.url}
              className="text-gray-600 hover:text-primary-main text-sm relative group"
              >
              <span>{link.name}</span>
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-main group-hover:w-full transition-all duration-300"></span>
              </Link>
          ))}
        </motion.div>

        {/* Social Icons with Creative Hover Effect */}
        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeIn}
          className="flex justify-center space-x-5 mb-10"
        >
          {[
            { icon: <Twitter size={18} />, color: "hover:bg-tertiary-main" },
            { icon: <Instagram size={18} />, color: "hover:bg-accent-main" },
            { icon: <Github size={18} />, color: "hover:bg-primary-main" }
          ].map((social, i) => (
            <motion.a
              key={i}
              href="#"
              className={`w-10 h-10 rounded-full flex items-center justify-center bg-white border border-gray-100 text-gray-600 shadow-sm ${social.color} hover:text-white hover:scale-110 hover:shadow-md transition-all duration-300`}
              whileHover={{ y: -3 }}
            >
              {social.icon}
            </motion.a>
          ))}
        </motion.div>

        {/* Business Contact Email */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeIn}
          className="text-center mb-8"
        >
          <p className="text-gray-600 text-sm mb-2">Business Inquiries</p>
          <a
            href="mailto:<EMAIL>"
            className="inline-flex items-center text-primary-main hover:text-primary-dark transition-colors font-medium"
          >
            <Mail className="w-4 h-4 mr-2" />
            <EMAIL>
          </a>
        </motion.div>

        {/* Footer Credit - Creative Style */}
        <motion.div 
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeIn}
          className="text-center text-gray-500 text-xs flex flex-col items-center"
        >
          <div className="flex items-center mb-2">
            <span>© {currentYear} CouponLink</span>
            <span className="mx-2">•</span>
            <Link to="/terms" className="hover:text-primary-main transition-colors">Terms</Link>
            <span className="mx-2">•</span>
            <Link to="/privacy" className="hover:text-primary-main transition-colors">Privacy</Link>
          </div>
          
          <p className="flex items-center text-xs">
            <span>Made with</span>
            <Heart className="w-3 h-3 mx-1 text-accent-main fill-accent-main" />
            <span>for creators</span>
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer; 