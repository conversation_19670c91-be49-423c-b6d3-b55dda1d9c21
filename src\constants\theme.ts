import { FiYoutube, FiInstagram } from 'react-icons/fi';
import { FaTiktok } from 'react-icons/fa';
import React, { ReactElement } from 'react';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config';

// Types for platform objects
interface Platform {
  icon: ReactElement;
  name: string;
  color: string;
  gradient: string;
  bgColor: string;
  hoverGradient: string;
}

// Get tailwind config
const twConfig = resolveConfig(tailwindConfig);

// Simplify the approach - define base color objects
// These match the colors in tailwind config and act as a fallback
const primaryColors = {
  main: '#0ea76b',
  light: '#25c785',
  dark: '#0a8554',
  gradient: 'linear-gradient(135deg, #0ea76b 0%, #0a8554 100%)',
  bgLight: 'rgba(14, 167, 107, 0.1)'
};

const secondaryColors = {
  main: '#e9a800',
  light: '#ffc01e',
  dark: '#c89000',
  gradient: 'linear-gradient(135deg, #e9a800 0%, #c89000 100%)',
  bgLight: 'rgba(233, 168, 0, 0.1)'
};

const accentColors = {
  main: '#c30f69',
  light: '#e71f80',
  dark: '#a00954',
  gradient: 'linear-gradient(135deg, #c30f69 0%, #a00954 100%)',
  bgLight: 'rgba(195, 15, 105, 0.1)'
};

const tertiaryColors = {
  main: '#0095f6',
  light: '#20a6ff',
  dark: '#0077c8',
  gradient: 'linear-gradient(135deg, #0095f6 0%, #0077c8 100%)',
  bgLight: 'rgba(0, 149, 246, 0.1)'
};

const neutralColors = {
  50: '#F9FAFB',
  100: '#F3F4F6',
  200: '#E5E7EB',
  300: '#D1D5DB',
  400: '#9CA3AF',
  500: '#6B7280',
  600: '#4B5563',
  700: '#374151',
  800: '#1F2937',
  900: '#111827'
};

const surfaceColors = {
  light: 'rgba(255, 255, 255, 0.95)',
  lightTransparent: 'rgba(255, 255, 255, 0.8)',
  dark: 'rgba(31, 41, 55, 0.95)',
  darkTransparent: 'rgba(31, 41, 55, 0.8)',
  glass: 'rgba(255, 255, 255, 0.6)'
};

// Export the color system
export const COLORS = {
  primary: primaryColors,
  secondary: secondaryColors,
  accent: accentColors,
  tertiary: tertiaryColors,
  neutral: neutralColors,
  surface: surfaceColors,
  brand: (twConfig.theme?.colors as any)?.brand || {}
};

// Platform configuration
export const PLATFORMS: Record<string, Platform> = {
  youtube: { 
    icon: React.createElement(FiYoutube, { className: "w-4 h-4" }), 
    name: 'YouTube',
    color: COLORS.accent.main,
    gradient: COLORS.accent.gradient,
    bgColor: COLORS.accent.bgLight,
    hoverGradient: 'linear-gradient(135deg, #c30f69 0%, #c30f69 100%)'
  },
  instagram: { 
    icon: React.createElement(FiInstagram, { className: "w-4 h-4" }), 
    name: 'Instagram',
    color: COLORS.tertiary.main,
    gradient: COLORS.tertiary.gradient,
    bgColor: COLORS.tertiary.bgLight,
    hoverGradient: 'linear-gradient(135deg, #0095f6 0%, #0095f6 100%)'
  },
  tiktok: { 
    icon: React.createElement(FaTiktok, { className: "w-4 h-4" }), 
    name: 'TikTok',
    color: COLORS.primary.main,
    gradient: COLORS.primary.gradient,
    bgColor: COLORS.primary.bgLight,
    hoverGradient: 'linear-gradient(135deg, #0ea76b 0%, #0ea76b 100%)'
  }
};

// Sample deals data
export const DEALS = [
  {
    id: '1',
    creatorName: 'Tech With Jay',
    creatorHandle: '@techwithjay',
    platform: 'youtube' as const,
    category: 'Tech',
    discount: 'Flat 25% OFF',
    description: 'Boat Earbuds and accessories',
    code: 'TECH25',
    tags: ['Electronics', 'Audio'],
    isNew: true,
    addedAt: '2 min ago',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    affiliate_link: '/explore'
  },
  {
    id: '2',
    creatorName: 'Fashion Forward',
    creatorHandle: '@fashionfwd',
    platform: 'instagram' as const,
    category: 'Fashion',
    discount: '$15 OFF',
    description: 'Summer collection at Urban Outfitters',
    code: 'SUMMER15',
    tags: ['Apparel', 'Season'],
    expiresIn: 48,
    addedAt: '25 min ago',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    affiliate_link: '/explore'
  },
  {
    id: '3',
    creatorName: 'Food Lover',
    creatorHandle: '@foodlover',
    platform: 'tiktok' as const,
    category: 'Food',
    discount: 'BOGO FREE',
    description: 'DoorDash orders over $30',
    code: 'FOODBOGO',
    tags: ['Delivery', 'Dining'],
    isNew: true,
    addedAt: '42 min ago',
    avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    affiliate_link: '/explore'
  },
  {
    id: '4',
    creatorName: 'Game Master',
    creatorHandle: '@gamemaster',
    platform: 'youtube' as const,
    category: 'Gaming',
    discount: '20% OFF',
    description: 'Steam game codes',
    code: 'GAME20',
    tags: ['Games', 'Digital'],
    expiresIn: 72,
    addedAt: '1 hr ago',
    avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    affiliate_link: '/explore'
  }
]; 