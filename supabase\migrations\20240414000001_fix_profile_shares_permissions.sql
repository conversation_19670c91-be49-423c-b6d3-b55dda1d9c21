-- Enable Row Level Security on profile_shares table (if not already enabled)
ALTER TABLE IF EXISTS public.profile_shares ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert into profile_shares (anonymous tracking)
CREATE POLICY "Allow anonymous inserts to profile_shares"
ON public.profile_shares
FOR INSERT
TO anon, authenticated
WITH CHECK (true);

-- Allow service_role to do everything
CREATE POLICY "Allow service role full access to profile_shares"
ON public.profile_shares
USING (true)
WITH CHECK (true);

-- Allow users to see their own shares and shares of their profile
CREATE POLICY "Allow users to see relevant profile shares"
ON public.profile_shares
FOR SELECT
TO authenticated
USING (
  (user_id = auth.uid()) OR
  (profile_id = auth.uid())
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_profile_shares_user_id ON public.profile_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_shares_profile_id ON public.profile_shares(profile_id); 