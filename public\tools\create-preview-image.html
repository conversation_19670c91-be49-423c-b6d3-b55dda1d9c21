<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CouponLink Search Preview Image Creator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f3f4f6;
        }
        
        .preview-container {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            display: flex;
            align-items: center;
            padding: 40px 50px 20px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: #10B981;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .logo-text {
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .brand-name {
            font-size: 36px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .main-content {
            padding: 0 50px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 400px;
        }
        
        .left-content {
            flex: 1;
            max-width: 600px;
        }
        
        .headline {
            font-size: 64px;
            font-weight: bold;
            color: #1f2937;
            line-height: 1.1;
            margin-bottom: 20px;
        }
        
        .highlight {
            color: #10B981;
        }
        
        .subtext {
            font-size: 24px;
            color: #6b7280;
            margin-bottom: 30px;
            line-height: 1.4;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 20px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            display: inline-flex;
            align-items: center;
        }
        
        .arrow {
            margin-left: 8px;
            font-size: 18px;
        }
        
        .right-content {
            flex: 0 0 400px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .live-feed {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            width: 320px;
        }
        
        .feed-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .live-indicator {
            background: #ef4444;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .feed-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .creator-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .creator-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .creator-avatar {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .creator-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .creator-platform {
            font-size: 12px;
            color: #6b7280;
            margin-left: 4px;
        }
        
        .deal-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }
        
        .deal-text {
            font-size: 14px;
            font-weight: 600;
            color: #92400e;
        }
        
        .platform-tag {
            background: #dc2626;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .copy-btn {
            background: #10B981;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            border: none;
            flex: 1;
        }
        
        .visit-btn {
            background: white;
            color: #374151;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            flex: 1;
        }
        
        .instructions {
            max-width: 1200px;
            margin: 30px auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .instructions h2 {
            color: #1f2937;
            margin-bottom: 20px;
        }
        
        .instructions ol {
            color: #4b5563;
            line-height: 1.6;
        }
        
        .instructions li {
            margin-bottom: 10px;
        }
        
        .code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="preview-container" id="preview">
        <div class="header">
            <div class="logo">
                <div class="logo-text">CL</div>
            </div>
            <div class="brand-name">CouponLink</div>
        </div>
        
        <div class="main-content">
            <div class="left-content">
                <h1 class="headline">
                    All your <span class="highlight">coupons</span> in<br>
                    one place
                </h1>
                <p class="subtext">
                    Instantly discover, save, and use the best creator<br>
                    deals. Minimal effort. Maximum savings.
                </p>
                <button class="cta-button">
                    Browse Deals <span class="arrow">→</span>
                </button>
            </div>
            
            <div class="right-content">
                <div class="live-feed">
                    <div class="feed-header">
                        <div class="live-indicator">● Live Feed</div>
                        <div class="feed-title">Latest Deals</div>
                    </div>
                    
                    <div class="creator-card">
                        <div class="creator-info">
                            <div class="creator-avatar"></div>
                            <div>
                                <div class="creator-name">@gamemaster</div>
                                <div class="creator-platform">Added 1 hr ago</div>
                            </div>
                        </div>
                        
                        <div class="deal-info">
                            <div class="deal-text">💰 20% OFF Steam game codes</div>
                        </div>
                        
                        <div style="margin-bottom: 8px;">
                            <span class="platform-tag">🎮 Platform: YouTube</span>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="copy-btn">📋 Copy Code</button>
                            <button class="visit-btn">🔗 Visit Deal</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="instructions">
        <h2>📸 How to Create Your Search Preview Image</h2>
        <ol>
            <li>Right-click on the preview image above</li>
            <li>Select "Save image as..." or "Copy image"</li>
            <li>If copying, paste into an image editor and save as PNG</li>
            <li>Save the file as <span class="code">couponlink-search-preview.png</span></li>
            <li>Upload to <span class="code">public/images/couponlink-search-preview.png</span></li>
            <li>Deploy your website</li>
            <li>Test with Facebook Debugger or Twitter Card Validator</li>
        </ol>
        
        <p><strong>Alternative:</strong> Take a screenshot of this preview at exactly 1200x630 pixels using a screenshot tool.</p>
        
        <p><strong>Pro Tip:</strong> You can customize the colors, text, and layout by editing this HTML file to match your exact branding!</p>
    </div>
</body>
</html>
