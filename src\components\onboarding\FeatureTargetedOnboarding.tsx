import React, { useEffect, useState } from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Button } from '@/components/ui/button';
import { ChevronRight, X, ArrowDown, ArrowLeft, ArrowRight, ArrowUp } from 'lucide-react';

interface FeatureTargetedOnboardingProps {
  targetSelector?: string; // CSS selector for the target element
  position?: 'top' | 'bottom' | 'left' | 'right';
  offset?: number;
}

const FeatureTargetedOnboarding = ({ 
  targetSelector, 
  position = 'bottom', 
  offset = 10 
}: FeatureTargetedOnboardingProps) => {
  const { currentStep, skipStep, getCurrentMessage } = useOnboarding();
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);

  // Define which steps should show targeted tooltips and their selectors
  const stepTargets = {
    'profile_setup': {
      selector: '[data-onboarding="profile-form"]',
      position: 'right' as const,
      title: 'Complete Your Profile 👤',
      description: 'Fill out your profile information to help your audience know you better'
    },
    'social_links': {
      selector: '[data-onboarding="social-links"]',
      position: 'left' as const,
      title: 'Add Social Media Links 📱',
      description: 'Connect your social platforms to increase your reach and maximize earnings'
    },
    'payment_setup': {
      selector: '[data-onboarding="payment-section"]',
      position: 'left' as const,
      title: 'Set Up Payment Info 💳',
      description: 'Add your payment details to receive earnings from your coupon shares'
    },
    'first_coupon': {
      selector: '[data-onboarding="create-coupon-btn"]',
      position: 'bottom' as const,
      title: 'Create Your First Coupon 🎫',
      description: 'Start earning by creating and sharing your first coupon with your audience'
    }
  };

  const currentStepConfig = stepTargets[currentStep];

  useEffect(() => {
    if (!currentStepConfig) {
      setShowTooltip(false);
      return;
    }

    const findAndPositionTooltip = () => {
      const element = document.querySelector(currentStepConfig.selector) as HTMLElement;
      if (element) {
        setTargetElement(element);
        const rect = element.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        let top = 0;
        let left = 0;

        switch (currentStepConfig.position) {
          case 'top':
            top = rect.top + scrollTop - offset - 120; // 120px for tooltip height
            left = rect.left + scrollLeft + rect.width / 2 - 150; // 300px tooltip width / 2
            break;
          case 'bottom':
            top = rect.bottom + scrollTop + offset;
            left = rect.left + scrollLeft + rect.width / 2 - 150;
            break;
          case 'left':
            top = rect.top + scrollTop + rect.height / 2 - 60; // 120px tooltip height / 2
            left = rect.left + scrollLeft - offset - 300; // 300px tooltip width
            break;
          case 'right':
            top = rect.top + scrollTop + rect.height / 2 - 60;
            left = rect.right + scrollLeft + offset;
            break;
        }

        // Ensure tooltip stays within viewport
        const maxLeft = window.innerWidth - 320; // 300px width + 20px margin
        const maxTop = window.innerHeight - 140; // 120px height + 20px margin
        
        left = Math.max(20, Math.min(left, maxLeft));
        top = Math.max(20, Math.min(top, maxTop));

        setTooltipPosition({ top, left });
        setShowTooltip(true);

        // Highlight the target element
        element.style.position = 'relative';
        element.style.zIndex = '50';
        element.style.boxShadow = '0 0 0 4px rgba(59, 130, 246, 0.5)';
        element.style.borderRadius = '8px';
      }
    };

    // Wait a bit for the page to render
    const timer = setTimeout(findAndPositionTooltip, 500);

    return () => {
      clearTimeout(timer);
      // Remove highlight from target element
      if (targetElement) {
        targetElement.style.position = '';
        targetElement.style.zIndex = '';
        targetElement.style.boxShadow = '';
        targetElement.style.borderRadius = '';
      }
    };
  }, [currentStep, currentStepConfig, offset, targetElement]);

  const handleNext = async () => {
    try {
      await skipStep();
      setShowTooltip(false);
    } catch (error) {
      console.error('Failed to proceed to next step:', error);
    }
  };

  const handleSkip = async () => {
    try {
      await skipStep();
      setShowTooltip(false);
    } catch (error) {
      console.error('Failed to skip step:', error);
    }
  };

  if (!showTooltip || !currentStepConfig) {
    return null;
  }

  const getArrowIcon = () => {
    switch (currentStepConfig.position) {
      case 'top':
        return <ArrowDown className="h-4 w-4" />;
      case 'bottom':
        return <ArrowUp className="h-4 w-4" />;
      case 'left':
        return <ArrowRight className="h-4 w-4" />;
      case 'right':
        return <ArrowLeft className="h-4 w-4" />;
    }
  };

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/30 z-[60] pointer-events-none" />
      
      {/* Tooltip */}
      <div
        className="fixed z-[61] w-80 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden"
        style={{
          top: `${tooltipPosition.top}px`,
          left: `${tooltipPosition.left}px`,
        }}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getArrowIcon()}
              <h3 className="text-lg font-bold">{currentStepConfig.title}</h3>
            </div>
            <button
              onClick={handleSkip}
              className="text-white/80 hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <p className="text-gray-700 mb-4 leading-relaxed">
            {currentStepConfig.description}
          </p>

          {/* Action buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleNext}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
            >
              Got it!
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
            <Button
              onClick={handleSkip}
              variant="outline"
              className="px-6"
            >
              Skip
            </Button>
          </div>
        </div>

        {/* Arrow pointing to target */}
        <div
          className={`absolute w-0 h-0 ${
            currentStepConfig.position === 'top'
              ? 'bottom-full left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white'
              : currentStepConfig.position === 'bottom'
              ? 'top-full left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white'
              : currentStepConfig.position === 'left'
              ? 'right-full top-1/2 transform -translate-y-1/2 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white'
              : 'left-full top-1/2 transform -translate-y-1/2 border-t-8 border-b-8 border-l-8 border-t-transparent border-b-transparent border-l-white'
          }`}
        />
      </div>
    </>
  );
};

export default FeatureTargetedOnboarding;
