import { serve } from 'std/http/server.ts'
import { createClient } from 'supabase'
import Stripe from 'stripe'

// Initialize Stripe with your secret key
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') as string, {
  apiVersion: '2022-11-15',
})

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
const supabase = createClient(supabaseUrl, supabaseKey)

serve(async (req) => {
  try {
    // CORS headers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    }

    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
      return new Response('ok', { headers })
    }

    // Get request data
    const { couponId, price, title, successUrl, cancelUrl } = await req.json()
    
    // Get user information from JWT
    const authHeader = req.headers.get('Authorization')
    let userId = null
    
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      const { data: { user }, error: userError } = await supabase.auth.getUser(token)
      
      if (user && !userError) {
        userId = user.id
      }
    }
    
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Authentication required to purchase premium coupons' }),
        { headers, status: 401 }
      )
    }

    // Verify the coupon exists and is premium
    const { data: coupon, error: couponError } = await supabase
      .from('coupons')
      .select('id, title, is_premium, price, influencer_id')
      .eq('id', couponId)
      .eq('is_premium', true)
      .single()

    if (couponError || !coupon) {
      return new Response(
        JSON.stringify({ error: 'Coupon not found or not a premium coupon' }),
        { headers, status: 400 }
      )
    }
    
    // Check if user has already purchased this coupon
    const { data: existingPurchase, error: purchaseError } = await supabase
      .from('premium_purchases')
      .select('id')
      .eq('buyer_id', userId)
      .eq('coupon_id', couponId)
      .maybeSingle()
      
    if (existingPurchase) {
      return new Response(
        JSON.stringify({ alreadyPurchased: true }),
        { headers, status: 200 }
      )
    }

    // Get information about the influencer for the description
    const { data: influencer } = await supabase
      .from('profiles')
      .select('full_name, username')
      .eq('id', coupon.influencer_id)
      .single()

    // Create a Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: title || coupon.title,
              description: `Premium coupon by ${influencer?.full_name || influencer?.username || 'an influencer'}`,
            },
            unit_amount: Math.round(parseFloat(price || coupon.price) * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${successUrl}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl,
      metadata: {
        couponId,
        userId
      },
    })

    // Return the checkout URL to the client
    return new Response(
      JSON.stringify({ checkoutUrl: session.url }),
      { headers, status: 200 }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400 }
    )
  }
}) 