<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Multi-Size Favicon Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      padding: 20px;
    }
    h1 {
      text-align: center;
      color: #0095F6;
    }
    .favicon-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .favicon-preview {
      background-color: #0095F6;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .favicon-preview:after {
      content: attr(data-size);
      position: absolute;
      bottom: -20px;
      font-size: 12px;
      color: #666;
    }
    .favicon-preview span {
      color: white;
      font-weight: bold;
    }
    .size16 {
      width: 16px;
      height: 16px;
      font-size: 10px;
    }
    .size32 {
      width: 32px;
      height: 32px;
      font-size: 18px;
    }
    .size48 {
      width: 48px;
      height: 48px;
      font-size: 24px;
    }
    .size64 {
      width: 64px;
      height: 64px;
      font-size: 32px;
    }
    .btn {
      display: block;
      background-color: #0095F6;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      margin: 20px auto;
      text-align: center;
      max-width: 300px;
    }
    .steps {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
    }
    code {
      background-color: #eeeeee;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <div class="card">
    <h1>Windows Favicon Fix</h1>
    <p>Windows needs a multi-size favicon.ico file to display properly in different contexts (browser tabs, taskbar, start menu, etc.). The current favicon is too small.</p>
    
    <h2>The Problem</h2>
    <p>A proper favicon.ico for Windows should contain multiple sizes in a single file:</p>
    
    <div class="favicon-grid">
      <div class="favicon-preview size16" data-size="16×16">
        <span>P</span>
      </div>
      <div class="favicon-preview size32" data-size="32×32">
        <span>P</span>
      </div>
      <div class="favicon-preview size48" data-size="48×48">
        <span>P</span>
      </div>
      <div class="favicon-preview size64" data-size="64×64">
        <span>P</span>
      </div>
    </div>
    
    <div class="steps">
      <h3>Solution: Online ICO Generator</h3>
      <p>Since we can't generate ICO files directly in the browser, follow these steps:</p>
      <ol>
        <li>Visit <a href="https://convertico.com/favicon-generator/" target="_blank">ConvertICO Favicon Generator</a> or <a href="https://www.favicon-generator.org/" target="_blank">Favicon & App Icon Generator</a></li>
        <li>Upload your logo image (PNG or JPG)</li>
        <li>Create an ICO file with multiple sizes (16×16, 32×32, 48×48, and 64×64)</li>
        <li>Download the generated favicon.ico file</li>
        <li>Replace the existing favicon.ico in your public folder</li>
      </ol>
    </div>
    
    <h3>Quick Fix (One Size Only)</h3>
    <p>If you need a quick solution, you can use our <a href="/windows-favicon-fix.html">Simple Favicon Generator</a> to create a single-size larger favicon.</p>
    
    <div class="steps">
      <h3>Testing Your Favicon</h3>
      <p>After replacing your favicon.ico file:</p>
      <ol>
        <li>Clear your browser cache (Ctrl+Shift+Delete)</li>
        <li>Restart your browser</li>
        <li>Visit your website to see if the favicon displays correctly</li>
        <li>Check different contexts: browser tabs, bookmarks, taskbar</li>
      </ol>
    </div>
  </div>
  
  <a href="/" class="btn">Return to Site</a>
</body>
</html> 