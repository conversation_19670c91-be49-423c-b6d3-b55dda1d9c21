/// <reference types="https://deno.land/x/deno_types/index.d.ts" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const supabaseUrl = "https://oewgwxxajssonxavydbx.supabase.co";
// Use a safer way to access environment variables that works in both Deno and other environments
const getEnv = (key: string) => {
  // @ts-ignore - Deno exists in edge functions environment
  return typeof Deno !== 'undefined' ? Deno.env.get(key) : process.env[key];
};
const supabaseKey = getEnv("SUPABASE_ANON_KEY") || "";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Simple health check endpoint
    if (req.url.endsWith('/health')) {
      return new Response(
        JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Create a Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get any auth header if available
    const authHeader = req.headers.get("Authorization");
    let userId = null;
    
    if (authHeader) {
      // If authenticated, get the user ID
      const { data, error } = await supabase.auth.getUser(authHeader.replace("Bearer ", ""));
      if (!error && data?.user) {
        userId = data.user.id;
      }
    }
    
    console.log("Starting to parse request body");
    let reqBody;
    try {
      // Parse the request body - ONLY ONCE
      reqBody = await req.json();
      console.log("Request body parsed:", JSON.stringify(reqBody));
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      return new Response(
        JSON.stringify({ success: false, error: "Invalid JSON in request body" }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400
        }
      );
    }
    
    // Check for debug mode
    const isDebugMode = reqBody.debug === true;
    
    // Extract data from request body
    const { profile_id, platform, sharer_id } = reqBody;
    
    if (!profile_id) {
      console.error("Missing required field profile_id:", { body: reqBody });
      throw new Error("Missing required field: profile_id");
    }
    
    // Get client info
    const userAgent = req.headers.get("user-agent") || "";
    const sharerIp = req.headers.get("x-forwarded-for") || "";
    
    // In debug mode, just return what would be inserted without actually inserting
    if (isDebugMode) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          debug: true,
          operation: "profile_share",
          payload: {
            profile_id: profile_id,
            user_id: sharer_id || userId,
            sharer_ip: sharerIp,
            user_agent: userAgent,
            platform: platform || null,
            created_at: new Date().toISOString()
          }
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }
    
    console.log("Recording profile share for profile_id:", profile_id);
    // Record profile share
    const { error } = await supabase
      .from("profile_shares")
      .insert({
        profile_id: profile_id,
        user_id: sharer_id || userId,
        sharer_ip: sharerIp,
        user_agent: userAgent,
        platform: platform || null
      });
      
    if (error) {
      console.error("Error recording profile share:", error);
      // Just log the error but don't throw it - to avoid breaking the client
      // return a success response to the client anyway
    } else {
      console.log("Profile share recorded successfully");
    }
    
    // Always return success to the client
    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error tracking share:", error.message, error.stack);
    
    // Return a success response anyway to avoid breaking the client
    return new Response(
      JSON.stringify({ 
        success: true,
        error_logged: true,
        message: "Error logged but continuing normally"
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200 // Return 200 OK even on error
      }
    );
  }
}); 