/**
 * Comprehensive keyword database for coupon and deal-related searches
 * Includes trending terms, seasonal keywords, and influencer-related searches
 */

export const COUPON_KEYWORDS = {
  // Primary coupon terms (most searched)
  primary: [
    'coupon codes',
    'promo codes',
    'discount codes',
    'coupon deals',
    'promotional codes',
    'discount coupons',
    'promo deals',
    'coupon offers',
    'discount offers',
    'promotional offers',
    'voucher codes',
    'deal codes',
    'savings codes',
    'offer codes',
    'reduction codes'
  ],

  // Secondary coupon terms
  secondary: [
    'verified coupons',
    'working coupons',
    'active coupons',
    'valid coupons',
    'tested coupons',
    'latest coupons',
    'new coupons',
    'fresh coupons',
    'updated coupons',
    'current coupons',
    'today coupons',
    'exclusive coupons',
    'special offers',
    'limited offers',
    'flash deals'
  ],

  // Intent-based keywords
  intent: [
    'save money',
    'get discount',
    'find deals',
    'best deals',
    'cheap deals',
    'bargain deals',
    'clearance deals',
    'sale codes',
    'markdown codes',
    'price drop',
    'lowest price',
    'best price',
    'price match',
    'cashback deals',
    'rebate offers'
  ],

  // Seasonal and trending
  seasonal: [
    'black friday coupons',
    'cyber monday deals',
    'holiday coupons',
    'christmas deals',
    'new year offers',
    'valentine deals',
    'easter coupons',
    'summer sale codes',
    'back to school deals',
    'thanksgiving offers',
    'memorial day sales',
    'labor day coupons',
    'independence day deals',
    'mothers day coupons',
    'fathers day deals'
  ],

  // Shopping categories
  categories: [
    'fashion coupons',
    'electronics deals',
    'beauty coupons',
    'home deals',
    'travel coupons',
    'food deals',
    'restaurant coupons',
    'grocery deals',
    'clothing coupons',
    'shoes deals',
    'tech coupons',
    'gadget deals',
    'furniture coupons',
    'appliance deals',
    'jewelry coupons'
  ],

  // Influencer and social media related
  influencer: [
    'link in bio',
    'linkinbio',
    'bio link',
    'social media deals',
    'influencer codes',
    'creator codes',
    'exclusive codes',
    'follower discount',
    'social discount',
    'instagram deals',
    'tiktok coupons',
    'youtube codes',
    'twitter deals',
    'facebook offers',
    'social media coupons'
  ],

  // Brand-specific modifiers
  brandModifiers: [
    'official',
    'authentic',
    'genuine',
    'verified',
    'working',
    'active',
    'valid',
    'current',
    'latest',
    'new',
    'exclusive',
    'special',
    'limited',
    'premium',
    'vip'
  ],

  // Action keywords
  actions: [
    'shop now',
    'buy now',
    'get deal',
    'claim offer',
    'redeem code',
    'apply coupon',
    'use code',
    'activate deal',
    'grab offer',
    'secure discount',
    'unlock savings',
    'access deal',
    'download coupon',
    'print coupon',
    'mobile coupon'
  ],

  // Long-tail keywords
  longTail: [
    'how to save money shopping online',
    'best coupon websites',
    'where to find discount codes',
    'free shipping codes',
    'student discount codes',
    'senior citizen discounts',
    'military discount codes',
    'teacher discount codes',
    'healthcare worker discounts',
    'first time buyer discount',
    'bulk order discounts',
    'loyalty program benefits',
    'membership exclusive deals',
    'app only deals',
    'mobile exclusive offers'
  ]
};

export const POPULAR_BRANDS = [
  'amazon', 'walmart', 'target', 'best buy', 'macys', 'kohls', 'jcpenney',
  'nike', 'adidas', 'under armour', 'lululemon', 'gap', 'old navy',
  'sephora', 'ulta', 'bath and body works', 'bed bath beyond',
  'home depot', 'lowes', 'wayfair', 'ikea', 'pottery barn',
  'starbucks', 'mcdonalds', 'dominos', 'pizza hut', 'subway',
  'apple', 'samsung', 'microsoft', 'dell', 'hp', 'lenovo',
  'booking.com', 'expedia', 'hotels.com', 'airbnb', 'uber', 'lyft'
];

export const TRENDING_SEARCHES = [
  'free shipping coupon',
  'buy one get one free',
  'percentage off deals',
  'dollar off coupons',
  'clearance sale codes',
  'end of season deals',
  'flash sale coupons',
  'limited time offers',
  'exclusive member deals',
  'app download discounts'
];

/**
 * Generate comprehensive keywords for a specific page type
 */
export const generateKeywords = (
  pageType: string,
  brandName?: string,
  category?: string
): string => {
  const keywords: string[] = [];

  // Add primary coupon keywords
  keywords.push(...COUPON_KEYWORDS.primary);
  keywords.push(...COUPON_KEYWORDS.secondary);
  keywords.push(...COUPON_KEYWORDS.intent);

  // Add seasonal keywords
  keywords.push(...COUPON_KEYWORDS.seasonal);

  // Add influencer keywords
  keywords.push(...COUPON_KEYWORDS.influencer);

  // Add trending searches
  keywords.push(...TRENDING_SEARCHES);

  // Add brand-specific keywords if brand is provided
  if (brandName) {
    const brandKeywords = COUPON_KEYWORDS.brandModifiers.map(
      modifier => `${modifier} ${brandName.toLowerCase()} coupons`
    );
    keywords.push(...brandKeywords);
    
    // Add brand + action combinations
    const brandActions = COUPON_KEYWORDS.actions.map(
      action => `${action} ${brandName.toLowerCase()}`
    );
    keywords.push(...brandActions);
  }

  // Add category-specific keywords if category is provided
  if (category) {
    keywords.push(`${category.toLowerCase()} coupons`);
    keywords.push(`${category.toLowerCase()} deals`);
    keywords.push(`${category.toLowerCase()} discounts`);
    keywords.push(`best ${category.toLowerCase()} offers`);
  }

  // Add page-type specific keywords
  switch (pageType) {
    case 'homepage':
      keywords.push(...COUPON_KEYWORDS.categories);
      keywords.push('best coupon site', 'top deals website', 'savings platform');
      break;
    case 'brand':
      keywords.push('brand coupons', 'store deals', 'retailer discounts');
      break;
    case 'category':
      keywords.push('category deals', 'product discounts', 'shopping savings');
      break;
    case 'deals':
      keywords.push('hot deals', 'trending offers', 'popular discounts');
      break;
  }

  // Add long-tail keywords
  keywords.push(...COUPON_KEYWORDS.longTail);

  // Remove duplicates and return as comma-separated string
  return [...new Set(keywords)].join(', ');
};
