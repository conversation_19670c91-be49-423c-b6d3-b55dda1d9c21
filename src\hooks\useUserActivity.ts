import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useUserActivity = (userId?: string) => {
  // Don't attempt to fetch if no userId is provided
  const enabled = !!userId;

  return useQuery({
    queryKey: ['userActivity', userId],
    queryFn: async () => {
      if (!userId) {
        return {
          savedCoupons: 0,
          usedCoupons: 0,
          totalSavings: 0
        };
      }

      try {
        // Get count of saved coupons
        const { count: savedCount, error: savedError } = await supabase
          .from('saved_coupons')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId);

        if (savedError) {
          console.error('Error fetching saved coupons count:', savedError);
        }

        // Get count of used coupons and total savings
        const { data: usedCoupons, error: usedError } = await supabase
          .from('coupon_usage')
          .select(`
            id,
            savings_amount
          `)
          .eq('user_id', userId);

        if (usedError) {
          console.error('Error fetching used coupons:', usedError);
        }

        // Calculate total savings
        const totalSavings = usedCoupons?.reduce((total, usage) => {
          return total + (usage.savings_amount || 0);
        }, 0) || 0;

        return {
          savedCoupons: savedCount || 0,
          usedCoupons: usedCoupons?.length || 0,
          totalSavings
        };
      } catch (error) {
        console.error('Error in useUserActivity hook:', error);
        return {
          savedCoupons: 0,
          usedCoupons: 0,
          totalSavings: 0
        };
      }
    },
    enabled,
    staleTime: 1000 * 60 * 5 // 5 minutes
  });
}; 