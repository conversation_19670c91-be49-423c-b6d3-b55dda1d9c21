import React from 'react';
import { HelpCircle, Book, Mail, Phone, MessageSquare, Youtube, FileText, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import MainLayout from '@/components/layout/MainLayout';
import { useNavigate } from 'react-router-dom';
import { COLORS } from '@/constants/theme';
import { motion } from 'framer-motion';
import PageContainer from '@/components/layout/PageContainer';
import PageHeaderWithBackButton from '@/components/layout/PageHeaderWithBackButton';

const HelpCenter = () => {
  const navigate = useNavigate();

  return (
    <MainLayout>
      <PageContainer decorationType="default" decorationOpacity={0.6}>
        <PageHeaderWithBackButton
          title="Help Center"
          subtitle="Support & Resources"
          icon={HelpCircle}
        />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {/* Support channels */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                  style={{ background: COLORS.primary.bgLight }}>
                  <Book className="h-6 w-6" style={{ color: COLORS.primary.main }} />
                </div>
                <h2 className="text-lg font-semibold mb-2" style={{ color: COLORS.neutral[800] }}>Knowledge Base</h2>
                <p className="text-sm mb-4" style={{ color: COLORS.neutral[600] }}>Browse our comprehensive guides and documentation</p>
                <Button className="mt-auto shadow-sm"
                  style={{ background: COLORS.primary.gradient, color: "white" }}>
                  Browse Articles
                </Button>
              </div>
            </div>

            <div className="rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                  style={{ background: COLORS.secondary.bgLight }}>
                  <MessageSquare className="h-6 w-6" style={{ color: COLORS.secondary.main }} />
                </div>
                <h2 className="text-lg font-semibold mb-2" style={{ color: COLORS.neutral[800] }}>Live Chat</h2>
                <p className="text-sm mb-4" style={{ color: COLORS.neutral[600] }}>Chat with our support team during business hours</p>
                <Button className="mt-auto shadow-sm"
                  style={{ background: COLORS.secondary.gradient, color: "white" }}>
                  Start Chat
                </Button>
              </div>
            </div>

            <div className="rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.tertiary.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                  style={{ background: COLORS.tertiary.bgLight }}>
                  <Mail className="h-6 w-6" style={{ color: COLORS.tertiary.main }} />
                </div>
                <h2 className="text-lg font-semibold mb-2" style={{ color: COLORS.neutral[800] }}>Email Support</h2>
                <p className="text-sm mb-4" style={{ color: COLORS.neutral[600] }}>Send us an email and we'll respond within 24 hours</p>
                <Button className="mt-auto shadow-sm"
                  style={{ background: COLORS.tertiary.gradient, color: "white" }}>
                  Contact Us
                </Button>
              </div>
            </div>
          </div>

          {/* FAQs */}
          <div className="rounded-xl p-6 shadow-md mb-8" 
            style={{ 
              background: `linear-gradient(135deg, ${COLORS.surface.lightTransparent}, ${COLORS.surface.light})`,
              border: `1px solid rgba(255, 255, 255, 0.2)`
            }}>
            <h2 className="text-xl font-bold mb-6 flex items-center" style={{ color: COLORS.neutral[800] }}>
              <span className="w-8 h-8 rounded-full flex items-center justify-center mr-2" 
                style={{ background: COLORS.accent.bgLight }}>
                <HelpCircle className="h-4 w-4" style={{ color: COLORS.accent.main }} />
              </span>
              Frequently Asked Questions
            </h2>
            
            <Accordion type="single" collapsible className="w-full space-y-2">
              <AccordionItem value="item-1" className="border-0 bg-white/60 rounded-lg shadow-sm mb-2">
                <AccordionTrigger className="px-4 py-3 text-base font-medium hover:no-underline" 
                  style={{ color: COLORS.neutral[800] }}>
                  How do I create a coupon?
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-3 text-sm" style={{ color: COLORS.neutral[600] }}>
                  To create a coupon, navigate to the "Coupons" section in the start menu and click on "Create Coupon". 
                  Fill in the required details like coupon code, description, expiry date, and any discount information.
                  Once completed, click "Save" to publish your coupon.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-2" className="border-0 bg-white/60 rounded-lg shadow-sm mb-2">
                <AccordionTrigger className="px-4 py-3 text-base font-medium hover:no-underline" 
                  style={{ color: COLORS.neutral[800] }}>
                  How do I share my profile with followers?
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-3 text-sm" style={{ color: COLORS.neutral[600] }}>
                  You can share your profile by clicking on "Share Profile" in the start menu. This will generate a unique 
                  link to your profile that you can share on social media or through direct messages.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-3" className="border-0 bg-white/60 rounded-lg shadow-sm mb-2">
                <AccordionTrigger className="px-4 py-3 text-base font-medium hover:no-underline" 
                  style={{ color: COLORS.neutral[800] }}>
                  Can I track how many people use my coupons?
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-3 text-sm" style={{ color: COLORS.neutral[600] }}>
                  Yes! Visit the Analytics Dashboard to see detailed statistics on how many people view and use your coupons.
                  You can access this by clicking on "Dashboard" in the start menu.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-4" className="border-0 bg-white/60 rounded-lg shadow-sm mb-2">
                <AccordionTrigger className="px-4 py-3 text-base font-medium hover:no-underline" 
                  style={{ color: COLORS.neutral[800] }}>
                  How do I change my account settings?
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-3 text-sm" style={{ color: COLORS.neutral[600] }}>
                  To change your account settings, click on "Settings" in the start menu. From there, you can update your 
                  profile information, change your password, and manage notification preferences.
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-5" className="border-0 bg-white/60 rounded-lg shadow-sm mb-2">
                <AccordionTrigger className="px-4 py-3 text-base font-medium hover:no-underline" 
                  style={{ color: COLORS.neutral[800] }}>
                  Is there a mobile app available?
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-3 text-sm" style={{ color: COLORS.neutral[600] }}>
                  Currently, we offer a responsive web application that works well on mobile browsers. A dedicated mobile 
                  app is in development and will be available soon on iOS and Android platforms.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          {/* Resources */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <div className="rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.accent.bgLight}, ${COLORS.surface.lightTransparent})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                  style={{ background: COLORS.accent.bgLight }}>
                  <Youtube className="h-5 w-5" style={{ color: COLORS.accent.main }} />
                </div>
                <h2 className="text-lg font-semibold" style={{ color: COLORS.neutral[800] }}>Video Tutorials</h2>
              </div>
              <p className="text-sm mb-4" style={{ color: COLORS.neutral[600] }}>Learn how to use our platform with step-by-step video guides</p>
              <Button className="w-full shadow-sm"
                style={{ background: COLORS.accent.gradient, color: "white" }}>
                Watch Tutorials
              </Button>
            </div>

            <div className="rounded-xl p-6 shadow-md" 
              style={{ 
                background: `linear-gradient(135deg, ${COLORS.primary.bgLight}, ${COLORS.tertiary.bgLight})`,
                border: `1px solid rgba(255, 255, 255, 0.2)`
              }}>
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                  style={{ background: COLORS.primary.bgLight }}>
                  <FileText className="h-5 w-5" style={{ color: COLORS.primary.main }} />
                </div>
                <h2 className="text-lg font-semibold" style={{ color: COLORS.neutral[800] }}>User Guide</h2>
              </div>
              <p className="text-sm mb-4" style={{ color: COLORS.neutral[600] }}>Download our comprehensive user guide in PDF format</p>
              <Button className="w-full shadow-sm"
                style={{ background: `linear-gradient(to right, ${COLORS.primary.main}, ${COLORS.tertiary.main})`, color: "white" }}>
                Download Guide
              </Button>
            </div>
          </div>

          {/* Contact section */}
          <div className="rounded-xl p-6 shadow-md text-center" 
            style={{ 
              background: `linear-gradient(135deg, ${COLORS.secondary.bgLight}, ${COLORS.primary.bgLight})`,
              border: `1px solid rgba(255, 255, 255, 0.2)`
            }}>
            <h2 className="text-xl font-semibold mb-2" style={{ color: COLORS.neutral[800] }}>Need more help?</h2>
            <p className="text-sm mb-4" style={{ color: COLORS.neutral[700] }}>Our support team is available Monday through Friday, 9am-6pm EST</p>
            <div className="flex flex-wrap justify-center gap-3">
              <Button className="flex items-center shadow-md"
                style={{ background: COLORS.primary.gradient, color: "white" }}>
                <Phone className="mr-2 h-4 w-4" />
                Call Support
              </Button>
              <Button className="flex items-center shadow-md"
                style={{ background: COLORS.secondary.gradient, color: "white" }}>
                <Mail className="mr-2 h-4 w-4" />
                Email Us
              </Button>
            </div>
          </div>
        </motion.div>
      </PageContainer>
    </MainLayout>
  );
};

export default HelpCenter; 